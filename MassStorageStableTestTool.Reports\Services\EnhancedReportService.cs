using MassStorageStableTestTool.Core.Interfaces;
using MassStorageStableTestTool.Core.Models;
using MassStorageStableTestTool.Core.Enums;
using MassStorageStableTestTool.Reports.Interfaces;
using MassStorageStableTestTool.Reports.Models;
using MassStorageStableTestTool.Reports.Generators;
using Microsoft.Extensions.Logging;
using System.Text;

namespace MassStorageStableTestTool.Reports.Services;

/// <summary>
/// 增强的报告服务实现
/// </summary>
public class EnhancedReportService : IReportService
{
    private readonly ILogger<EnhancedReportService> _logger;
    private readonly ReportGenerationConfiguration _configuration;
    private readonly Dictionary<ReportFormat, IReportGenerator> _generators;
    private readonly List<ReportHistory> _reportHistory;

    /// <summary>
    /// 报告生成完成事件
    /// </summary>
    public event EventHandler<ReportGeneratedEventArgs>? ReportGenerated;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="configuration">报告配置</param>
    public EnhancedReportService(ILogger<EnhancedReportService> logger, ReportGenerationConfiguration? configuration = null)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _configuration = configuration ?? new ReportGenerationConfiguration();
        _generators = new Dictionary<ReportFormat, IReportGenerator>();
        _reportHistory = new List<ReportHistory>();

        InitializeGenerators();
    }

    /// <summary>
    /// 生成测试报告
    /// </summary>
    /// <param name="testSuiteResult">测试套件结果</param>
    /// <param name="format">报告格式</param>
    /// <returns>报告内容</returns>
    public async Task<string> GenerateReportAsync(TestSuiteResult testSuiteResult, ReportFormat format = ReportFormat.Text)
    {
        var startTime = DateTime.Now;
        
        try
        {
            _logger.LogInformation($"开始生成 {format} 格式的报告...");

            if (!_generators.TryGetValue(format, out var generator))
            {
                throw new NotSupportedException($"不支持的报告格式: {format}");
            }

            var reportContent = await generator.GenerateReportAsync(testSuiteResult);
            
            // 验证报告内容
            var (isValid, errors) = generator.ValidateReport(reportContent);
            if (!isValid)
            {
                _logger.LogWarning($"报告验证失败: {string.Join(", ", errors)}");
            }

            var duration = DateTime.Now - startTime;
            OnReportGenerated(string.Empty, format, reportContent.Length, duration, true);

            _logger.LogInformation($"{format} 格式报告生成完成，耗时 {duration.TotalSeconds:F2} 秒");
            return reportContent;
        }
        catch (Exception ex)
        {
            var duration = DateTime.Now - startTime;
            _logger.LogError(ex, $"生成 {format} 格式报告时出错");
            OnReportGenerated(string.Empty, format, 0, duration, false, ex.Message);
            throw;
        }
    }

    /// <summary>
    /// 生成并保存报告
    /// </summary>
    /// <param name="testSuiteResult">测试套件结果</param>
    /// <param name="outputPath">输出路径</param>
    /// <param name="format">报告格式</param>
    /// <returns>保存的文件路径</returns>
    public async Task<string> GenerateAndSaveReportAsync(TestSuiteResult testSuiteResult, string? outputPath = null, ReportFormat format = ReportFormat.Text)
    {
        var reportContent = await GenerateReportAsync(testSuiteResult, format);
        
        if (string.IsNullOrEmpty(outputPath))
        {
            outputPath = GenerateDefaultFileName(testSuiteResult, format);
        }

        var success = await ExportReportAsync(reportContent, outputPath, format);
        if (success)
        {
            // 添加到历史记录
            await AddToHistoryAsync(testSuiteResult, outputPath, format, reportContent.Length);
        }

        return outputPath;
    }

    /// <summary>
    /// 导出报告到文件
    /// </summary>
    /// <param name="reportContent">报告内容</param>
    /// <param name="filePath">文件路径</param>
    /// <param name="format">报告格式</param>
    /// <returns>导出结果</returns>
    public async Task<bool> ExportReportAsync(string reportContent, string filePath, ReportFormat format)
    {
        try
        {
            // 确保目录存在
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            await File.WriteAllTextAsync(filePath, reportContent, Encoding.UTF8);
            
            _logger.LogInformation($"报告已保存到: {filePath}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"导出报告到 {filePath} 时出错");
            return false;
        }
    }

    /// <summary>
    /// 获取报告模板内容
    /// </summary>
    /// <param name="templateName">模板名称</param>
    /// <returns>模板内容字符串</returns>
    public async Task<string> GetReportTemplateAsync(string templateName)
    {
        // 这里可以实现从文件系统或数据库加载模板的逻辑
        await Task.CompletedTask;
        return string.Empty;
    }

    /// <summary>
    /// 设置报告模板内容
    /// </summary>
    /// <param name="templateName">模板名称</param>
    /// <param name="templateContent">模板内容</param>
    /// <returns>是否设置成功</returns>
    public async Task<bool> SetReportTemplateAsync(string templateName, string templateContent)
    {
        // 这里可以实现保存模板到文件系统或数据库的逻辑
        await Task.CompletedTask;
        return false;
    }

    /// <summary>
    /// 获取所有可用的报告模板名称
    /// </summary>
    /// <returns>模板名称列表</returns>
    public async Task<List<string>> GetAvailableTemplatesAsync()
    {
        var allTemplates = new List<string>();
        
        foreach (var generator in _generators.Values)
        {
            var templates = await generator.GetAvailableTemplatesAsync();
            allTemplates.AddRange(templates.Select(t => $"{generator.SupportedFormat}:{t}"));
        }
        
        return allTemplates;
    }

    /// <summary>
    /// 生成摘要报告
    /// </summary>
    /// <param name="testSuiteResult">测试套件结果</param>
    /// <returns>报告内容字符串</returns>
    public async Task<string> GenerateSummaryReportAsync(TestSuiteResult testSuiteResult)
    {
        // 创建一个简化的配置，只包含摘要信息
        var summaryConfig = new ReportGenerationConfiguration
        {
            Title = "测试摘要报告",
            IncludeDetailedLogs = false,
            IncludeErrorDetails = false,
            IncludeWarnings = false
        };

        var originalConfig = _configuration;
        try
        {
            // 临时替换配置
            var generator = new TextReportGenerator(Microsoft.Extensions.Logging.Abstractions.NullLogger<TextReportGenerator>.Instance, summaryConfig);
            return await generator.GenerateReportAsync(testSuiteResult);
        }
        finally
        {
            // 恢复原配置
        }
    }

    /// <summary>
    /// 生成详细报告
    /// </summary>
    /// <param name="testSuiteResult">测试套件结果</param>
    /// <returns>报告内容字符串</returns>
    public async Task<string> GenerateDetailedReportAsync(TestSuiteResult testSuiteResult)
    {
        return await GenerateReportAsync(testSuiteResult, ReportFormat.HTML);
    }

    /// <summary>
    /// 生成性能报告
    /// </summary>
    /// <param name="testSuiteResult">测试套件结果</param>
    /// <returns>报告内容字符串</returns>
    public async Task<string> GeneratePerformanceReportAsync(TestSuiteResult testSuiteResult)
    {
        return await GenerateReportAsync(testSuiteResult, ReportFormat.JSON);
    }

    /// <summary>
    /// 生成对比报告
    /// </summary>
    /// <param name="testResults">测试结果列表</param>
    /// <returns>报告内容字符串</returns>
    public async Task<string> GenerateComparisonReportAsync(List<TestSuiteResult> testResults)
    {
        // 这里可以实现对比报告的生成逻辑
        await Task.CompletedTask;
        return "对比报告功能尚未实现";
    }

    /// <summary>
    /// 发送报告邮件
    /// </summary>
    /// <param name="reportContent">报告内容</param>
    /// <param name="recipients">收件人列表</param>
    /// <param name="subject">邮件主题</param>
    /// <returns>是否发送成功</returns>
    public async Task<bool> SendReportEmailAsync(string reportContent, List<string> recipients, string subject)
    {
        // 这里可以实现邮件发送逻辑
        await Task.CompletedTask;
        _logger.LogWarning("邮件发送功能尚未实现");
        return false;
    }

    /// <summary>
    /// 获取报告历史记录
    /// </summary>
    /// <param name="count">获取数量</param>
    /// <returns>报告历史列表</returns>
    public async Task<List<ReportHistory>> GetReportHistoryAsync(int count = 10)
    {
        await Task.CompletedTask;
        return _reportHistory.OrderByDescending(h => h.GeneratedAt).Take(count).ToList();
    }

    /// <summary>
    /// 删除报告文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>是否删除成功</returns>
    public async Task<bool> DeleteReportAsync(string filePath)
    {
        try
        {
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
                
                // 从历史记录中移除
                _reportHistory.RemoveAll(h => h.FilePath == filePath);
                
                _logger.LogInformation($"报告文件已删除: {filePath}");
                return true;
            }
            
            await Task.CompletedTask;
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"删除报告文件 {filePath} 时出错");
            return false;
        }
    }

    /// <summary>
    /// 清理过期报告
    /// </summary>
    /// <param name="retentionDays">保留天数</param>
    /// <returns>清理数量</returns>
    public async Task<int> CleanupExpiredReportsAsync(int retentionDays = 30)
    {
        var cutoffDate = DateTime.Now.AddDays(-retentionDays);
        var expiredReports = _reportHistory.Where(h => h.GeneratedAt < cutoffDate).ToList();
        
        int deletedCount = 0;
        foreach (var report in expiredReports)
        {
            if (await DeleteReportAsync(report.FilePath))
            {
                deletedCount++;
            }
        }
        
        _logger.LogInformation($"清理了 {deletedCount} 个过期报告文件");
        return deletedCount;
    }

    /// <summary>
    /// 验证报告内容
    /// </summary>
    /// <param name="reportContent">报告内容</param>
    /// <param name="format">报告格式</param>
    /// <returns>验证结果</returns>
    public (bool IsValid, List<string> Errors) ValidateReport(string reportContent, ReportFormat format)
    {
        if (_generators.TryGetValue(format, out var generator))
        {
            return generator.ValidateReport(reportContent);
        }
        
        return (false, new List<string> { $"不支持的报告格式: {format}" });
    }

    /// <summary>
    /// 初始化报告生成器
    /// </summary>
    private void InitializeGenerators()
    {
        _generators[ReportFormat.Text] = new TextReportGenerator(Microsoft.Extensions.Logging.Abstractions.NullLogger<TextReportGenerator>.Instance, _configuration);
        _generators[ReportFormat.HTML] = new HtmlReportGenerator(Microsoft.Extensions.Logging.Abstractions.NullLogger<HtmlReportGenerator>.Instance, _configuration);
        _generators[ReportFormat.CSV] = new CsvReportGenerator(Microsoft.Extensions.Logging.Abstractions.NullLogger<CsvReportGenerator>.Instance, _configuration);
        _generators[ReportFormat.JSON] = new JsonReportGenerator(Microsoft.Extensions.Logging.Abstractions.NullLogger<JsonReportGenerator>.Instance, _configuration);
        
        _logger.LogInformation($"已初始化 {_generators.Count} 个报告生成器");
    }

    /// <summary>
    /// 生成默认文件名
    /// </summary>
    /// <param name="testSuiteResult">测试套件结果</param>
    /// <param name="format">报告格式</param>
    /// <returns>文件路径</returns>
    private string GenerateDefaultFileName(TestSuiteResult testSuiteResult, ReportFormat format)
    {
        var timestamp = DateTime.Now.ToString(_configuration.TimestampFormat);
        var driveName = testSuiteResult.Configuration.TargetDrive.Replace(":", "").Replace("\\", "");
        
        var fileName = _configuration.FileNameTemplate
            .Replace("{drive}", driveName)
            .Replace("{timestamp}", timestamp);

        var extension = _generators[format].GetDefaultFileExtension();
        var fullFileName = $"{fileName}{extension}";
        
        return Path.Combine(_configuration.OutputDirectory, fullFileName);
    }

    /// <summary>
    /// 添加到历史记录
    /// </summary>
    /// <param name="testSuiteResult">测试套件结果</param>
    /// <param name="filePath">文件路径</param>
    /// <param name="format">报告格式</param>
    /// <param name="fileSize">文件大小</param>
    private async Task AddToHistoryAsync(TestSuiteResult testSuiteResult, string filePath, ReportFormat format, long fileSize)
    {
        var history = new ReportHistory
        {
            FilePath = filePath,
            Format = format,
            GeneratedAt = DateTime.Now,
            ConfigurationName = "Default",
            TargetDrive = testSuiteResult.Configuration.TargetDrive,
            TestTools = testSuiteResult.Configuration.SelectedTools,
            Summary = $"总计 {testSuiteResult.TotalTestsCount} 个测试，成功 {testSuiteResult.SuccessfulTestsCount} 个",
            FileSize = fileSize,
            IsSuccess = testSuiteResult.AllTestsPassed
        };

        _reportHistory.Add(history);
        
        // 限制历史记录数量
        if (_reportHistory.Count > 100)
        {
            _reportHistory.RemoveAt(0);
        }
        
        await Task.CompletedTask;
    }

    /// <summary>
    /// 触发报告生成事件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="format">报告格式</param>
    /// <param name="size">文件大小</param>
    /// <param name="duration">生成耗时</param>
    /// <param name="isSuccess">是否成功</param>
    /// <param name="errorMessage">错误信息</param>
    private void OnReportGenerated(string filePath, ReportFormat format, long size, TimeSpan duration, bool isSuccess, string? errorMessage = null)
    {
        ReportGenerated?.Invoke(this, new ReportGeneratedEventArgs
        {
            FilePath = filePath,
            Format = format,
            Size = size,
            Duration = duration,
            IsSuccess = isSuccess,
            ErrorMessage = errorMessage,
            Timestamp = DateTime.Now
        });
    }
}
