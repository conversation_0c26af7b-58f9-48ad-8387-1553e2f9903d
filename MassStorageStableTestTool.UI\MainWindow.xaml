﻿<Window x:Class="MassStorageStableTestTool.UI.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:MassStorageStableTestTool.UI"
        xmlns:converters="clr-namespace:MassStorageStableTestTool.UI.Converters"
        xmlns:p="clr-namespace:MassStorageStableTestTool.UI.Resources"
        mc:Ignorable="d"
        Title="{x:Static p:Strings.WindowTitle}"
        Height="900" Width="1400"
        MinHeight="800" MinWidth="1200"
        WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- 转换器 -->
            <converters:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
            <converters:InverseBoolToVisibilityConverter x:Key="InverseBoolToVisibilityConverter"/>
            <converters:ProgressToStringConverter x:Key="ProgressToStringConverter"/>
        </ResourceDictionary>
    </Window.Resources>

    <Grid Background="{StaticResource BackgroundColor}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 菜单栏 -->
        <Menu Grid.Row="0" Background="{StaticResource SurfaceColor}" BorderBrush="{StaticResource BorderColor}" BorderThickness="0,0,0,1">
            <MenuItem Header="{x:Static p:Strings.MenuFile}">
                <MenuItem Header="{x:Static p:Strings.MenuFileNewConfig}"/>
                <MenuItem Header="{x:Static p:Strings.MenuFileOpenConfig}"/>
                <MenuItem Header="{x:Static p:Strings.MenuFileSaveConfig}"/>
                <Separator/>
                <MenuItem Header="{x:Static p:Strings.MenuFileExit}"/>
            </MenuItem>
            <MenuItem Header="{x:Static p:Strings.MenuSettings}">
                <MenuItem Header="{x:Static p:Strings.MenuSettingsTool}" Command="{Binding OpenSettingsCommand}"/>
                <MenuItem Header="{x:Static p:Strings.MenuSettingsReport}"/>
                <Separator/>
                <MenuItem Header="语言 (Language)">
                    <MenuItem Header="简体中文" Click="SwitchToChinese"/>
                    <MenuItem Header="English" Click="SwitchToEnglish"/>
                </MenuItem>
            </MenuItem>
            <MenuItem Header="{x:Static p:Strings.MenuReport}">
                <MenuItem Header="{x:Static p:Strings.MenuReportViewLatest}"/>
                <MenuItem Header="{x:Static p:Strings.MenuReportOpenFolder}"/>
            </MenuItem>
            <MenuItem Header="{x:Static p:Strings.MenuHelp}">
                <MenuItem Header="{x:Static p:Strings.MenuHelpUserManual}"/>
                <MenuItem Header="{x:Static p:Strings.MenuHelpAbout}"/>
            </MenuItem>
        </Menu>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1" Margin="16">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 上半部分：设备选择、工具选择、测试控制、进度显示 -->
            <Grid Grid.Row="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="350"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="350"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧：多设备选择和测试控制 -->
                <StackPanel Grid.Column="0">
                    <!-- 设备选择卡片 -->
                    <Border Style="{StaticResource CardStyle}">
                        <StackPanel>
                            <Grid Margin="0,0,0,12">
                                <TextBlock Text="{x:Static p:Strings.DeviceSelectionCardTitle}" Style="{StaticResource CardTitleStyle}" HorizontalAlignment="Left"/>
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                    <Button Content="{x:Static p:Strings.ButtonSelectAll}"
                                          Command="{Binding SelectAllDrivesCommand}"
                                          Style="{StaticResource SecondaryButtonStyle}"
                                          IsEnabled="{Binding CanSelectAll}"
                                          FontSize="10"
                                          Padding="8,4"
                                          Margin="0,0,4,0"/>
                                    <Button Content="{x:Static p:Strings.ButtonDeselectAll}"
                                          Command="{Binding DeselectAllDrivesCommand}"
                                          Style="{StaticResource SecondaryButtonStyle}"
                                          IsEnabled="{Binding CanDeselectAll}"
                                          FontSize="10"
                                          Padding="8,4"/>
                                </StackPanel>
                            </Grid>

                            <!-- 设备列表 -->
                            <ScrollViewer MaxHeight="155" Style="{StaticResource ModernScrollViewerStyle}">
                                <ItemsControl ItemsSource="{Binding AvailableDrives}">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Border Background="{StaticResource SurfaceColor}"
                                                  BorderBrush="{Binding SelectionStatusColor}"
                                                  BorderThickness="2"
                                                  CornerRadius="4"
                                                  Margin="0,2"
                                                  Padding="8">
                                                <Grid>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                    </Grid.RowDefinitions>

                                                    <!-- 第一行：选择框和设备名称 -->
                                                    <Grid Grid.Row="0">
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="*"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                        </Grid.ColumnDefinitions>

                                                        <CheckBox Grid.Column="0"
                                                                IsChecked="{Binding IsSelected}"
                                                                VerticalAlignment="Center"
                                                                Margin="0,0,8,0"/>

                                                        <StackPanel Grid.Column="1">
                                                            <TextBlock Text="{Binding DisplayName}"
                                                                     FontWeight="Medium"
                                                                     FontSize="12"/>
                                                            <TextBlock Text="{Binding TotalSizeFormatted}"
                                                                     FontSize="10"
                                                                     Foreground="{StaticResource TextSecondaryColor}"/>
                                                        </StackPanel>

                                                        <StackPanel Grid.Column="2" Orientation="Horizontal">
                                                            <TextBlock Text="{Binding TestStatusIcon}"
                                                                     FontFamily="Segoe UI Emoji, Segoe UI Symbol, Segoe UI"
                                                                     Foreground="{Binding TestStatusColor}"
                                                                     Margin="0,0,4,0"
                                                                     VerticalAlignment="Center"/>
                                                            <TextBlock Text="{Binding TestStatus}"
                                                                     FontSize="10"
                                                                     Foreground="{Binding TestStatusColor}"
                                                                     VerticalAlignment="Center"/>
                                                        </StackPanel>
                                                    </Grid>

                                                    <!-- 第二行：测试进度 -->
                                                    <Grid Grid.Row="1" Margin="0,4,0,0"
                                                        Visibility="{Binding IsSelected, Converter={StaticResource BoolToVisibilityConverter}}">
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="*"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                        </Grid.ColumnDefinitions>

                                                        <ProgressBar Grid.Column="0"
                                                                   Value="{Binding TestProgress}"
                                                                   Height="12"
                                                                   Background="{StaticResource BorderColor}"
                                                                   Foreground="{StaticResource PrimaryColor}"
                                                                   Margin="0,0,8,0"/>

                                                        <TextBlock Grid.Column="1"
                                                                 Text="{Binding TestProgressFormatted}"
                                                                 FontSize="10"
                                                                 VerticalAlignment="Center"/>
                                                    </Grid>

                                                    <!-- 第三行：当前测试 -->
                                                    <TextBlock Grid.Row="2"
                                                             Text="{Binding CurrentTest, StringFormat={x:Static p:Strings.LabelCurrentTest}}"
                                                             FontSize="10"
                                                             Foreground="{StaticResource TextSecondaryColor}"
                                                             Margin="0,2,0,0"
                                                             Visibility="{Binding IsSelected, Converter={StaticResource BoolToVisibilityConverter}}"/>
                                                </Grid>
                                            </Border>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </ScrollViewer>

                            <Button Content="{x:Static p:Strings.ButtonRefreshDrives}"
                                  Command="{Binding RefreshDrivesCommand}"
                                  Style="{StaticResource SecondaryButtonStyle}"
                                  HorizontalAlignment="Left"
                                  Margin="0,12,0,0"/>
                        </StackPanel>
                    </Border>

                    <!-- 测试控制卡片 -->
                    <Border Style="{StaticResource CardStyle}">
                        <StackPanel>
                            <TextBlock Text="{x:Static p:Strings.TestControlCardTitle}" Style="{StaticResource CardTitleStyle}"/>

                            <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                <Button Content="{x:Static p:Strings.ButtonStartParallelTest}"
                                      Style="{StaticResource PrimaryButtonStyle}"
                                      Command="{Binding StartTestCommand}"
                                      IsEnabled="{Binding CanStartTest}"
                                      Margin="0,0,8,0"/>
                                <Button Content="{x:Static p:Strings.ButtonStopAllTests}"
                                      Style="{StaticResource SecondaryButtonStyle}"
                                      Command="{Binding StopTestCommand}"
                                      IsEnabled="{Binding IsTestRunning}"/>
                            </StackPanel>

                            <TextBlock Text="{x:Static p:Strings.LabelTestConfiguration}" FontWeight="Medium" Margin="0,0,0,4"/>
                            <TextBlock Text="{Binding TestSummary}"
                                     TextWrapping="Wrap"
                                     FontSize="12"
                                     Foreground="{StaticResource TextSecondaryColor}" HorizontalAlignment="Left"/>

                            <!-- 并行测试说明 -->
                            <Border Background="{StaticResource PrimaryLightColor}"
                                  CornerRadius="4"
                                  Padding="8"
                                  Margin="0,8,0,0">
                                <StackPanel>
                                    <TextBlock Text="{x:Static p:Strings.LabelParallelTestInfo}"
                                             FontWeight="Medium"
                                             FontSize="11"
                                             Margin="0,0,0,4"/>
                                    <TextBlock Text="{x:Static p:Strings.TextParallelTestInfo1}"
                                             FontSize="10"
                                             TextWrapping="Wrap"/>
                                    <TextBlock Text="{x:Static p:Strings.TextParallelTestInfo2}"
                                             FontSize="10"
                                             TextWrapping="Wrap"/>
                                    <TextBlock Text="{x:Static p:Strings.TextParallelTestInfo3}"
                                             FontSize="10"
                                             TextWrapping="Wrap"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </Border>
                </StackPanel>

                <!-- 中间：测试工具选择 -->
                <Border Grid.Column="1" Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <Grid Margin="0,0,0,12">
                            <TextBlock Text="{x:Static p:Strings.ToolSelectionCardTitle}" Style="{StaticResource CardTitleStyle}" HorizontalAlignment="Left"/>
                            <Button Content="{x:Static p:Strings.ButtonAdvancedSettings}"
                                  Command="{Binding OpenSettingsCommand}"
                                  Style="{StaticResource SecondaryButtonStyle}"
                                  HorizontalAlignment="Right"/>
                        </Grid>

                        <ScrollViewer MaxHeight="400" Style="{StaticResource ModernScrollViewerStyle}">
                            <StackPanel>
                                <!-- GUI工具组 -->
                                <Expander Header="{x:Static p:Strings.HeaderGuiTools}" IsExpanded="True" Style="{StaticResource ModernExpanderStyle}">
                                    <ItemsControl ItemsSource="{Binding GuiTools}" Margin="16,8,0,0">
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal" Margin="0,2">
                                                    <CheckBox IsChecked="{Binding IsSelected}"
                                                            Content="{Binding DisplayName}"
                                                            Style="{StaticResource ModernCheckBoxStyle}"
                                                            VerticalAlignment="Center"/>
                                                    <Ellipse Width="8" Height="8"
                                                           Fill="{Binding StatusColor}"
                                                           Margin="8,0,0,0"
                                                           VerticalAlignment="Center"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </Expander>

                                <!-- CLI工具组 -->
                                <Expander Header="{x:Static p:Strings.HeaderCliTools}" IsExpanded="True" Style="{StaticResource ModernExpanderStyle}">
                                    <ItemsControl ItemsSource="{Binding CliTools}" Margin="16,8,0,0">
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal" Margin="0,2">
                                                    <CheckBox IsChecked="{Binding IsSelected}"
                                                            Content="{Binding DisplayName}"
                                                            Style="{StaticResource ModernCheckBoxStyle}"
                                                            VerticalAlignment="Center"/>
                                                    <Ellipse Width="8" Height="8"
                                                           Fill="{Binding StatusColor}"
                                                           Margin="8,0,0,0"
                                                           VerticalAlignment="Center"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </Expander>

                                <!-- 混合工具组 -->
                                <Expander Header="{x:Static p:Strings.HeaderHybridTools}" IsExpanded="False" Style="{StaticResource ModernExpanderStyle}">
                                    <ItemsControl ItemsSource="{Binding HybridTools}" Margin="16,8,0,0">
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal" Margin="0,2">
                                                    <CheckBox IsChecked="{Binding IsSelected}"
                                                            Content="{Binding DisplayName}"
                                                            Style="{StaticResource ModernCheckBoxStyle}"
                                                            VerticalAlignment="Center"/>
                                                    <Ellipse Width="8" Height="8"
                                                           Fill="{Binding StatusColor}"
                                                           Margin="8,0,0,0"
                                                           VerticalAlignment="Center"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </Expander>
                            </StackPanel>
                        </ScrollViewer>
                    </StackPanel>
                </Border>

                <!-- 右侧：并行测试进度 -->
                <Border Grid.Column="2" Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="{x:Static p:Strings.ParallelTestProgressCardTitle}" Style="{StaticResource CardTitleStyle}"/>

                        <TextBlock Text="{Binding CurrentStatus}" FontWeight="Medium" Margin="0,0,0,8"/>

                        <!-- 总体进度 -->
                        <TextBlock Text="{x:Static p:Strings.LabelOverallProgress}" Style="{StaticResource LabelStyle}"/>
                        <Grid Margin="0,0,0,12">
                            <ProgressBar Value="{Binding OverallProgress}"
                                       Style="{StaticResource ModernProgressBarStyle}"/>
                            <TextBlock Text="{Binding OverallProgress, Converter={StaticResource ProgressToStringConverter}}"
                                     HorizontalAlignment="Center"
                                     VerticalAlignment="Center"
                                     FontSize="11"
                                     FontWeight="Medium"/>
                        </Grid>

                        <!-- 设备状态统计 -->
                        <TextBlock Text="{x:Static p:Strings.LabelDeviceStatusStats}" Style="{StaticResource LabelStyle}" FontWeight="Medium"/>
                        <Grid Margin="0,0,0,12">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <StackPanel Grid.Column="0">
                                <TextBlock FontSize="10">
                                    <Run Text="{x:Static p:Strings.LabelTesting}"/>
                                    <Run Text="{Binding TestingDriveCount, Mode=OneWay}" FontWeight="Medium"/>
                                </TextBlock>
                                <TextBlock FontSize="10">
                                    <Run Text="{x:Static p:Strings.LabelCompleted}"/>
                                    <Run Text="{Binding CompletedDriveCount, Mode=OneWay}" FontWeight="Medium"/>
                                </TextBlock>
                            </StackPanel>
                            <StackPanel Grid.Column="1">
                                <TextBlock FontSize="10">
                                    <Run Text="{x:Static p:Strings.LabelWaiting}"/>
                                    <Run Text="{Binding WaitingDriveCount, Mode=OneWay}" FontWeight="Medium"/>
                                </TextBlock>
                                <TextBlock FontSize="10">
                                    <Run Text="{x:Static p:Strings.LabelFailed}"/>
                                    <Run Text="{Binding FailedDriveCount, Mode=OneWay}" FontWeight="Medium"/>
                                </TextBlock>
                            </StackPanel>
                        </Grid>

                        <!-- 选中的测试工具状态 -->
                        <TextBlock Text="{x:Static p:Strings.LabelToolStatus}" Style="{StaticResource LabelStyle}" FontWeight="Medium"/>
                        <ScrollViewer MaxHeight="150" Style="{StaticResource ModernScrollViewerStyle}">
                            <StackPanel>
                                <ItemsControl ItemsSource="{Binding GuiTools}">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal" Margin="0,2"
                                                      Visibility="{Binding IsSelected, Converter={StaticResource BoolToVisibilityConverter}}">
                                                <TextBlock Text="{Binding StatusIcon}" Margin="0,0,4,0" FontFamily="Segoe UI Emoji, Segoe UI Symbol, Segoe UI"/>
                                                <TextBlock Text="{Binding Name}" FontSize="11"/>
                                                <TextBlock Text="{Binding Progress, StringFormat= ({0:F0}%)}"
                                                         FontSize="10"
                                                         Foreground="{StaticResource TextSecondaryColor}"
                                                         Margin="4,0,0,0"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                                <ItemsControl ItemsSource="{Binding CliTools}">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal" Margin="0,2"
                                                      Visibility="{Binding IsSelected, Converter={StaticResource BoolToVisibilityConverter}}">
                                                <TextBlock Text="{Binding StatusIcon}" Margin="0,0,4,0" FontFamily="Segoe UI Emoji, Segoe UI Symbol, Segoe UI"/>
                                                <TextBlock Text="{Binding Name}" FontSize="11"/>
                                                <TextBlock Text="{Binding Progress, StringFormat= ({0:F0}%)}"
                                                         FontSize="10"
                                                         Foreground="{StaticResource TextSecondaryColor}"
                                                         Margin="4,0,0,0"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                                <ItemsControl ItemsSource="{Binding HybridTools}">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal" Margin="0,2"
                                                      Visibility="{Binding IsSelected, Converter={StaticResource BoolToVisibilityConverter}}">
                                                <TextBlock Text="{Binding StatusIcon}" Margin="0,0,4,0" FontFamily="Segoe UI Emoji, Segoe UI Symbol, Segoe UI"/>
                                                <TextBlock Text="{Binding Name}" FontSize="11"/>
                                                <TextBlock Text="{Binding Progress, StringFormat= ({0:F0}%)}"
                                                         FontSize="10"
                                                         Foreground="{StaticResource TextSecondaryColor}"
                                                         Margin="4,0,0,0"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </StackPanel>
                        </ScrollViewer>

                        <TextBlock Text="{x:Static p:Strings.TextTip}"
                                 FontSize="10"
                                 Foreground="{StaticResource TextSecondaryColor}"
                                 Margin="0,8,0,0"
                                 TextWrapping="Wrap"/>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- 下半部分：实时日志 -->
            <Border Grid.Row="1" Style="{StaticResource CardStyle}" Margin="0,16,0,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <Grid Grid.Row="0" Margin="0,0,0,8">
                        <TextBlock Text="{x:Static p:Strings.RealTimeLogCardTitle}" Style="{StaticResource CardTitleStyle}" HorizontalAlignment="Left"/>
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                            <Button Content="{x:Static p:Strings.ButtonClear}"
                                  Command="{Binding ClearLogCommand}"
                                  Style="{StaticResource SecondaryButtonStyle}"
                                  Margin="0,0,8,0"/>
                            <Button Content="{x:Static p:Strings.ButtonSave}"
                                  Command="{Binding SaveLogCommand}"
                                  Style="{StaticResource SecondaryButtonStyle}"/>
                        </StackPanel>
                    </Grid>

                    <ScrollViewer Grid.Row="1"
                                Style="{StaticResource ModernScrollViewerStyle}"
                                x:Name="LogScrollViewer">
                        <ItemsControl ItemsSource="{Binding LogEntries}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" Margin="0,1">
                                        <TextBlock Text="{Binding LevelIcon}"
                                                 Margin="0,0,4,0"
                                                 VerticalAlignment="Top"/>
                                        <TextBlock Text="{Binding FormattedMessage}"
                                                 FontFamily="Consolas"
                                                 FontSize="11"
                                                 Foreground="{Binding LevelColor}"
                                                 TextWrapping="Wrap"
                                                 MaxWidth="1200"/>
                                    </StackPanel>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </Grid>
            </Border>
        </Grid>

        <!-- 状态栏 -->
        <StatusBar Grid.Row="2" Style="{StaticResource StatusBarStyle}">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusBarText}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="{x:Static p:Strings.StatusBarCpu}"/>
                    <TextBlock Text="{Binding CpuUsage, StringFormat={}{0:F0}%}"/>
                </StackPanel>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="{x:Static p:Strings.StatusBarMemory}"/>
                    <TextBlock Text="{Binding MemoryUsage, StringFormat={}{0:F0}%}"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
