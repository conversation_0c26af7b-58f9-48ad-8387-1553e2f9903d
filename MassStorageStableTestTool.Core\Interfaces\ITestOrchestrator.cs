using MassStorageStableTestTool.Core.Models;

namespace MassStorageStableTestTool.Core.Interfaces;

/// <summary>
/// 测试编排器接口
/// </summary>
public interface ITestOrchestrator
{
    /// <summary>
    /// 执行测试套件
    /// </summary>
    /// <param name="configuration">测试配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <param name="progress">进度报告器</param>
    /// <returns>测试套件结果</returns>
    Task<TestSuiteResult> ExecuteTestSuiteAsync(
        TestConfiguration configuration,
        CancellationToken cancellationToken,
        IProgress<ProgressEventArgs>? progress = null);

    /// <summary>
    /// 获取可用的测试工具列表
    /// </summary>
    /// <returns>可用的测试工具</returns>
    Task<List<ITestToolController>> GetAvailableToolsAsync();

    /// <summary>
    /// 根据名称获取测试工具控制器
    /// </summary>
    /// <param name="toolName">工具名称</param>
    /// <returns>测试工具控制器</returns>
    ITestToolController? GetToolController(string toolName);

    /// <summary>
    /// 验证测试配置
    /// </summary>
    /// <param name="configuration">测试配置</param>
    /// <returns>验证结果</returns>
    Task<(bool IsValid, List<string> Errors)> ValidateConfigurationAsync(TestConfiguration configuration);

    /// <summary>
    /// 估算测试时间
    /// </summary>
    /// <param name="configuration">测试配置</param>
    /// <returns>估算的测试时间</returns>
    Task<TimeSpan> EstimateTestDurationAsync(TestConfiguration configuration);

    /// <summary>
    /// 停止当前运行的测试套件
    /// </summary>
    /// <returns>停止结果</returns>
    Task<bool> StopCurrentTestSuiteAsync();

    /// <summary>
    /// 获取当前测试套件状态
    /// </summary>
    /// <returns>当前状态</returns>
    TestSuiteStatus GetCurrentStatus();

    /// <summary>
    /// 暂停当前测试套件
    /// </summary>
    /// <returns>暂停结果</returns>
    Task<bool> PauseCurrentTestSuiteAsync();

    /// <summary>
    /// 恢复当前测试套件
    /// </summary>
    /// <returns>恢复结果</returns>
    Task<bool> ResumeCurrentTestSuiteAsync();

    /// <summary>
    /// 获取测试历史记录
    /// </summary>
    /// <param name="count">记录数量</param>
    /// <returns>历史记录列表</returns>
    Task<List<TestSuiteResult>> GetTestHistoryAsync(int count = 10);

    /// <summary>
    /// 清理测试环境
    /// </summary>
    /// <param name="configuration">测试配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理结果</returns>
    Task<bool> CleanupTestEnvironmentAsync(TestConfiguration configuration, CancellationToken cancellationToken);

    /// <summary>
    /// 测试套件状态变化事件
    /// </summary>
    event EventHandler<TestSuiteStatusChangedEventArgs>? StatusChanged;

    /// <summary>
    /// 测试工具状态变化事件
    /// </summary>
    event EventHandler<TestStatusChangedEventArgs>? ToolStatusChanged;

    /// <summary>
    /// 日志事件
    /// </summary>
    event EventHandler<LogEventArgs>? LogReceived;
}

/// <summary>
/// 测试套件状态枚举
/// </summary>
public enum TestSuiteStatus
{
    /// <summary>
    /// 空闲
    /// </summary>
    Idle,

    /// <summary>
    /// 准备中
    /// </summary>
    Preparing,

    /// <summary>
    /// 运行中
    /// </summary>
    Running,

    /// <summary>
    /// 暂停
    /// </summary>
    Paused,

    /// <summary>
    /// 完成
    /// </summary>
    Completed,

    /// <summary>
    /// 失败
    /// </summary>
    Failed,

    /// <summary>
    /// 已取消
    /// </summary>
    Cancelled,

    /// <summary>
    /// 清理中
    /// </summary>
    Cleaning
}

/// <summary>
/// 测试套件状态变化事件参数
/// </summary>
public class TestSuiteStatusChangedEventArgs : EventArgs
{
    /// <summary>
    /// 旧状态
    /// </summary>
    public TestSuiteStatus OldStatus { get; set; }

    /// <summary>
    /// 新状态
    /// </summary>
    public TestSuiteStatus NewStatus { get; set; }

    /// <summary>
    /// 状态消息
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// 当前测试工具
    /// </summary>
    public string? CurrentTool { get; set; }

    /// <summary>
    /// 已完成的测试数量
    /// </summary>
    public int CompletedTests { get; set; }

    /// <summary>
    /// 总测试数量
    /// </summary>
    public int TotalTests { get; set; }

    /// <summary>
    /// 总体进度百分比
    /// </summary>
    public double OverallProgress { get; set; }

    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;

    /// <summary>
    /// 额外数据
    /// </summary>
    public Dictionary<string, object> Data { get; set; } = new();
}
