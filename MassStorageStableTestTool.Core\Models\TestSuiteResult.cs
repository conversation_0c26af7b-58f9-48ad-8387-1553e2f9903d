using MassStorageStableTestTool.Core.Enums;

namespace MassStorageStableTestTool.Core.Models;

/// <summary>
/// 测试套件结果模型
/// </summary>
public class TestSuiteResult
{
    /// <summary>
    /// 测试配置
    /// </summary>
    public TestConfiguration Configuration { get; set; } = new();

    /// <summary>
    /// 测试结果列表
    /// </summary>
    public List<TestResult> TestResults { get; set; } = new();

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 总持续时间
    /// </summary>
    public TimeSpan Duration => EndTime - StartTime;

    /// <summary>
    /// 套件状态
    /// </summary>
    public TestStatus Status { get; set; } = TestStatus.NotStarted;

    /// <summary>
    /// 是否所有测试都通过
    /// </summary>
    public bool AllTestsPassed => TestResults.All(r => r.Success);

    /// <summary>
    /// 成功的测试数量
    /// </summary>
    public int SuccessfulTestsCount => TestResults.Count(r => r.Success);

    /// <summary>
    /// 失败的测试数量
    /// </summary>
    public int FailedTestsCount => TestResults.Count(r => !r.Success);

    /// <summary>
    /// 总测试数量
    /// </summary>
    public int TotalTestsCount => TestResults.Count;

    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate => TotalTestsCount > 0 ? (double)SuccessfulTestsCount / TotalTestsCount * 100 : 0;

    /// <summary>
    /// 报告文件路径
    /// </summary>
    public string? ReportFilePath { get; set; }

    /// <summary>
    /// 系统信息
    /// </summary>
    public SystemInfo? SystemInfo { get; set; }

    /// <summary>
    /// 驱动器信息
    /// </summary>
    public DriveInfo? DriveInfo { get; set; }

    /// <summary>
    /// 套件级别的错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 套件级别的警告信息
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// 套件执行日志
    /// </summary>
    public List<string> Logs { get; set; } = new();

    /// <summary>
    /// 添加测试结果
    /// </summary>
    /// <param name="testResult">测试结果</param>
    public void AddTestResult(TestResult testResult)
    {
        TestResults.Add(testResult);
        AddLog($"添加测试结果: {testResult.ToolName} - {(testResult.Success ? "成功" : "失败")}");
    }

    /// <summary>
    /// 添加日志条目
    /// </summary>
    /// <param name="message">日志消息</param>
    public void AddLog(string message)
    {
        Logs.Add($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {message}");
    }

    /// <summary>
    /// 添加警告
    /// </summary>
    /// <param name="warning">警告信息</param>
    public void AddWarning(string warning)
    {
        Warnings.Add(warning);
        AddLog($"WARNING: {warning}");
    }

    /// <summary>
    /// 开始测试套件
    /// </summary>
    public void Start()
    {
        StartTime = DateTime.Now;
        Status = TestStatus.Running;
        AddLog("测试套件开始执行");
    }

    /// <summary>
    /// 完成测试套件
    /// </summary>
    /// <param name="errorMessage">错误信息（如果有）</param>
    public void Complete(string? errorMessage = null)
    {
        EndTime = DateTime.Now;
        ErrorMessage = errorMessage;
        
        if (string.IsNullOrEmpty(errorMessage))
        {
            Status = AllTestsPassed ? TestStatus.Completed : TestStatus.Failed;
        }
        else
        {
            Status = TestStatus.Failed;
        }

        AddLog($"测试套件完成: 总计 {TotalTestsCount} 个测试，成功 {SuccessfulTestsCount} 个，失败 {FailedTestsCount} 个");
        AddLog($"成功率: {SuccessRate:F1}%，总耗时: {Duration.TotalMinutes:F1} 分钟");
        
        if (!string.IsNullOrEmpty(errorMessage))
        {
            AddLog($"套件错误: {errorMessage}");
        }
    }

    /// <summary>
    /// 取消测试套件
    /// </summary>
    public void Cancel()
    {
        EndTime = DateTime.Now;
        Status = TestStatus.Cancelled;
        AddLog("测试套件已取消");
    }

    /// <summary>
    /// 获取综合性能评分
    /// </summary>
    /// <returns>综合评分</returns>
    public double GetOverallPerformanceScore()
    {
        var successfulResults = TestResults.Where(r => r.Success && r.Performance != null).ToList();
        
        if (!successfulResults.Any())
            return 0;

        var scores = successfulResults.Select(r => r.Performance!.GetOverallScore()).ToList();
        return scores.Average();
    }

    /// <summary>
    /// 获取性能摘要
    /// </summary>
    /// <returns>性能摘要字典</returns>
    public Dictionary<string, object> GetPerformanceSummary()
    {
        var summary = new Dictionary<string, object>();
        var successfulResults = TestResults.Where(r => r.Success).ToList();

        if (!successfulResults.Any())
        {
            summary["Message"] = "没有成功的测试结果";
            return summary;
        }

        // 收集所有性能数据
        var readSpeeds = new List<double>();
        var writeSpeeds = new List<double>();
        var totalIOPS = new List<double>();

        foreach (var result in successfulResults)
        {
            if (result.Performance?.ReadSpeed.HasValue == true)
                readSpeeds.Add(result.Performance.ReadSpeed.Value);
            
            if (result.Performance?.WriteSpeed.HasValue == true)
                writeSpeeds.Add(result.Performance.WriteSpeed.Value);
            
            if (result.Performance?.TotalIOPS.HasValue == true)
                totalIOPS.Add(result.Performance.TotalIOPS.Value);
        }

        // 计算平均值
        if (readSpeeds.Any())
        {
            summary["AverageReadSpeed"] = readSpeeds.Average();
            summary["MaxReadSpeed"] = readSpeeds.Max();
            summary["MinReadSpeed"] = readSpeeds.Min();
        }

        if (writeSpeeds.Any())
        {
            summary["AverageWriteSpeed"] = writeSpeeds.Average();
            summary["MaxWriteSpeed"] = writeSpeeds.Max();
            summary["MinWriteSpeed"] = writeSpeeds.Min();
        }

        if (totalIOPS.Any())
        {
            summary["AverageIOPS"] = totalIOPS.Average();
            summary["MaxIOPS"] = totalIOPS.Max();
            summary["MinIOPS"] = totalIOPS.Min();
        }

        summary["OverallScore"] = GetOverallPerformanceScore();
        summary["TestCount"] = successfulResults.Count;
        summary["SuccessRate"] = SuccessRate;

        return summary;
    }
}
