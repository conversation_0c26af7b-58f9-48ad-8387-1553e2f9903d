using FlaUI.Core;
using FlaUI.Core.AutomationElements;
using FlaUI.Core.Conditions;
using FlaUI.Core.Definitions;
using Microsoft.Extensions.Logging;

namespace MassStorageStableTestTool.Automation.GUI;

/// <summary>
/// UI元素查找器，负责在窗口中查找各种UI元素
/// </summary>
public class ElementFinder
{
    private readonly ILogger<ElementFinder> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public ElementFinder(ILogger<ElementFinder> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 根据名称查找元素
    /// </summary>
    /// <param name="parent">父元素</param>
    /// <param name="name">元素名称</param>
    /// <param name="timeout">超时时间</param>
    /// <returns>找到的元素</returns>
    public AutomationElement? FindElementByName(AutomationElement parent, string name, TimeSpan timeout = default)
    {
        if (parent == null)
            throw new ArgumentNullException(nameof(parent));
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("元素名称不能为空", nameof(name));

        if (timeout == default)
            timeout = TimeSpan.FromSeconds(10);

        var endTime = DateTime.Now.Add(timeout);
        
        while (DateTime.Now < endTime)
        {
            try
            {
                var element = parent.FindFirstDescendant(cf => cf.ByName(name));
                if (element != null)
                {
                    _logger.LogDebug($"找到名称为 '{name}' 的元素");
                    return element;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"查找名称为 '{name}' 的元素时出错: {ex.Message}");
            }

            Thread.Sleep(500);
        }

        _logger.LogError($"在 {timeout.TotalSeconds} 秒内未找到名称为 '{name}' 的元素");
        return null;
    }

    /// <summary>
    /// 根据自动化ID查找元素
    /// </summary>
    /// <param name="parent">父元素</param>
    /// <param name="automationId">自动化ID</param>
    /// <param name="timeout">超时时间</param>
    /// <returns>找到的元素</returns>
    public AutomationElement? FindElementByAutomationId(AutomationElement parent, string automationId, TimeSpan timeout = default)
    {
        if (parent == null)
            throw new ArgumentNullException(nameof(parent));
        if (string.IsNullOrWhiteSpace(automationId))
            throw new ArgumentException("自动化ID不能为空", nameof(automationId));

        if (timeout == default)
            timeout = TimeSpan.FromSeconds(10);

        var endTime = DateTime.Now.Add(timeout);
        
        while (DateTime.Now < endTime)
        {
            try
            {
                var element = parent.FindFirstDescendant(cf => cf.ByAutomationId(automationId));
                if (element != null)
                {
                    _logger.LogDebug($"找到自动化ID为 '{automationId}' 的元素");
                    return element;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"查找自动化ID为 '{automationId}' 的元素时出错: {ex.Message}");
            }

            Thread.Sleep(500);
        }

        _logger.LogError($"在 {timeout.TotalSeconds} 秒内未找到自动化ID为 '{automationId}' 的元素");
        return null;
    }

    /// <summary>
    /// 根据控件类型查找元素
    /// </summary>
    /// <param name="parent">父元素</param>
    /// <param name="controlType">控件类型</param>
    /// <param name="timeout">超时时间</param>
    /// <returns>找到的元素</returns>
    public AutomationElement? FindElementByControlType(AutomationElement parent, ControlType controlType, TimeSpan timeout = default)
    {
        if (parent == null)
            throw new ArgumentNullException(nameof(parent));

        if (timeout == default)
            timeout = TimeSpan.FromSeconds(10);

        var endTime = DateTime.Now.Add(timeout);
        
        while (DateTime.Now < endTime)
        {
            try
            {
                var element = parent.FindFirstDescendant(cf => cf.ByControlType(controlType));
                if (element != null)
                {
                    _logger.LogDebug($"找到控件类型为 '{controlType}' 的元素");
                    return element;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"查找控件类型为 '{controlType}' 的元素时出错: {ex.Message}");
            }

            Thread.Sleep(500);
        }

        _logger.LogError($"在 {timeout.TotalSeconds} 秒内未找到控件类型为 '{controlType}' 的元素");
        return null;
    }

    /// <summary>
    /// 根据类名查找元素
    /// </summary>
    /// <param name="parent">父元素</param>
    /// <param name="className">类名</param>
    /// <param name="timeout">超时时间</param>
    /// <returns>找到的元素</returns>
    public AutomationElement? FindElementByClassName(AutomationElement parent, string className, TimeSpan timeout = default)
    {
        if (parent == null)
            throw new ArgumentNullException(nameof(parent));
        if (string.IsNullOrWhiteSpace(className))
            throw new ArgumentException("类名不能为空", nameof(className));

        if (timeout == default)
            timeout = TimeSpan.FromSeconds(10);

        var endTime = DateTime.Now.Add(timeout);
        
        while (DateTime.Now < endTime)
        {
            try
            {
                var element = parent.FindFirstDescendant(cf => cf.ByClassName(className));
                if (element != null)
                {
                    _logger.LogDebug($"找到类名为 '{className}' 的元素");
                    return element;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"查找类名为 '{className}' 的元素时出错: {ex.Message}");
            }

            Thread.Sleep(500);
        }

        _logger.LogError($"在 {timeout.TotalSeconds} 秒内未找到类名为 '{className}' 的元素");
        return null;
    }

    /// <summary>
    /// 根据复合条件查找元素
    /// </summary>
    /// <param name="parent">父元素</param>
    /// <param name="condition">查找条件</param>
    /// <param name="timeout">超时时间</param>
    /// <returns>找到的元素</returns>
    public AutomationElement? FindElementByCondition(AutomationElement parent, ConditionBase condition, TimeSpan timeout = default)
    {
        if (parent == null)
            throw new ArgumentNullException(nameof(parent));
        if (condition == null)
            throw new ArgumentNullException(nameof(condition));

        if (timeout == default)
            timeout = TimeSpan.FromSeconds(10);

        var endTime = DateTime.Now.Add(timeout);
        
        while (DateTime.Now < endTime)
        {
            try
            {
                var element = parent.FindFirstDescendant(condition);
                if (element != null)
                {
                    _logger.LogDebug($"根据复合条件找到元素");
                    return element;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"根据复合条件查找元素时出错: {ex.Message}");
            }

            Thread.Sleep(500);
        }

        _logger.LogError($"在 {timeout.TotalSeconds} 秒内未根据复合条件找到元素");
        return null;
    }

    /// <summary>
    /// 查找所有匹配条件的元素
    /// </summary>
    /// <param name="parent">父元素</param>
    /// <param name="condition">查找条件</param>
    /// <param name="timeout">超时时间</param>
    /// <returns>找到的元素列表</returns>
    public AutomationElement[] FindAllElementsByCondition(AutomationElement parent, ConditionBase condition, TimeSpan timeout = default)
    {
        if (parent == null)
            throw new ArgumentNullException(nameof(parent));
        if (condition == null)
            throw new ArgumentNullException(nameof(condition));

        if (timeout == default)
            timeout = TimeSpan.FromSeconds(10);

        var endTime = DateTime.Now.Add(timeout);
        
        while (DateTime.Now < endTime)
        {
            try
            {
                var elements = parent.FindAllDescendants(condition);
                if (elements != null && elements.Length > 0)
                {
                    _logger.LogDebug($"根据复合条件找到 {elements.Length} 个元素");
                    return elements;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"根据复合条件查找所有元素时出错: {ex.Message}");
            }

            Thread.Sleep(500);
        }

        _logger.LogError($"在 {timeout.TotalSeconds} 秒内未根据复合条件找到任何元素");
        return Array.Empty<AutomationElement>();
    }

    /// <summary>
    /// 等待元素出现
    /// </summary>
    /// <param name="parent">父元素</param>
    /// <param name="condition">查找条件</param>
    /// <param name="timeout">超时时间</param>
    /// <returns>是否在超时时间内出现</returns>
    public bool WaitForElement(AutomationElement parent, ConditionBase condition, TimeSpan timeout)
    {
        var element = FindElementByCondition(parent, condition, timeout);
        return element != null;
    }

    /// <summary>
    /// 等待元素消失
    /// </summary>
    /// <param name="parent">父元素</param>
    /// <param name="condition">查找条件</param>
    /// <param name="timeout">超时时间</param>
    /// <returns>是否在超时时间内消失</returns>
    public bool WaitForElementGone(AutomationElement parent, ConditionBase condition, TimeSpan timeout)
    {
        var endTime = DateTime.Now.Add(timeout);
        
        while (DateTime.Now < endTime)
        {
            try
            {
                var element = parent.FindFirstDescendant(condition);
                if (element == null)
                {
                    _logger.LogDebug("元素已消失");
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"检查元素是否消失时出错: {ex.Message}");
                // 如果出错，可能是元素已经消失
                return true;
            }

            Thread.Sleep(500);
        }

        _logger.LogWarning($"元素在 {timeout.TotalSeconds} 秒内未消失");
        return false;
    }
}
