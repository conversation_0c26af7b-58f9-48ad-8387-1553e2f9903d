namespace MassStorageStableTestTool.UI.Resources;

/// <summary>
///   A strongly-typed resource class, for looking up localized strings, etc.
/// </summary>
// This class was auto-generated by the Visual Studio Code Extension PrateekMahendrakar.resxpress
[global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
[global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
[global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
public class Strings 
{
	private static global::System.Resources.ResourceManager resourceMan;
	[global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
	public Strings()
	{
	}
	/// <summary>
	///   Returns the cached ResourceManager instance used by this class.
	/// </summary>
	[global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
	public static global::System.Resources.ResourceManager ResourceManager
	{
		get
		{
			if (object.ReferenceEquals(resourceMan, null))
			{
				global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("MassStorageStableTestTool.UI.Resources.Strings", typeof(Strings).Assembly);
				resourceMan = temp;
			}
			return resourceMan;
		}
	}
	/// <summary>
	///   Overrides the current thread's CurrentUICulture property for all
	///   resource lookups using this strongly typed resource class.
	/// </summary>
	[global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
	public static global::System.Globalization.CultureInfo Culture { get; set; }
	
	/// <summary>
	/// Looks up a localized string similar to SD Card Automated Test Tool v1.0.
	/// </summary>
	public static string WindowTitle => ResourceManager.GetString("WindowTitle", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to File(_F).
	/// </summary>
	public static string MenuFile => ResourceManager.GetString("MenuFile", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to New Config.
	/// </summary>
	public static string MenuFileNewConfig => ResourceManager.GetString("MenuFileNewConfig", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Open Config.
	/// </summary>
	public static string MenuFileOpenConfig => ResourceManager.GetString("MenuFileOpenConfig", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Save Config.
	/// </summary>
	public static string MenuFileSaveConfig => ResourceManager.GetString("MenuFileSaveConfig", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Exit.
	/// </summary>
	public static string MenuFileExit => ResourceManager.GetString("MenuFileExit", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Settings(_S).
	/// </summary>
	public static string MenuSettings => ResourceManager.GetString("MenuSettings", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Test Tool Settings.
	/// </summary>
	public static string MenuSettingsTool => ResourceManager.GetString("MenuSettingsTool", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Report Settings.
	/// </summary>
	public static string MenuSettingsReport => ResourceManager.GetString("MenuSettingsReport", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Report(_R).
	/// </summary>
	public static string MenuReport => ResourceManager.GetString("MenuReport", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to View Latest Report.
	/// </summary>
	public static string MenuReportViewLatest => ResourceManager.GetString("MenuReportViewLatest", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Open Report Folder.
	/// </summary>
	public static string MenuReportOpenFolder => ResourceManager.GetString("MenuReportOpenFolder", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Help(_H).
	/// </summary>
	public static string MenuHelp => ResourceManager.GetString("MenuHelp", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to User Manual.
	/// </summary>
	public static string MenuHelpUserManual => ResourceManager.GetString("MenuHelpUserManual", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to About.
	/// </summary>
	public static string MenuHelpAbout => ResourceManager.GetString("MenuHelpAbout", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to 🖥️ Device Selection 
		///     (Multi-select).
	/// </summary>
	public static string DeviceSelectionCardTitle => ResourceManager.GetString("DeviceSelectionCardTitle", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Select All.
	/// </summary>
	public static string ButtonSelectAll => ResourceManager.GetString("ButtonSelectAll", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Deselect All.
	/// </summary>
	public static string ButtonDeselectAll => ResourceManager.GetString("ButtonDeselectAll", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Current: {0}.
	/// </summary>
	public static string LabelCurrentTest => ResourceManager.GetString("LabelCurrentTest", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to 🔄 Refresh Drive List.
	/// </summary>
	public static string ButtonRefreshDrives => ResourceManager.GetString("ButtonRefreshDrives", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to 🎮 Parallel Test Control.
	/// </summary>
	public static string TestControlCardTitle => ResourceManager.GetString("TestControlCardTitle", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to ▶️ Start Tests.
	/// </summary>
	public static string ButtonStartParallelTest => ResourceManager.GetString("ButtonStartParallelTest", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to ⏹️ Stop Tests.
	/// </summary>
	public static string ButtonStopAllTests => ResourceManager.GetString("ButtonStopAllTests", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to 📋 Test Configuration:.
	/// </summary>
	public static string LabelTestConfiguration => ResourceManager.GetString("LabelTestConfiguration", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to 💡 Parallel Test Info:.
	/// </summary>
	public static string LabelParallelTestInfo => ResourceManager.GetString("LabelParallelTestInfo", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to • All selected devices will be tested simultaneously.
	/// </summary>
	public static string TextParallelTestInfo1 => ResourceManager.GetString("TextParallelTestInfo1", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to • Each device runs the selected test tools independently.
	/// </summary>
	public static string TextParallelTestInfo2 => ResourceManager.GetString("TextParallelTestInfo2", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to • You can view the test progress of each device in real time.
	/// </summary>
	public static string TextParallelTestInfo3 => ResourceManager.GetString("TextParallelTestInfo3", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to 🔧 Test Tool Selection.
	/// </summary>
	public static string ToolSelectionCardTitle => ResourceManager.GetString("ToolSelectionCardTitle", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to ⚙️ Advanced Settings.
	/// </summary>
	public static string ButtonAdvancedSettings => ResourceManager.GetString("ButtonAdvancedSettings", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to 🖥️ GUI Tools.
	/// </summary>
	public static string HeaderGuiTools => ResourceManager.GetString("HeaderGuiTools", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to 💻 CLI Tools.
	/// </summary>
	public static string HeaderCliTools => ResourceManager.GetString("HeaderCliTools", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to 🔄 Hybrid Tools.
	/// </summary>
	public static string HeaderHybridTools => ResourceManager.GetString("HeaderHybridTools", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to 📊 Parallel Test Progress.
	/// </summary>
	public static string ParallelTestProgressCardTitle => ResourceManager.GetString("ParallelTestProgressCardTitle", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Overall Progress (Average of all devices):.
	/// </summary>
	public static string LabelOverallProgress => ResourceManager.GetString("LabelOverallProgress", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Device Status Stats:.
	/// </summary>
	public static string LabelDeviceStatusStats => ResourceManager.GetString("LabelDeviceStatusStats", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to 🔄 Testing: .
	/// </summary>
	public static string LabelTesting => ResourceManager.GetString("LabelTesting", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to ✅ Completed: .
	/// </summary>
	public static string LabelCompleted => ResourceManager.GetString("LabelCompleted", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to ⚪ Waiting: .
	/// </summary>
	public static string LabelWaiting => ResourceManager.GetString("LabelWaiting", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to ❌ Failed: .
	/// </summary>
	public static string LabelFailed => ResourceManager.GetString("LabelFailed", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Test Tool Status:.
	/// </summary>
	public static string LabelToolStatus => ResourceManager.GetString("LabelToolStatus", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to 💡 Tip: Each device runs tests independently.
	/// </summary>
	public static string TextTip => ResourceManager.GetString("TextTip", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to 📝 Real-time Log.
	/// </summary>
	public static string RealTimeLogCardTitle => ResourceManager.GetString("RealTimeLogCardTitle", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Clear.
	/// </summary>
	public static string ButtonClear => ResourceManager.GetString("ButtonClear", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Save.
	/// </summary>
	public static string ButtonSave => ResourceManager.GetString("ButtonSave", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to CPU: .
	/// </summary>
	public static string StatusBarCpu => ResourceManager.GetString("StatusBarCpu", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Memory: .
	/// </summary>
	public static string StatusBarMemory => ResourceManager.GetString("StatusBarMemory", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Waiting to start.
	/// </summary>
	public static string Status_WaitingStart => ResourceManager.GetString("Status_WaitingStart", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Ready.
	/// </summary>
	public static string Status_Ready => ResourceManager.GetString("Status_Ready", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Not ready.
	/// </summary>
	public static string Status_NotReady => ResourceManager.GetString("Status_NotReady", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Completed.
	/// </summary>
	public static string Status_Completed => ResourceManager.GetString("Status_Completed", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Preparing test....
	/// </summary>
	public static string Status_PreparingTest => ResourceManager.GetString("Status_PreparingTest", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Test completed.
	/// </summary>
	public static string Status_TestCompleted => ResourceManager.GetString("Status_TestCompleted", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Test stopped.
	/// </summary>
	public static string Status_TestStopped => ResourceManager.GetString("Status_TestStopped", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Found {0} removable storage devices.
	/// </summary>
	public static string Log_FoundRemovableDevice => ResourceManager.GetString("Log_FoundRemovableDevice", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to • Devices selected: {0}  
		/// • Tools selected: {1}  
		/// • Estimated time: Approx. {2} hours  
		/// • Test Mode: Parallel Testing.
	/// </summary>
	public static string TestSummaryFormat => ResourceManager.GetString("TestSummaryFormat", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Failed to refresh drive list: {0}.
	/// </summary>
	public static string Error_RefreshDrives => ResourceManager.GetString("Error_RefreshDrives", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Test execution failed: {0}.
	/// </summary>
	public static string Log_TestExecutionFailed => ResourceManager.GetString("Log_TestExecutionFailed", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Starting test execution....
	/// </summary>
	public static string Log_BeginTestExecution => ResourceManager.GetString("Log_BeginTestExecution", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to User stopped the test.
	/// </summary>
	public static string Log_UserStoppedTest => ResourceManager.GetString("Log_UserStoppedTest", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Settings saved.
	/// </summary>
	public static string Log_SettingSaved => ResourceManager.GetString("Log_SettingSaved", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Settings cancelled.
	/// </summary>
	public static string Log_SettingCancelled => ResourceManager.GetString("Log_SettingCancelled", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Failed to open settings window: {0}.
	/// </summary>
	public static string Log_OpenSettingWindowsFail => ResourceManager.GetString("Log_OpenSettingWindowsFail", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Log cleared.
	/// </summary>
	public static string Log_ClearLog => ResourceManager.GetString("Log_ClearLog", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Log saved to file.
	/// </summary>
	public static string Log_SaveLogToFile => ResourceManager.GetString("Log_SaveLogToFile", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to {0} devices selected.
	/// </summary>
	public static string Log_SelectedDrives => ResourceManager.GetString("Log_SelectedDrives", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Device: {0} selected.
	/// </summary>
	public static string Log_DriverSelected => ResourceManager.GetString("Log_DriverSelected", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Device: {0} deselected.
	/// </summary>
	public static string Log_DriverDeselected => ResourceManager.GetString("Log_DriverDeselected", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to All devices deselected.
	/// </summary>
	public static string Log_AllDrivesDeselected => ResourceManager.GetString("Log_AllDrivesDeselected", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Testing complete for all {0} devices. Generating report....
	/// </summary>
	public static string Log_GenerateReport => ResourceManager.GetString("Log_GenerateReport", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to All tests completed for device {0}.
	/// </summary>
	public static string Log_DriverTestCompleted => ResourceManager.GetString("Log_DriverTestCompleted", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to {0} : {1} test completed.
	/// </summary>
	public static string Log_ToolTestCompleted => ResourceManager.GetString("Log_ToolTestCompleted", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to {0} : {1} test started.
	/// </summary>
	public static string Log_ToolTestBegin => ResourceManager.GetString("Log_ToolTestBegin", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Testing started for device {0}.
	/// </summary>
	public static string Log_DriverTestBegin => ResourceManager.GetString("Log_DriverTestBegin", Culture);
	
	/// <summary>
	/// Looks up a localized string similar to Refresh Drivers.
	/// </summary>
	public static string Log_RefreshDrivers => ResourceManager.GetString("Log_RefreshDrivers", Culture);
}
