{
  "report_metadata": {
    "title": "{{ configuration.title }}",
    "generated_at": "{{ generated_at | date: 'yyyy-MM-ddTHH:mm:ss.fffZ' }}",
    "generated_by": "{{ generated_by }}",
    "version": "{{ version }}",
    "author": "{{ configuration.author }}",
    "organization": "{{ configuration.organization }}"
  },
  "test_suite": {
    "start_time": "{{ test_suite.start_time | date: 'yyyy-MM-ddTHH:mm:ss.fffZ' }}",
    "end_time": "{{ test_suite.end_time | date: 'yyyy-MM-ddTHH:mm:ss.fffZ' }}",
    "duration": {
      "total_milliseconds": {{ test_suite.duration.total_milliseconds }},
      "total_seconds": {{ test_suite.duration.total_seconds }},
      "total_minutes": {{ test_suite.duration.total_minutes }},
      "total_hours": {{ test_suite.duration.total_hours }}
    },
    "status": "{{ test_suite.status }}",
    "success": {{ test_suite.all_tests_passed | json }},
    "configuration": {
      "target_drive": "{{ test_suite.configuration.target_drive }}",
      "selected_tools": [
        {{ for tool in test_suite.configuration.selected_tools }}
        "{{ tool }}"{{ unless forloop.last }},{{ end }}
        {{ end }}
      ],
      "format_before_test": {{ test_suite.configuration.format_before_test | json }},
      "quick_format": {{ test_suite.configuration.quick_format | json }},
      "test_timeout_minutes": {{ test_suite.configuration.test_timeout_minutes }},
      "output_directory": "{{ test_suite.configuration.output_directory }}"
    },
    "statistics": {
      "total_tests_count": {{ test_suite.total_tests_count }},
      "successful_tests_count": {{ test_suite.successful_tests_count }},
      "failed_tests_count": {{ test_suite.failed_tests_count }},
      "success_rate": {{ test_suite.success_rate }}
    },
    "test_results": [
      {{ for test_result in test_suite.test_results }}
      {
        "tool_name": "{{ test_result.tool_name }}",
        "start_time": "{{ test_result.start_time | date: 'yyyy-MM-ddTHH:mm:ss.fffZ' }}",
        "end_time": "{{ test_result.end_time | date: 'yyyy-MM-ddTHH:mm:ss.fffZ' }}",
        "duration": {
          "total_milliseconds": {{ test_result.duration.total_milliseconds }},
          "total_seconds": {{ test_result.duration.total_seconds }},
          "total_minutes": {{ test_result.duration.total_minutes }}
        },
        "status": "{{ test_result.status }}",
        "success": {{ test_result.success | json }},
        "error_message": {{ test_result.error_message | json }},
        "output_file_path": {{ test_result.output_file_path | json }},
        "exit_code": {{ test_result.exit_code }},
        "process_id": {{ test_result.process_id }},
        "command_line": {{ test_result.command_line | json }},
        "working_directory": {{ test_result.working_directory | json }}
      }{{ unless forloop.last }},{{ end }}
      {{ end }}
    ]{{ if test_suite.drive_info }},
    "drive_info": {
      "name": "{{ test_suite.drive_info.name }}",
      "label": "{{ test_suite.drive_info.label }}",
      "file_system": "{{ test_suite.drive_info.file_system }}",
      "drive_type": "{{ test_suite.drive_info.drive_type }}",
      "total_size_bytes": {{ test_suite.drive_info.total_size }},
      "total_size_gb": {{ test_suite.drive_info.total_size_gb }},
      "available_free_space_bytes": {{ test_suite.drive_info.available_free_space }},
      "available_free_space_gb": {{ test_suite.drive_info.available_free_space_gb }},
      "is_ready": {{ test_suite.drive_info.is_ready | json }}
    }{{ end }}
  }{{ if configuration.include_system_info }},
  "system_info": {
    "os_version": "{{ Environment.OSVersion }}",
    "machine_name": "{{ Environment.MachineName }}",
    "user_name": "{{ Environment.UserName }}",
    "processor_count": {{ Environment.ProcessorCount }},
    "working_set": {{ Environment.WorkingSet }},
    "clr_version": "{{ Environment.Version }}"
  }{{ end }}
}
