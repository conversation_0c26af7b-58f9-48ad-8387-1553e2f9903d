using System.ComponentModel;
using System.Windows.Media;

namespace MassStorageStableTestTool.UI.Models
{
    public enum TestToolType
    {
        GUI,
        CLI,
        Hybrid
    }

    public enum TestToolStatus
    {
        Available,      // 可用且已配置
        NotConfigured,  // 可用但未配置
        NotAvailable,   // 不可用
        Testing,        // 测试中
        Completed,      // 测试完成
        Failed,         // 测试失败
        Paused          // 测试暂停
    }

    public class TestToolViewModel : INotifyPropertyChanged
    {
        private bool _isSelected;
        private TestToolStatus _status = TestToolStatus.NotAvailable;
        private double _progress;
        private string _statusMessage = string.Empty;

        public string Name { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public TestToolType ToolType { get; set; }
        public string ExecutablePath { get; set; } = string.Empty;

        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                _isSelected = value;
                OnPropertyChanged();
            }
        }

        public TestToolStatus Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(StatusColor));
                OnPropertyChanged(nameof(StatusIcon));
            }
        }

        public double Progress
        {
            get => _progress;
            set
            {
                _progress = value;
                OnPropertyChanged();
            }
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                _statusMessage = value;
                OnPropertyChanged();
            }
        }

        // UI绑定属性
        public Brush StatusColor
        {
            get
            {
                return Status switch
                {
                    TestToolStatus.Available => new SolidColorBrush(Colors.Green),
                    TestToolStatus.NotConfigured => new SolidColorBrush(Colors.Orange),
                    TestToolStatus.NotAvailable => new SolidColorBrush(Colors.Red),
                    TestToolStatus.Testing => new SolidColorBrush(Colors.Blue),
                    TestToolStatus.Completed => new SolidColorBrush(Colors.Green),
                    TestToolStatus.Failed => new SolidColorBrush(Colors.Red),
                    TestToolStatus.Paused => new SolidColorBrush(Colors.Orange),
                    _ => new SolidColorBrush(Colors.Gray)
                };
            }
        }

        public string StatusIcon
        {
            get
            {
                return Status switch
                {
                    TestToolStatus.Available => "🟢",
                    TestToolStatus.NotConfigured => "🟡",
                    TestToolStatus.NotAvailable => "🔴",
                    TestToolStatus.Testing => "🔄",
                    TestToolStatus.Completed => "✅",
                    TestToolStatus.Failed => "❌",
                    TestToolStatus.Paused => "⏸️",
                    _ => "⚪"
                };
            }
        }

        public string ToolTypeIcon
        {
            get
            {
                return ToolType switch
                {
                    TestToolType.GUI => "🖥️",
                    TestToolType.CLI => "💻",
                    TestToolType.Hybrid => "🔄",
                    _ => "🔧"
                };
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
