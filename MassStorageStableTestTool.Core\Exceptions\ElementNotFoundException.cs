namespace MassStorageStableTestTool.Core.Exceptions;

/// <summary>
/// UI元素未找到异常
/// </summary>
public class ElementNotFoundException : Exception
{
    /// <summary>
    /// 元素标识符
    /// </summary>
    public string ElementIdentifier { get; }

    /// <summary>
    /// 窗口标题
    /// </summary>
    public string? WindowTitle { get; }

    /// <summary>
    /// 超时时间
    /// </summary>
    public TimeSpan? Timeout { get; }

    /// <summary>
    /// 使用元素标识符初始化异常。
    /// </summary>
    /// <param name="elementIdentifier">元素标识符</param>
    public ElementNotFoundException(string elementIdentifier) 
        : base($"UI元素 '{elementIdentifier}' 未找到")
    {
        ElementIdentifier = elementIdentifier;
    }

    /// <summary>
    /// 使用元素标识符和窗口标题初始化异常。
    /// </summary>
    /// <param name="elementIdentifier">元素标识符</param>
    /// <param name="windowTitle">窗口标题</param>
    public ElementNotFoundException(string elementIdentifier, string windowTitle) 
        : base($"在窗口 '{windowTitle}' 中未找到UI元素 '{elementIdentifier}'")
    {
        ElementIdentifier = elementIdentifier;
        WindowTitle = windowTitle;
    }

    /// <summary>
    /// 使用元素标识符和超时时间初始化异常。
    /// </summary>
    /// <param name="elementIdentifier">元素标识符</param>
    /// <param name="timeout">超时时间</param>
    public ElementNotFoundException(string elementIdentifier, TimeSpan timeout) 
        : base($"在 {timeout.TotalSeconds} 秒内未找到UI元素 '{elementIdentifier}'")
    {
        ElementIdentifier = elementIdentifier;
        Timeout = timeout;
    }

    /// <summary>
    /// 使用元素标识符、窗口标题和超时时间初始化异常。
    /// </summary>
    /// <param name="elementIdentifier">元素标识符</param>
    /// <param name="windowTitle">窗口标题</param>
    /// <param name="timeout">超时时间</param>
    public ElementNotFoundException(string elementIdentifier, string windowTitle, TimeSpan timeout) 
        : base($"在窗口 '{windowTitle}' 中，{timeout.TotalSeconds} 秒内未找到UI元素 '{elementIdentifier}'")
    {
        ElementIdentifier = elementIdentifier;
        WindowTitle = windowTitle;
        Timeout = timeout;
    }

    /// <summary>
    /// 使用元素标识符和内部异常初始化异常。
    /// </summary>
    /// <param name="elementIdentifier">元素标识符</param>
    /// <param name="innerException">内部异常</param>
    public ElementNotFoundException(string elementIdentifier, Exception innerException) 
        : base($"UI元素 '{elementIdentifier}' 未找到", innerException)
    {
        ElementIdentifier = elementIdentifier;
    }
}
