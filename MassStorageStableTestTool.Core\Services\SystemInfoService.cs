using MassStorageStableTestTool.Core.Interfaces;
using MassStorageStableTestTool.Core.Models;
using System.Management;
using System.Net.NetworkInformation;
using System.Runtime.InteropServices;

namespace MassStorageStableTestTool.Core.Services;

/// <summary>
/// 系统信息服务实现
/// </summary>
public class SystemInfoService : ISystemInfoService
{
    private Timer? _performanceTimer;
    private readonly object _lockObject = new();
    /// <summary>
    /// 触发驱动器状态变化事件。
    /// </summary>
    /// <param name="args">事件参数</param>
    protected void OnDriveStatusChanged(DriveStatusChangedEventArgs args)
    {
        DriveStatusChanged?.Invoke(this, args);
    }
    /// <summary>
    /// 性能数据更新事件
    /// </summary>
    public event EventHandler<PerformanceDataEventArgs>? PerformanceDataUpdated;

    /// <summary>
    /// 驱动器状态变化事件
    /// </summary>
    public event EventHandler<DriveStatusChangedEventArgs>? DriveStatusChanged;

    /// <summary>
    /// 获取系统信息
    /// </summary>
    /// <returns>系统信息</returns>
    public async Task<SystemInfo> GetSystemInfoAsync()
    {
        var systemInfo = new SystemInfo();

        try
        {
            // 基本系统信息
            systemInfo.OperatingSystem = Environment.OSVersion.Platform.ToString();
            systemInfo.OSVersion = Environment.OSVersion.VersionString;
            systemInfo.Architecture = RuntimeInformation.OSArchitecture.ToString();
            systemInfo.ComputerName = Environment.MachineName;
            systemInfo.UserName = Environment.UserName;
            systemInfo.DotNetVersion = RuntimeInformation.FrameworkDescription;
            systemInfo.SystemLanguage = System.Globalization.CultureInfo.CurrentCulture.DisplayName;
            systemInfo.TimeZone = TimeZoneInfo.Local.DisplayName;

            // 处理器信息
            systemInfo.ProcessorCores = Environment.ProcessorCount;
            systemInfo.LogicalProcessors = Environment.ProcessorCount;

            // 内存信息
            var totalMemory = GC.GetTotalMemory(false);
            systemInfo.TotalMemory = Math.Round(totalMemory / (1024.0 * 1024.0 * 1024.0), 2);

            // 系统启动时间
            var uptime = TimeSpan.FromMilliseconds(Environment.TickCount64);
            systemInfo.SystemStartTime = DateTime.Now - uptime;

            // 屏幕分辨率（如果在Windows环境下）
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                await GetWindowsSpecificInfoAsync(systemInfo);
            }

            // 环境变量
            systemInfo.EnvironmentVariables = Environment.GetEnvironmentVariables()
                .Cast<System.Collections.DictionaryEntry>()
                .ToDictionary(
                    entry => entry.Key.ToString() ?? string.Empty,
                    entry => entry.Value?.ToString() ?? string.Empty
                );

            // 网络适配器信息
            systemInfo.NetworkAdapters = await GetNetworkAdaptersAsync();

            // 已安装软件（仅Windows）
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                systemInfo.InstalledSoftware = await GetInstalledSoftwareAsync();
            }
        }
        catch (Exception ex)
        {
            // 记录错误但不抛出异常，返回部分信息
            Console.WriteLine($"获取系统信息时出现错误: {ex.Message}");
        }

        return systemInfo;
    }

    /// <summary>
    /// 获取Windows特定信息
    /// </summary>
    /// <param name="systemInfo">系统信息对象</param>
    private async Task GetWindowsSpecificInfoAsync(SystemInfo systemInfo)
    {
        try
        {
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                // 使用WMI获取详细信息
                await Task.Run(() =>
                {
                    try
                    {
                        // 处理器信息
                        using var processorSearcher = new ManagementObjectSearcher("SELECT * FROM Win32_Processor");
                        foreach (ManagementObject processor in processorSearcher.Get())
                        {
                            systemInfo.Processor = processor["Name"]?.ToString() ?? "Unknown";
                            if (int.TryParse(processor["NumberOfCores"]?.ToString(), out var cores))
                            {
                                systemInfo.ProcessorCores = cores;
                            }
                            if (int.TryParse(processor["NumberOfLogicalProcessors"]?.ToString(), out var logicalCores))
                            {
                                systemInfo.LogicalProcessors = logicalCores;
                            }
                            break; // 只取第一个处理器
                        }

                        // 内存信息
                        using var memorySearcher = new ManagementObjectSearcher("SELECT * FROM Win32_ComputerSystem");
                        foreach (ManagementObject memory in memorySearcher.Get())
                        {
                            if (ulong.TryParse(memory["TotalPhysicalMemory"]?.ToString(), out var totalMemory))
                            {
                                systemInfo.TotalMemory = Math.Round(totalMemory / (1024.0 * 1024.0 * 1024.0), 2);
                            }
                            break;
                        }

                        // 可用内存
                        using var availableMemorySearcher = new ManagementObjectSearcher("SELECT * FROM Win32_OperatingSystem");
                        foreach (ManagementObject os in availableMemorySearcher.Get())
                        {
                            if (ulong.TryParse(os["FreePhysicalMemory"]?.ToString(), out var freeMemory))
                            {
                                systemInfo.AvailableMemory = Math.Round(freeMemory / (1024.0 * 1024.0), 2);
                            }
                            break;
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"获取Windows特定信息时出现错误: {ex.Message}");
                    }
                });
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取Windows特定信息失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取驱动器信息
    /// </summary>
    /// <param name="driveName">驱动器名称</param>
    /// <returns>驱动器信息</returns>
    public async Task<Models.DriveInfo?> GetDriveInfoAsync(string driveName)
    {
        try
        {
            var systemDriveInfo = new System.IO.DriveInfo(driveName);
            if (!systemDriveInfo.IsReady)
            {
                return null;
            }

            var driveInfo = new Models.DriveInfo
            {
                Name = systemDriveInfo.Name,
                Label = systemDriveInfo.VolumeLabel,
                DriveType = systemDriveInfo.DriveType,
                FileSystem = systemDriveInfo.DriveFormat,
                TotalSize = systemDriveInfo.TotalSize,
                AvailableFreeSpace = systemDriveInfo.AvailableFreeSpace,
                TotalFreeSpace = systemDriveInfo.TotalFreeSpace,
                IsReady = systemDriveInfo.IsReady,
                RootDirectory = systemDriveInfo.RootDirectory.FullName,
                DriveFormat = systemDriveInfo.DriveFormat
            };

            // 获取额外的驱动器信息（Windows）
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                await GetWindowsDriveInfoAsync(driveInfo, driveName);
            }

            // 触发驱动器状态变化事件（示例，实际可根据业务逻辑调整）
            OnDriveStatusChanged(new DriveStatusChangedEventArgs
            {
                DriveName = driveInfo.Name,
                ChangeType = driveInfo.IsReady ? DriveChangeType.Inserted : DriveChangeType.Removed,
                DriveInfo = driveInfo,
                Timestamp = DateTime.Now
            });

            return driveInfo;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取驱动器 {driveName} 信息失败: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 获取Windows驱动器详细信息
    /// </summary>
    /// <param name="driveInfo">驱动器信息对象</param>
    /// <param name="driveName">驱动器名称</param>
    private async Task GetWindowsDriveInfoAsync(Models.DriveInfo driveInfo, string driveName)
    {
        try
        {
            await Task.Run(() =>
            {
                try
                {
                    var driveQuery = $"SELECT * FROM Win32_LogicalDisk WHERE DeviceID = '{driveName.TrimEnd('\\')}'";
                    using var driveSearcher = new ManagementObjectSearcher(driveQuery);
                    
                    foreach (ManagementObject drive in driveSearcher.Get())
                    {
                        driveInfo.SerialNumber = drive["VolumeSerialNumber"]?.ToString() ?? string.Empty;
                        break;
                    }

                    // 获取物理磁盘信息
                    var physicalDiskQuery = "SELECT * FROM Win32_DiskDrive";
                    using var diskSearcher = new ManagementObjectSearcher(physicalDiskQuery);
                    
                    foreach (ManagementObject disk in diskSearcher.Get())
                    {
                        var model = disk["Model"]?.ToString();
                        var manufacturer = disk["Manufacturer"]?.ToString();
                        var interfaceType = disk["InterfaceType"]?.ToString();

                        if (!string.IsNullOrEmpty(model))
                        {
                            driveInfo.Model = model;
                            driveInfo.Manufacturer = manufacturer ?? string.Empty;
                            driveInfo.InterfaceType = interfaceType ?? string.Empty;
                            break; // 简化处理，只取第一个
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"获取Windows驱动器详细信息失败: {ex.Message}");
                }
            });
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取Windows驱动器信息失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取所有可用驱动器
    /// </summary>
    /// <returns>驱动器信息列表</returns>
    public async Task<List<Models.DriveInfo>> GetAvailableDrivesAsync()
    {
        var drives = new List<Models.DriveInfo>();

        try
        {
            var systemDrives = System.IO.DriveInfo.GetDrives();
            
            foreach (var systemDrive in systemDrives)
            {
                if (systemDrive.IsReady)
                {
                    var driveInfo = await GetDriveInfoAsync(systemDrive.Name);
                    if (driveInfo != null)
                    {
                        drives.Add(driveInfo);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取可用驱动器列表失败: {ex.Message}");
        }

        return drives;
    }

    /// <summary>
    /// 获取可移动驱动器列表
    /// </summary>
    /// <returns>可移动驱动器列表</returns>
    public async Task<List<Models.DriveInfo>> GetRemovableDrivesAsync()
    {
        var allDrives = await GetAvailableDrivesAsync();
        return allDrives.Where(d => d.IsRemovable).ToList();
    }

    /// <summary>
    /// 检查驱动器是否适合测试
    /// </summary>
    /// <param name="driveName">驱动器名称</param>
    /// <param name="requiredSpace">所需空间（字节）</param>
    /// <returns>检查结果</returns>
    public async Task<(bool IsSuitable, List<string> Issues)> CheckDriveSuitabilityAsync(string driveName, long requiredSpace = 0)
    {
        var driveInfo = await GetDriveInfoAsync(driveName);
        if (driveInfo == null)
        {
            return (false, new List<string> { "驱动器不存在或不可访问" });
        }

        return driveInfo.CheckSuitabilityForTesting(requiredSpace);
    }

    /// <summary>
    /// 获取系统性能信息
    /// </summary>
    /// <returns>性能信息</returns>
    public async Task<SystemPerformanceInfo> GetSystemPerformanceAsync()
    {
        var performanceInfo = new SystemPerformanceInfo();

        try
        {
            // 基本性能信息
            performanceInfo.ProcessCount = System.Diagnostics.Process.GetProcesses().Length;
            performanceInfo.Uptime = TimeSpan.FromMilliseconds(Environment.TickCount64);

            // 内存信息
            var totalMemory = GC.GetTotalMemory(false);
            performanceInfo.TotalMemory = Math.Round(totalMemory / (1024.0 * 1024.0 * 1024.0), 2);

            // Windows特定性能信息
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                await GetWindowsPerformanceInfoAsync(performanceInfo);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取系统性能信息失败: {ex.Message}");
        }

        return performanceInfo;
    }

    /// <summary>
    /// 获取Windows性能信息
    /// </summary>
    /// <param name="performanceInfo">性能信息对象</param>
    private async Task GetWindowsPerformanceInfoAsync(SystemPerformanceInfo performanceInfo)
    {
        try
        {
            await Task.Run(() =>
            {
                try
                {
                    // CPU使用率
                    using var cpuCounter = new System.Diagnostics.PerformanceCounter("Processor", "% Processor Time", "_Total");
                    cpuCounter.NextValue(); // 第一次调用返回0
                    Thread.Sleep(100);
                    performanceInfo.CpuUsage = Math.Round(cpuCounter.NextValue(), 2);

                    // 内存使用率
                    using var memoryCounter = new System.Diagnostics.PerformanceCounter("Memory", "Available MBytes");
                    var availableMemoryMB = memoryCounter.NextValue();
                    performanceInfo.AvailableMemory = Math.Round(availableMemoryMB / 1024.0, 2);
                    
                    if (performanceInfo.TotalMemory > 0)
                    {
                        performanceInfo.MemoryUsage = Math.Round((performanceInfo.TotalMemory - performanceInfo.AvailableMemory) / performanceInfo.TotalMemory * 100, 2);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"获取Windows性能计数器失败: {ex.Message}");
                }
            });
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取Windows性能信息失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 监控系统性能
    /// </summary>
    /// <param name="interval">监控间隔</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>性能监控任务</returns>
    public async Task StartPerformanceMonitoringAsync(TimeSpan interval, CancellationToken cancellationToken)
    {
        lock (_lockObject)
        {
            if (_performanceTimer != null)
            {
                _performanceTimer.Dispose();
            }

            _performanceTimer = new Timer(async _ =>
            {
                try
                {
                    var performanceInfo = await GetSystemPerformanceAsync();
                    PerformanceDataUpdated?.Invoke(this, new PerformanceDataEventArgs
                    {
                        PerformanceInfo = performanceInfo,
                        Timestamp = DateTime.Now
                    });
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"性能监控出现错误: {ex.Message}");
                }
            }, null, TimeSpan.Zero, interval);
        }

        // 等待取消令牌
        try
        {
            await Task.Delay(Timeout.Infinite, cancellationToken);
        }
        catch (OperationCanceledException)
        {
            await StopPerformanceMonitoringAsync();
        }
    }

    /// <summary>
    /// 停止性能监控
    /// </summary>
    /// <returns>停止结果</returns>
    public async Task<bool> StopPerformanceMonitoringAsync()
    {
        try
        {
            lock (_lockObject)
            {
                _performanceTimer?.Dispose();
                _performanceTimer = null;
            }
            await Task.CompletedTask;
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"停止性能监控失败: {ex.Message}");
            await Task.CompletedTask;
            return false;
        }
    }

    /// <summary>
    /// 获取已安装的软件列表
    /// </summary>
    /// <returns>已安装软件列表</returns>
    public async Task<List<InstalledSoftware>> GetInstalledSoftwareAsync()
    {
        var softwareList = new List<InstalledSoftware>();

        if (!RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
        {
            return softwareList;
        }

        try
        {
            await Task.Run(() =>
            {
                try
                {
                    using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_Product");
                    foreach (ManagementObject software in searcher.Get())
                    {
                        var installedSoftware = new InstalledSoftware
                        {
                            Name = software["Name"]?.ToString() ?? string.Empty,
                            Version = software["Version"]?.ToString() ?? string.Empty,
                            Publisher = software["Vendor"]?.ToString() ?? string.Empty,
                            InstallPath = software["InstallLocation"]?.ToString() ?? string.Empty
                        };

                        if (DateTime.TryParse(software["InstallDate"]?.ToString(), out var installDate))
                        {
                            installedSoftware.InstallDate = installDate;
                        }

                        if (!string.IsNullOrEmpty(installedSoftware.Name))
                        {
                            softwareList.Add(installedSoftware);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"获取已安装软件列表失败: {ex.Message}");
                }
            });
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取已安装软件失败: {ex.Message}");
        }

        return softwareList;
    }

    /// <summary>
    /// 检查软件是否已安装
    /// </summary>
    /// <param name="softwareName">软件名称</param>
    /// <returns>是否已安装</returns>
    public async Task<bool> IsSoftwareInstalledAsync(string softwareName)
    {
        var installedSoftware = await GetInstalledSoftwareAsync();
        return installedSoftware.Any(s => s.Name.Contains(softwareName, StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// 获取环境变量
    /// </summary>
    /// <returns>环境变量字典</returns>
    public async Task<Dictionary<string, string>> GetEnvironmentVariablesAsync()
    {
        return await Task.FromResult(
            Environment.GetEnvironmentVariables()
                .Cast<System.Collections.DictionaryEntry>()
                .ToDictionary(
                    entry => entry.Key.ToString() ?? string.Empty,
                    entry => entry.Value?.ToString() ?? string.Empty
                )
        );
    }

    /// <summary>
    /// 获取网络适配器信息
    /// </summary>
    /// <returns>网络适配器列表</returns>
    public async Task<List<NetworkAdapter>> GetNetworkAdaptersAsync()
    {
        var adapters = new List<NetworkAdapter>();

        try
        {
            var networkInterfaces = NetworkInterface.GetAllNetworkInterfaces();
            
            foreach (var networkInterface in networkInterfaces)
            {
                var adapter = new NetworkAdapter
                {
                    Name = networkInterface.Name,
                    Description = networkInterface.Description,
                    IsEnabled = networkInterface.OperationalStatus == OperationalStatus.Up,
                    AdapterType = networkInterface.NetworkInterfaceType.ToString(),
                    Speed = networkInterface.Speed,
                    MacAddress = networkInterface.GetPhysicalAddress().ToString()
                };

                // 获取IP地址
                var ipProperties = networkInterface.GetIPProperties();
                adapter.IPAddresses = ipProperties.UnicastAddresses
                    .Select(addr => addr.Address.ToString())
                    .ToList();

                adapters.Add(adapter);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取网络适配器信息失败: {ex.Message}");
        }

    await Task.CompletedTask;
    return adapters;
    }
}
