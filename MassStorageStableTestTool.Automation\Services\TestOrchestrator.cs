using Microsoft.Extensions.Logging;
using MassStorageStableTestTool.Core.Interfaces;
using MassStorageStableTestTool.Core.Models;
using MassStorageStableTestTool.Core.Enums;
using System.Collections.Concurrent;
using System.IO;

namespace MassStorageStableTestTool.Automation.Services;

/// <summary>
/// 测试编排器实现
/// </summary>
public class TestOrchestrator : ITestOrchestrator
{
    private readonly IControllerFactory _controllerFactory;
    private readonly IReportService _reportService;
    private readonly ILogger<TestOrchestrator> _logger;
    private readonly ConcurrentDictionary<string, ITestToolController> _controllers;
    private TestSuiteStatus _currentStatus = TestSuiteStatus.Idle;
    // private TestStatus _currentToolStatus = TestStatus.NotStarted;
    private CancellationTokenSource? _currentCancellationTokenSource;
    private TestSuiteResult? _currentTestSuite;

    public TestOrchestrator(
        IControllerFactory controllerFactory,
        IReportService reportService,
        ILogger<TestOrchestrator> logger)
    {
        _controllerFactory = controllerFactory ?? throw new ArgumentNullException(nameof(controllerFactory));
        _reportService = reportService ?? throw new ArgumentNullException(nameof(reportService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _controllers = new ConcurrentDictionary<string, ITestToolController>();
    }

    public event EventHandler<TestSuiteStatusChangedEventArgs>? StatusChanged;
    public event EventHandler<TestStatusChangedEventArgs>? ToolStatusChanged;
    public event EventHandler<LogEventArgs>? LogReceived;

    public async Task<TestSuiteResult> ExecuteTestSuiteAsync(
        TestConfiguration configuration,
        CancellationToken cancellationToken,
        IProgress<ProgressEventArgs>? progress = null)
    {
        _logger.LogInformation("开始执行测试套件，目标驱动器: {TargetDrive}", configuration.TargetDrive);

        var suiteResult = new TestSuiteResult
        {
            StartTime = DateTime.Now,
            Configuration = configuration,
            TestResults = new List<TestResult>()
        };

        _currentTestSuite = suiteResult;
        _currentCancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);

        try
        {
            UpdateStatus(TestSuiteStatus.Running);
            //UpdateToolStatus(TestStatus.Preparing, configuration.TargetDrive);
            progress?.Report(new ProgressEventArgs { Progress = 0, Status = "准备测试环境..." });

            // 验证配置
            var (isValid, errors) = await ValidateConfigurationAsync(configuration);
            if (!isValid)
            {
                throw new InvalidOperationException($"配置验证失败: {string.Join(", ", errors)}");
            }

            // 获取选中的工具控制器
            var selectedControllers = new List<ITestToolController>();
            foreach (var toolName in configuration.SelectedTools)
            {
                var controller = GetToolController(toolName);
                if (controller != null)
                {
                    // 避免重复订阅 ToolStatusChanged
                    controller.StatusChanged -= ToolStatusChanged;
                    controller.StatusChanged += ToolStatusChanged;
                    
                    selectedControllers.Add(controller);
                }
                else
                {
                    _logger.LogWarning("未找到工具控制器: {ToolName}", toolName);
                }
            }

            if (!selectedControllers.Any())
            {
                throw new InvalidOperationException("没有可用的测试工具");
            }

            progress?.Report(new ProgressEventArgs { Progress = 10, Status = "开始执行测试..." });
            //UpdateToolStatus(TestStatus.Running, configuration.TargetDrive);
            // 执行测试
            var totalTests = selectedControllers.Count;
            var completedTests = 0;

            foreach (var controller in selectedControllers)
            {
                if (_currentCancellationTokenSource.Token.IsCancellationRequested)
                    break;

                try
                {
                    _logger.LogInformation("开始执行工具: {ToolName}", controller.ToolName);

                    var testProgress = new Progress<ProgressEventArgs>(args =>
                    {
                        var overallProgress = (completedTests * 100 + args.Progress) / totalTests;
                        progress?.Report(new ProgressEventArgs
                        {
                            Progress = overallProgress,
                            Status = $"[{controller.ToolName}] {args.Status}"
                        });
                    });

                    var testResult = await controller.ExecuteTestAsync(
                        configuration,
                        _currentCancellationTokenSource.Token,
                        testProgress);

                    suiteResult.TestResults.Add(testResult);
                    completedTests++;

                    _logger.LogInformation("工具 {ToolName} 执行完成，结果: {Success}",
                        controller.ToolName, testResult.Success);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "工具 {ToolName} 执行失败", controller.ToolName);

                    var failedResult = new TestResult
                    {
                        ToolName = controller.ToolName,
                        StartTime = DateTime.Now,
                        Success = false,
                        ErrorMessage = ex.Message,
                        Exception = ex
                    };
                    failedResult.Complete(false, ex.Message);
                    suiteResult.TestResults.Add(failedResult);
                    completedTests++;
                }
            }

            suiteResult.EndTime = DateTime.Now;
            var allTestsPassed = suiteResult.TestResults.Any() && suiteResult.TestResults.All(r => r.Success);

            UpdateStatus(allTestsPassed ? TestSuiteStatus.Completed : TestSuiteStatus.Failed);
            progress?.Report(new ProgressEventArgs { Progress = 100, Status = "测试套件执行完成" });

            //UpdateToolStatus(allTestsPassed ? TestStatus.Completed : TestStatus.Failed, configuration.TargetDrive);

            _logger.LogInformation("测试套件执行完成，成功: {Success}, 总测试数: {TotalTests}",
                allTestsPassed, suiteResult.TestResults.Count);
        }
        catch (OperationCanceledException)
        {
            suiteResult.EndTime = DateTime.Now;
            suiteResult.Cancel();
            UpdateStatus(TestSuiteStatus.Cancelled);
            //UpdateToolStatus(TestStatus.Cancelled, configuration.TargetDrive);
            _logger.LogInformation("测试套件已取消");
        }
        catch (Exception ex)
        {
            suiteResult.EndTime = DateTime.Now;
            suiteResult.Complete(ex.Message);
            UpdateStatus(TestSuiteStatus.Failed);
            //UpdateToolStatus(TestStatus.Failed, configuration.TargetDrive);
            _logger.LogError(ex, "测试套件执行失败");
        }
        finally
        {
            _currentTestSuite = null;
            _currentCancellationTokenSource?.Dispose();
            _currentCancellationTokenSource = null;
        }

        return suiteResult;
    }

    public async Task<List<ITestToolController>> GetAvailableToolsAsync()
    {
        _logger.LogDebug("获取可用的测试工具列表");

        var availableTools = new List<ITestToolController>();
        var toolNames = _controllerFactory.GetAvailableToolNames();

        foreach (var toolName in toolNames)
        {
            try
            {
                var controller = await _controllerFactory.CreateControllerAsync(toolName).ConfigureAwait(false);
                if (controller != null && controller.IsToolAvailable())
                {
                    availableTools.Add(controller);
                    _controllers.TryAdd(toolName, controller);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "创建工具控制器失败: {ToolName}", toolName);
            }
        }

        _logger.LogInformation("发现 {Count} 个可用的测试工具", availableTools.Count);
        return availableTools;
    }

    public ITestToolController? GetToolController(string toolName)
    {
        if (_controllers.TryGetValue(toolName, out var controller))
        {
            return controller;
        }

        try
        {
            // 注意：这里需要同步调用，但我们使用 GetAwaiter().GetResult() 来避免死锁
            controller = _controllerFactory.CreateControllerAsync(toolName).ConfigureAwait(false).GetAwaiter().GetResult();
            if (controller != null)
            {
                _controllers.TryAdd(toolName, controller);
                return controller;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "创建工具控制器失败: {ToolName}", toolName);
        }

        return null;
    }

    public async Task<(bool IsValid, List<string> Errors)> ValidateConfigurationAsync(TestConfiguration configuration)
    {
        var errors = new List<string>();

        // 验证目标驱动器
        if (string.IsNullOrWhiteSpace(configuration.TargetDrive))
        {
            errors.Add("目标驱动器不能为空");
        }
        else if (!Directory.Exists(configuration.TargetDrive))
        {
            errors.Add($"目标驱动器不存在: {configuration.TargetDrive}");
        }

        // 验证选中的工具
        if (!configuration.SelectedTools.Any())
        {
            errors.Add("至少需要选择一个测试工具");
        }

        // 验证每个工具的可用性
        foreach (var toolName in configuration.SelectedTools)
        {
            var controller = GetToolController(toolName);
            if (controller == null)
            {
                errors.Add($"工具控制器不存在: {toolName}");
            }
            else if (!controller.IsToolAvailable())
            {
                errors.Add($"工具不可用: {toolName}");
            }
        }

        return (errors.Count == 0, errors);
    }

    public async Task<TimeSpan> EstimateTestDurationAsync(TestConfiguration configuration)
    {
        // 简单的时间估算逻辑
        var baseTimePerTool = TimeSpan.FromMinutes(5); // 每个工具基础时间5分钟
        var totalTime = TimeSpan.FromTicks(baseTimePerTool.Ticks * configuration.SelectedTools.Count);

        _logger.LogDebug("估算测试时间: {Duration} 分钟", totalTime.TotalMinutes);
        return totalTime;
    }

    public async Task<bool> StopCurrentTestSuiteAsync()
    {
        if (_currentCancellationTokenSource != null && !_currentCancellationTokenSource.Token.IsCancellationRequested)
        {
            _logger.LogInformation("停止当前测试套件");
            _currentCancellationTokenSource.Cancel();
            UpdateStatus(TestSuiteStatus.Cancelled);
            return true;
        }
        return false;
    }

    public TestSuiteStatus GetCurrentStatus()
    {
        return _currentStatus;
    }

    public async Task<bool> PauseCurrentTestSuiteAsync()
    {
        // 暂停功能的简单实现
        _logger.LogInformation("暂停测试套件功能暂未实现");
        return false;
    }

    public async Task<bool> ResumeCurrentTestSuiteAsync()
    {
        // 恢复功能的简单实现
        _logger.LogInformation("恢复测试套件功能暂未实现");
        return false;
    }

    public async Task<List<TestSuiteResult>> GetTestHistoryAsync(int count = 10)
    {
        // 测试历史功能的简单实现
        _logger.LogDebug("获取测试历史记录功能暂未实现");
        return new List<TestSuiteResult>();
    }

    public async Task<bool> CleanupTestEnvironmentAsync(TestConfiguration configuration, CancellationToken cancellationToken)
    {
        _logger.LogInformation("清理测试环境");

        try
        {
            // 清理所有控制器
            foreach (var controller in _controllers.Values)
            {
                if (controller is IDisposable disposable)
                {
                    disposable.Dispose();
                }
            }
            _controllers.Clear();

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理测试环境失败");
            return false;
        }
    }

    private void UpdateStatus(TestSuiteStatus newStatus)
    {
        var oldStatus = _currentStatus;
        _currentStatus = newStatus;

        StatusChanged?.Invoke(this, new TestSuiteStatusChangedEventArgs
        {
            OldStatus = oldStatus,
            NewStatus = newStatus,
            Timestamp = DateTime.Now
        });

        _logger.LogDebug("测试套件状态变更: {OldStatus} -> {NewStatus}", oldStatus, newStatus);
    }

    // private void UpdateToolStatus(TestStatus newStatus, string targetDrive)
    // {
    //     var oldStatus = _currentToolStatus;
    //     _currentToolStatus = newStatus;
    //     ToolStatusChanged?.Invoke(this, new TestStatusChangedEventArgs
    //     {
    //         OldStatus = oldStatus,
    //         NewStatus = newStatus,
    //         TargetDrive = targetDrive,
    //         Timestamp = DateTime.Now
    //     });

    //     _logger.LogDebug("工具状态变更: {OldStatus} -> {NewStatus}", oldStatus, newStatus);
    // }
}
