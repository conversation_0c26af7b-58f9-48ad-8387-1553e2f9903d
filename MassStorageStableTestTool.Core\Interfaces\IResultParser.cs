using MassStorageStableTestTool.Core.Models;

namespace MassStorageStableTestTool.Core.Interfaces;

/// <summary>
/// 结果解析器接口
/// </summary>
/// <typeparam name="T">测试结果类型</typeparam>
public interface IResultParser<T> where T : TestResult
{
    /// <summary>
    /// 支持的工具名称
    /// </summary>
    string SupportedToolName { get; }

    /// <summary>
    /// 支持的输出格式
    /// </summary>
    List<string> SupportedFormats { get; }

    /// <summary>
    /// 从文本输出解析结果
    /// </summary>
    /// <param name="output">输出文本</param>
    /// <param name="format">输出格式</param>
    /// <returns>解析后的测试结果</returns>
    Task<T> ParseFromTextAsync(string output, string format = "text");

    /// <summary>
    /// 从文件解析结果
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="format">文件格式</param>
    /// <returns>解析后的测试结果</returns>
    Task<T> ParseFromFileAsync(string filePath, string? format = null);

    /// <summary>
    /// 从进程结果解析
    /// </summary>
    /// <param name="processResult">进程执行结果</param>
    /// <returns>解析后的测试结果</returns>
    Task<T> ParseFromProcessResultAsync(ProcessResult processResult);

    /// <summary>
    /// 验证输出格式是否受支持
    /// </summary>
    /// <param name="format">输出格式</param>
    /// <returns>是否支持</returns>
    bool IsFormatSupported(string format);

    /// <summary>
    /// 检查是否可以解析指定工具的结果
    /// </summary>
    /// <param name="toolName">工具名称</param>
    /// <returns>是否可以解析</returns>
    bool CanParseResult(string toolName);

    /// <summary>
    /// 获取解析器信息
    /// </summary>
    /// <returns>解析器信息</returns>
    ParserInfo GetParserInfo();

    /// <summary>
    /// 验证输出内容
    /// </summary>
    /// <param name="output">输出内容</param>
    /// <returns>验证结果</returns>
    (bool IsValid, List<string> Errors) ValidateOutput(string output);

    /// <summary>
    /// 提取关键性能指标
    /// </summary>
    /// <param name="output">输出内容</param>
    /// <returns>性能指标</returns>
    Task<PerformanceMetrics?> ExtractPerformanceMetricsAsync(string output);
}

/// <summary>
/// 通用结果解析器接口
/// </summary>
public interface IResultParser : IResultParser<TestResult>
{
}

/// <summary>
/// 解析器信息
/// </summary>
public class ParserInfo
{
    /// <summary>
    /// 解析器名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 解析器版本
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 支持的工具列表
    /// </summary>
    public List<string> SupportedTools { get; set; } = new();

    /// <summary>
    /// 支持的格式列表
    /// </summary>
    public List<string> SupportedFormats { get; set; } = new();

    /// <summary>
    /// 解析器描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 解析器作者
    /// </summary>
    public string Author { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 解析规则
    /// </summary>
    public Dictionary<string, string> ParsingRules { get; set; } = new();

    /// <summary>
    /// 示例输出
    /// </summary>
    public Dictionary<string, string> ExampleOutputs { get; set; } = new();
}

/// <summary>
/// 解析器工厂接口
/// </summary>
public interface IResultParserFactory
{
    /// <summary>
    /// 创建解析器
    /// </summary>
    /// <param name="toolName">工具名称</param>
    /// <returns>解析器实例</returns>
    IResultParser? CreateParser(string toolName);

    /// <summary>
    /// 获取所有可用的解析器
    /// </summary>
    /// <returns>解析器列表</returns>
    List<IResultParser> GetAllParsers();

    /// <summary>
    /// 注册解析器
    /// </summary>
    /// <param name="parser">解析器实例</param>
    void RegisterParser(IResultParser parser);

    /// <summary>
    /// 注册解析器类型
    /// </summary>
    /// <typeparam name="T">解析器类型</typeparam>
    /// <param name="toolName">工具名称</param>
    void RegisterParser<T>(string toolName) where T : class, IResultParser, new();

    /// <summary>
    /// 获取支持指定工具的解析器
    /// </summary>
    /// <param name="toolName">工具名称</param>
    /// <returns>解析器列表</returns>
    List<IResultParser> GetParsersForTool(string toolName);

    /// <summary>
    /// 检查是否有支持指定工具的解析器
    /// </summary>
    /// <param name="toolName">工具名称</param>
    /// <returns>是否有支持的解析器</returns>
    bool HasParserForTool(string toolName);
}

/// <summary>
/// 解析上下文
/// </summary>
public class ParseContext
{
    /// <summary>
    /// 工具名称
    /// </summary>
    public string ToolName { get; set; } = string.Empty;

    /// <summary>
    /// 输出格式
    /// </summary>
    public string Format { get; set; } = "text";

    /// <summary>
    /// 测试配置
    /// </summary>
    public TestConfiguration? Configuration { get; set; }

    /// <summary>
    /// 额外参数
    /// </summary>
    public Dictionary<string, object> Parameters { get; set; } = new();

    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;

    /// <summary>
    /// 文件编码
    /// </summary>
    public string Encoding { get; set; } = "UTF-8";

    /// <summary>
    /// 是否忽略错误
    /// </summary>
    public bool IgnoreErrors { get; set; } = false;

    /// <summary>
    /// 详细模式
    /// </summary>
    public bool VerboseMode { get; set; } = false;
}
