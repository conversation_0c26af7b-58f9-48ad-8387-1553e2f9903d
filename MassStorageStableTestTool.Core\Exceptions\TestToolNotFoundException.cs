namespace MassStorageStableTestTool.Core.Exceptions;

/// <summary>
/// 测试工具未找到异常
/// </summary>
public class TestToolNotFoundException : Exception
{
    /// <summary>
    /// 工具名称
    /// </summary>
    public string ToolName { get; }

    /// <summary>
    /// 工具路径
    /// </summary>
    public string? ToolPath { get; }

    /// <summary>
    /// 使用工具名称初始化异常。
    /// </summary>
    /// <param name="toolName">工具名称</param>
    public TestToolNotFoundException(string toolName) 
        : base($"测试工具 '{toolName}' 未找到")
    {
        ToolName = toolName;
    }

    /// <summary>
    /// 使用工具名称和工具路径初始化异常。
    /// </summary>
    /// <param name="toolName">工具名称</param>
    /// <param name="toolPath">工具路径</param>
    public TestToolNotFoundException(string toolName, string toolPath) 
        : base($"测试工具 '{toolName}' 在路径 '{toolPath}' 未找到")
    {
        ToolName = toolName;
        ToolPath = toolPath;
    }

    /// <summary>
    /// 使用工具名称和内部异常初始化异常。
    /// </summary>
    /// <param name="toolName">工具名称</param>
    /// <param name="innerException">内部异常</param>
    public TestToolNotFoundException(string toolName, Exception innerException) 
        : base($"测试工具 '{toolName}' 未找到", innerException)
    {
        ToolName = toolName;
    }

    /// <summary>
    /// 使用工具名称、工具路径和内部异常初始化异常。
    /// </summary>
    /// <param name="toolName">工具名称</param>
    /// <param name="toolPath">工具路径</param>
    /// <param name="innerException">内部异常</param>
    public TestToolNotFoundException(string toolName, string toolPath, Exception innerException) 
        : base($"测试工具 '{toolName}' 在路径 '{toolPath}' 未找到", innerException)
    {
        ToolName = toolName;
        ToolPath = toolPath;
    }
}
