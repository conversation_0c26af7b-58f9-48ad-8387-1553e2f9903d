using MassStorageStableTestTool.Core.Enums;
using MassStorageStableTestTool.Core.Interfaces;
using MassStorageStableTestTool.Core.Models;
using Microsoft.Extensions.Logging;
using MassStorageStableTestTool.Core.Exceptions;
using Microsoft.Extensions.DependencyInjection;
using System.Text.Json;

namespace MassStorageStableTestTool.Core.Common;

/// <summary>
/// 测试工具控制器基类，提供测试工具的通用控制逻辑。
/// </summary>
public abstract class BaseTestToolController : ITestToolController, IDisposable
{
    /// <summary>
    /// 工具配置字段
    /// </summary>
    protected readonly TestToolConfig _configuration;
    /// <summary>
    /// 当前测试状态。
    /// </summary>
    protected TestStatus _currentStatus = TestStatus.NotStarted;
    /// <summary>
    /// 取消令牌源。
    /// </summary>
    protected CancellationTokenSource? _cancellationTokenSource;
    /// <summary>
    /// 是否已释放资源。
    /// </summary>
    protected bool _disposed = false;
    /// <summary>
    /// 服务提供者（用于获取依赖服务）
    /// </summary>
    protected IServiceProvider? _serviceProvider;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="configuration">工具配置</param>
    protected BaseTestToolController(TestToolConfig configuration)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
    }

    /// <summary>
    /// 设置服务提供者
    /// </summary>
    /// <param name="serviceProvider">服务提供者</param>
    public virtual void SetServiceProvider(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    /// <summary>
    /// 工具名称。
    /// </summary>
    public abstract string ToolName { get; }

    /// <summary>
    /// 工具类型。
    /// </summary>
    public abstract TestToolType ToolType { get; }

    /// <summary>
    /// 工具配置。
    /// </summary>
    public TestToolConfig Configuration => _configuration;

    /// <summary>
    /// 状态变化事件。
    /// </summary>
    public event EventHandler<TestStatusChangedEventArgs>? StatusChanged;

    /// <summary>
    /// 日志事件。
    /// </summary>
    public event EventHandler<LogEventArgs>? LogReceived;

    /// <summary>
    /// 执行测试。
    /// </summary>
    /// <param name="config">测试配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <param name="progress">进度报告器</param>
    /// <returns>测试结果</returns>
    public async Task<TestResult> ExecuteTestAsync(
        TestConfiguration config,
        CancellationToken cancellationToken,
        IProgress<ProgressEventArgs>? progress = null)
    {
        var result = new TestResult
        {
            ToolName = ToolName,
            StartTime = DateTime.Now,
            Status = TestStatus.Running
        };

        try
        {
            // 验证配置
            var (isValid, errors) = ValidateConfiguration(config);
            if (!isValid)
            {
                throw new ConfigurationException($"配置验证失败: {string.Join(", ", errors)}");
            }

            // 检查工具可用性
            if (!IsToolAvailable())
            {
                throw new TestToolNotFoundException(ToolName, _configuration.ExecutablePath);
            }

            // 更新状态
            UpdateStatus(TestStatus.Preparing);
            progress?.Report(new ProgressEventArgs { Progress = 0, Status = "准备测试环境..." });

            // 准备测试环境
            var prepared = await PrepareTestEnvironmentAsync(config, cancellationToken);
            if (!prepared)
            {
                throw new TestExecutionException(ToolName, "准备测试环境失败");
            }

            progress?.Report(new ProgressEventArgs { Progress = 10, Status = "开始执行测试..." });

            // 更新状态
            UpdateStatus(TestStatus.Running);

            // 执行具体的测试逻辑
            result = await ExecuteTestInternalAsync(config, cancellationToken, progress);

            // 标记完成
            result.Complete(true);
            UpdateStatus(TestStatus.Completed);

            progress?.Report(new ProgressEventArgs { Progress = 100, Status = "测试完成" });

            LogInfo($"测试 {ToolName} 执行成功");
        }
        catch (OperationCanceledException)
        {
            result.Cancel();
            UpdateStatus(TestStatus.Cancelled);
            LogInfo($"测试 {ToolName} 已取消");
        }
        catch (TestTimeoutException ex)
        {
            result.Timeout();
            result.ErrorMessage = ex.Message;
            result.Exception = ex;
            UpdateStatus(TestStatus.Timeout);
            LogError($"测试 {ToolName} 超时", ex);
        }
        catch (Exception ex)
        {
            result.Complete(false, ex.Message);
            result.Exception = ex;
            UpdateStatus(TestStatus.Failed);
            LogError($"测试 {ToolName} 执行失败", ex);
        }
        finally
        {
            try
            {
                // 清理测试环境
                await CleanupTestEnvironmentAsync(config, CancellationToken.None);
            }
            catch (Exception ex)
            {
                LogWarning($"清理测试环境时出现异常: {ex.Message}");
            }
        }

        return result;
    }

    /// <summary>
    /// 执行具体的测试逻辑（由子类实现）。
    /// </summary>
    /// <param name="config">测试配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <param name="progress">进度报告器</param>
    /// <returns>测试结果</returns>
    protected abstract Task<TestResult> ExecuteTestInternalAsync(
        TestConfiguration config,
        CancellationToken cancellationToken,
        IProgress<ProgressEventArgs>? progress = null);

    /// <summary>
    /// 检查工具是否可用。
    /// </summary>
    /// <returns>是否可用</returns>
    public virtual bool IsToolAvailable()
    {
        try
        {
            LogInfo($"检查工具可用性: {ToolName}");
            LogInfo($"配置的可执行路径: {_configuration.ExecutablePath}");

            if (string.IsNullOrEmpty(_configuration.ExecutablePath))
            {
                LogWarning("可执行路径为空");
                return false;
            }

            // 使用改进的路径解析逻辑
            var resolvedPath = ResolveExecutablePath(_configuration.ExecutablePath);
            LogInfo($"解析后的路径: {resolvedPath}");

            var fileExists = File.Exists(resolvedPath);
            LogInfo($"文件是否存在: {fileExists}");

            return fileExists;
        }
        catch (Exception ex)
        {
            LogError($"检查工具可用性时出错: {ex.Message}", ex);
            return false;
        }
    }

    /// <summary>
    /// 解析可执行文件路径
    /// </summary>
    /// <param name="executablePath">配置的可执行文件路径</param>
    /// <returns>解析后的绝对路径</returns>
    protected virtual string ResolveExecutablePath(string executablePath)
    {
        if (string.IsNullOrEmpty(executablePath))
            return string.Empty;

        // 如果已经是绝对路径，直接返回
        if (Path.IsPathRooted(executablePath))
        {
            return executablePath;
        }

        // 对于相对路径，尝试多个基准目录
        var basePaths = new[]
        {
            Environment.CurrentDirectory,                    // 当前工作目录
            AppDomain.CurrentDomain.BaseDirectory,         // 应用程序基目录
            Path.GetDirectoryName(AppDomain.CurrentDomain.BaseDirectory), // 上级目录（解决方案根目录）
        };

        LogInfo($"尝试解析相对路径: {executablePath}");

        foreach (var basePath in basePaths)
        {
            if (!string.IsNullOrEmpty(basePath))
            {
                var testPath = Path.Combine(basePath, executablePath.TrimStart('.', '\\', '/'));
                LogInfo($"测试路径: {testPath}");

                if (File.Exists(testPath))
                {
                    var resolvedPath = Path.GetFullPath(testPath);
                    LogInfo($"找到文件，使用路径: {resolvedPath}");
                    return resolvedPath;
                }
            }
        }

        // 如果都找不到，使用默认的解析方式
        var defaultPath = Path.GetFullPath(executablePath);
        LogWarning($"所有基准路径都未找到文件，使用默认解析: {defaultPath}");
        return defaultPath;
    }

    /// <summary>
    /// 获取工具版本。
    /// </summary>
    /// <returns>工具版本</returns>
    public virtual async Task<string> GetToolVersionAsync()
    {
        try
        {
            if (!string.IsNullOrEmpty(_configuration.Version))
            {
                await Task.CompletedTask;
                return _configuration.Version;
            }

            // 尝试从文件版本信息获取
            if (File.Exists(_configuration.ExecutablePath))
            {
                var versionInfo = System.Diagnostics.FileVersionInfo.GetVersionInfo(_configuration.ExecutablePath);
                await Task.CompletedTask;
                return versionInfo.FileVersion ?? "Unknown";
            }

            await Task.CompletedTask;
            return "Unknown";
        }
        catch
        {
            await Task.CompletedTask;
            return "Unknown";
        }
    }

    /// <summary>
    /// 验证测试配置。
    /// </summary>
    /// <param name="config">测试配置</param>
    /// <returns>验证结果</returns>
    public virtual (bool IsValid, List<string> Errors) ValidateConfiguration(TestConfiguration config)
    {
        var errors = new List<string>();

        if (config == null)
        {
            errors.Add("测试配置不能为空");
            return (false, errors);
        }

        if (string.IsNullOrWhiteSpace(config.TargetDrive))
        {
            errors.Add("目标驱动器不能为空");
        }

        if (config.TimeoutSeconds <= 0)
        {
            errors.Add("超时时间必须大于0");
        }

        // 验证工具特定的配置
        var toolSpecificErrors = ValidateToolSpecificConfiguration(config);
        errors.AddRange(toolSpecificErrors);

        return (errors.Count == 0, errors);
    }

    /// <summary>
    /// 验证工具特定的配置（由子类重写）。
    /// </summary>
    /// <param name="config">测试配置</param>
    /// <returns>错误列表</returns>
    protected virtual List<string> ValidateToolSpecificConfiguration(TestConfiguration config)
    {
        return new List<string>();
    }

    /// <summary>
    /// 准备测试环境。
    /// </summary>
    /// <param name="config">测试配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>准备结果</returns>
    public virtual async Task<bool> PrepareTestEnvironmentAsync(TestConfiguration config, CancellationToken cancellationToken)
    {
        try
        {
            LogInfo($"准备 {ToolName} 测试环境");

            // 检查目标驱动器
            if (!Directory.Exists(config.TargetDrive))
            {
                LogError($"目标驱动器 {config.TargetDrive} 不存在或不可访问");
                return false;
            }

            // 检查磁盘空间
            var driveInfo = new System.IO.DriveInfo(config.TargetDrive);
            if (!driveInfo.IsReady)
            {
                LogError($"驱动器 {config.TargetDrive} 未就绪");
                return false;
            }

            // 如果启用了测试前格式化，则执行格式化
            if (config.FormatBeforeTest)
            {
                LogInfo("开始执行测试前磁盘格式化");
                var formatSuccess = await FormatDriveBeforeTestAsync(config, cancellationToken);
                if (!formatSuccess)
                {
                    LogError("磁盘格式化失败，无法继续测试");
                    return false;
                }
                LogInfo("磁盘格式化完成");
            }

            // 创建输出目录（如果需要）
            if (!string.IsNullOrEmpty(config.OutputDirectory))
            {
                Directory.CreateDirectory(config.OutputDirectory);
            }

            LogInfo($"{ToolName} 测试环境准备完成");
            return true;
        }
        catch (Exception ex)
        {
            LogError($"准备 {ToolName} 测试环境失败", ex);
            return false;
        }
    }

    /// <summary>
    /// 在测试前格式化驱动器
    /// </summary>
    /// <param name="config">测试配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>格式化是否成功</returns>
    protected virtual async Task<bool> FormatDriveBeforeTestAsync(TestConfiguration config, CancellationToken cancellationToken)
    {
        try
        {
            if (config.FormatBeforeTest == false)
            {
                LogInfo("测试配置未启用测试前格式化，跳过格式化步骤");
                return true;
            }

            // 尝试获取磁盘格式化服务
            var formatService = _serviceProvider?.GetService<IDiskFormatService>();
            if (formatService == null)
            {
                LogWarning("未找到磁盘格式化服务，跳过格式化步骤");
                return true; // 不阻止测试继续进行
            }

            LogInfo($"开始格式化驱动器 {config.TargetDrive}");

            // 检查驱动器是否可以格式化
            var (canFormat, issues) = await formatService.CheckDriveFormatabilityAsync(config.TargetDrive);
            if (!canFormat)
            {
                LogError($"驱动器不能格式化: {string.Join(", ", issues)}");
                return false;
            }

            // 执行格式化
            var progress = new Progress<ProgressEventArgs>(args =>
            {
                LogInfo($"格式化进度: {args.Progress}% - {args.Status}");
            });

            var formatResult = await formatService.FormatDriveAsync(
                config.TargetDrive,
                fileSystem: null, // 使用驱动器当前文件系统
                volumeLabel: null, // 使用驱动器当前卷标
                config.QuickFormat,
                cancellationToken,
                progress);

            if (formatResult.Success)
            {
                LogInfo($"驱动器格式化成功: {formatResult.FileSystem}, 卷标: {formatResult.VolumeLabel}, 耗时: {formatResult.Duration}");
                return true;
            }
            else
            {
                LogError($"驱动器格式化失败: {formatResult.ErrorMessage}");

                // 记录详细的格式化日志
                if (formatResult.Logs.Count > 0)
                {
                    LogInfo("格式化详细日志:");
                    foreach (var log in formatResult.Logs)
                    {
                        LogInfo($"  {log}");
                    }
                }

                // 如果有异常信息，也记录下来
                if (formatResult.Exception != null)
                {
                    LogError($"格式化异常详情: {formatResult.Exception}");
                }

                return false;
            }
        }
        catch (Exception ex)
        {
            LogError($"格式化驱动器时出现异常: {ex.Message}", ex);
            return false;
        }
    }

    /// <summary>
    /// 清理测试环境。
    /// </summary>
    /// <param name="config">测试配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理结果</returns>
    public virtual async Task<bool> CleanupTestEnvironmentAsync(TestConfiguration config, CancellationToken cancellationToken)
    {
        try
        {
            LogInfo($"清理 {ToolName} 测试环境");

            // 子类可以重写此方法进行特定的清理操作

            LogInfo($"{ToolName} 测试环境清理完成");
            await Task.CompletedTask;
            return true;
        }
        catch (Exception ex)
        {
            LogError($"清理 {ToolName} 测试环境失败", ex);
            await Task.CompletedTask;
            return false;
        }
    }

    /// <summary>
    /// 停止正在运行的测试。
    /// </summary>
    /// <returns>停止结果</returns>
    public virtual async Task<bool> StopTestAsync()
    {
        try
        {
            _cancellationTokenSource?.Cancel();
            UpdateStatus(TestStatus.Cancelled);
            LogInfo($"停止 {ToolName} 测试");
            await Task.CompletedTask;
            return true;
        }
        catch (Exception ex)
        {
            LogError($"停止 {ToolName} 测试失败", ex);
            await Task.CompletedTask;
            return false;
        }
    }

    /// <summary>
    /// 获取当前测试状态。
    /// </summary>
    /// <returns>测试状态</returns>
    public TestStatus GetCurrentStatus()
    {
        return _currentStatus;
    }

    /// <summary>
    /// 获取支持的参数列表。
    /// </summary>
    /// <returns>支持的参数</returns>
    public virtual Dictionary<string, ParameterInfo> GetSupportedParameters()
    {
        return new Dictionary<string, ParameterInfo>();
    }

    /// <summary>
    /// 更新状态。
    /// </summary>
    /// <param name="newStatus">新状态</param>
    protected virtual void UpdateStatus(TestStatus newStatus)
    {
        var oldStatus = _currentStatus;
        _currentStatus = newStatus;

        StatusChanged?.Invoke(this, new TestStatusChangedEventArgs
        {
            ToolName = ToolName,
            OldStatus = oldStatus,
            NewStatus = newStatus,
            Timestamp = DateTime.Now
        });
    }

    /// <summary>
    /// 记录信息日志。
    /// </summary>
    /// <param name="message">日志消息</param>
    protected virtual void LogInfo(string message)
    {
        LogReceived?.Invoke(this, new LogEventArgs
        {
            Level = LogLevel.Information,
            Message = message,
            Source = ToolName,
            Timestamp = DateTime.Now
        });
    }

    /// <summary>
    /// 记录警告日志。
    /// </summary>
    /// <param name="message">日志消息</param>
    protected virtual void LogWarning(string message)
    {
        LogReceived?.Invoke(this, new LogEventArgs
        {
            Level = LogLevel.Warning,
            Message = message,
            Source = ToolName,
            Timestamp = DateTime.Now
        });
    }

    /// <summary>
    /// 记录错误日志。
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="exception">异常信息</param>
    protected virtual void LogError(string message, Exception? exception = null)
    {
        LogReceived?.Invoke(this, new LogEventArgs
        {
            Level = LogLevel.Error,
            Message = message,
            Exception = exception,
            Source = ToolName,
            Timestamp = DateTime.Now
        });
    }

    /// <summary>
    /// 释放资源。
    /// </summary>
    public virtual void Dispose()
    {
        if (!_disposed)
        {
            _cancellationTokenSource?.Dispose();
            _disposed = true;
        }
    }

    /// <summary>
    /// 安全获取UI配置值
    /// </summary>
    /// <param name="key">配置键</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>配置值</returns>
    public string GetUIConfigValue(string key, string defaultValue)
    {
        try
        {
            if (_configuration.UIConfig.TryGetValue(key, out var value))
            {
                // 处理不同类型的值
                return value switch
                {
                    string strValue => strValue,
                    int intValue => intValue.ToString(),
                    long longValue => longValue.ToString(),
                    double doubleValue => doubleValue.ToString(),
                    _ => value?.ToString() ?? defaultValue
                };
            }
            return defaultValue;
        }
        catch
        {
            return defaultValue;
        }
    }

    /// <summary>
    /// 从配置中获取指定类型的值，如果不存在则返回默认值
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="config"></param>
    /// <param name="key"></param>
    /// <param name="defaultValue"></param>
    /// <returns></returns>
    protected T GetConfigValue<T>(TestConfiguration config, string key, T defaultValue)
    {
        var value = config.GetToolParameter(Configuration.Name, key, ExtractValue(Configuration.GetParameter(key, defaultValue)));
        return value is T tValue ? tValue : (T)Convert.ChangeType(value ?? defaultValue!, typeof(T));
    }
    
    /// <summary>
    /// 将有可能为非基本类型的Objet转换为基本类型object，例如jsonelement.string转为string
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public object ExtractValue(object? input)
    {
        if (input != null)
        {
            if (input is JsonElement jsonElement)
            {
                LogInfo($"Found JsonElement (ValueKind: {jsonElement.ValueKind}): ");
                switch (jsonElement.ValueKind)
                {
                    case JsonValueKind.String:
                        string? jsonString = jsonElement.GetString();
                        LogInfo($"'{jsonString}'");
                        // 额外处理：如果字符串看起来像日期时间，可以尝试解析
                        if (DateTime.TryParse(jsonString, out DateTime dateTimeValue))
                        {
                            LogInfo($"  (Also parsed as DateTime: {dateTimeValue})");
                            return dateTimeValue; // 优先返回 DateTime
                        }
                        return jsonString ?? string.Empty;

                    case JsonValueKind.Number:
                        // 尝试从最小的整数类型开始，如果失败再尝试更大的
                        if (jsonElement.TryGetInt32(out int jsonInt))
                        {
                            LogInfo($"{jsonInt} (int)");
                            return jsonInt;
                        }
                        if (jsonElement.TryGetInt64(out long jsonLong))
                        {
                            LogInfo($"{jsonLong} (long)");
                            return jsonLong;
                        }
                        if (jsonElement.TryGetDouble(out double jsonDouble)) // 包含浮点数
                        {
                            LogInfo($"{jsonDouble} (double)");
                            return jsonDouble;
                        }
                        // 如果以上都失败，可能是Decimal或其他大数
                        if (jsonElement.TryGetDecimal(out decimal jsonDecimal))
                        {
                            LogInfo($"{jsonDecimal} (decimal)");
                            return jsonDecimal;
                        }
                        LogError($"Could not parse number fully: {jsonElement.GetRawText()}");
                        return 0; // 返回默认值0，避免返回null引用

                    case JsonValueKind.True:
                    case JsonValueKind.False:
                        LogInfo($"{jsonElement.GetBoolean()} (bool)");
                        return jsonElement.GetBoolean();

                    case JsonValueKind.Null:
                        LogInfo("null");
                        return 0;

                    case JsonValueKind.Object:
                        LogInfo($"{{...}} (JsonElement Object)");
                        return jsonElement; // 返回 JsonElement 本身，以便后续处理其属性
                    case JsonValueKind.Array:
                        LogInfo($"[...] (JsonElement Array)");
                        return jsonElement; // 返回 JsonElement 本身，以便后续处理其元素

                    case JsonValueKind.Undefined:
                    default:
                        LogInfo("Undefined or other unhandled JsonValueKind.");
                        return 0;
                }
            }
        }

        return input ?? string.Empty;
    }
}
