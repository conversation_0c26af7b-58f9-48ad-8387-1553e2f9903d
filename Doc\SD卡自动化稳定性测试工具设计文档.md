# SD卡自动化稳定性测试工具 - GUI自动化设计文档

**版本:** 1.0  
**日期:** 2025年7月18日  
**技术方案:** C# + FlaUI GUI自动化  

---

## 1. 项目概述

### 1.1 技术背景
第三方测试软件的自动化支持情况各不相同：
- **无CLI支持的工具**（如H2testw、CrystalDiskMark等）：采用**GUI自动化技术**，使用C#语言结合FlaUI框架，通过Windows UI Automation API来模拟用户操作
- **有CLI支持的工具**（如fio、diskspd等）：采用**命令行调用技术**，直接通过Process类调用命令行接口
- **混合模式工具**：根据具体功能选择最适合的自动化方式

本项目设计了统一的测试控制器接口，能够同时支持GUI自动化和CLI调用两种模式，实现完全自动化的测试流程。

### 1.2 核心技术栈
- **开发语言:** C# (.NET 6.0+)
- **GUI框架:** WPF (Windows Presentation Foundation)
- **GUI自动化框架:** FlaUI (基于Windows UI Automation)
- **CLI调用框架:** System.Diagnostics.Process + 自定义封装
- **配置管理:** JSON配置文件
- **日志框架:** NLog
- **单元测试:** xUnit + Moq
- **依赖管理** NuGet

---

## 2. 系统架构设计

### 2.1 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    主应用程序 (WPF)                          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   UI控制层      │  │   业务逻辑层     │  │   数据访问层     │ │
│  │  (ViewModels)   │  │ (Services)      │  │ (Repositories)  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                   统一测试控制器接口                         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐                    ┌─────────────────┐  │
│  │ GUI自动化引擎   │                     │  CLI调用引擎    │  │
│  │   (FlaUI)       │                    │   (Process)     │  │
│  └─────────────────┘                    └─────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  应用程序控制器  │  │   结果解析器     │  │   报告生成器     │ │
│  │ (AppControllers)│  │ (ResultParsers) │  │(ReportGenerator)│ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    第三方测试工具                            │
│  GUI工具: H2testw | CrystalDiskMark | ATTO | HD Bench       │
│  CLI工具: fio | diskspd | dd | badblocks                    │
│  混合工具: HD Tune Pro | IOMeter | PassMark BurnInTest      │
└─────────────────────────────────────────────────────────────┘
```
**组件说明：**
- **主应用程序 (WPF)**: 用户界面，负责展示测试配置、进度、结果等信息
- **UI控制层 (ViewModels)**: 负责处理用户交互逻辑，与WPF界面绑定
- **业务逻辑层 (Services)**: 包含测试编排、配置管理、结果解析等核心业务逻辑
- **数据访问层 (Repositories)**: 负责数据的持久化和访问
- **自动化引擎 (FlaUI)**: 负责GUI工具的自动化控制
- **CLI调用引擎 (Process)**: 负责CLI工具的命令行调用
- **应用程序控制器 (AppControllers)**: 实现具体工具的自动化逻辑
- **结果解析器 (ResultParsers)**: 负责解析工具输出，提取关键性能指标
- **报告生成器 (ReportGenerator)**: 负责将解析结果整合成统一格式的报告


### 2.2 核心模块设计

#### 2.2.1 统一测试控制器接口
```csharp
// 核心自动化接口
public interface ITestToolController
{
    string ToolName { get; }
    TestToolType ToolType { get; }
    Task<TestResult> ExecuteTestAsync(TestConfiguration config, CancellationToken cancellationToken);
    bool IsToolAvailable();
    string GetToolVersion();
}

// 工具类型枚举
public enum TestToolType
{
    GUI,        // 需要GUI自动化
    CLI,        // 支持命令行调用
    Hybrid      // 混合模式
}

// 基础控制器抽象类
public abstract class BaseTestToolController : ITestToolController
{
    protected readonly ILogger _logger;
    protected readonly TestToolConfig _toolConfig;

    public abstract string ToolName { get; }
    public abstract TestToolType ToolType { get; }
    public abstract Task<TestResult> ExecuteTestAsync(TestConfiguration config, CancellationToken cancellationToken);

    public virtual bool IsToolAvailable()
    {
        return File.Exists(_toolConfig.ExecutablePath);
    }

    public virtual string GetToolVersion()
    {
        // 通用版本获取逻辑
    }
}
```

#### 2.2.2 GUI自动化控制器基类
```csharp
// GUI自动化控制器基类
public abstract class GuiTestToolController : BaseTestToolController
{
    protected readonly AutomationBase _automation;
    protected Application _application;

    protected GuiTestToolController(ILogger logger, TestToolConfig config) : base(logger, config)
    {
        _automation = new UIA3Automation();
    }

    public override TestToolType ToolType => TestToolType.GUI;

    protected virtual async Task<Application> LaunchApplicationAsync(string arguments = "")
    {
        // GUI应用程序启动逻辑
    }

    protected virtual async Task<Window> WaitForWindowAsync(string windowTitle, TimeSpan timeout)
    {
        // 等待窗口出现的通用逻辑
    }
}

// H2testw控制器示例（GUI模式）
public class H2testwController : GuiTestToolController
{
    public override async Task<TestResult> ExecuteTestAsync(TestConfiguration config, CancellationToken cancellationToken)
    {
        try
        {
            // 1. 启动H2testw应用程序
            var app = await LaunchApplicationAsync(_toolConfig.ExecutablePath);
            
            // 2. 获取主窗口
            var mainWindow = await WaitForWindowAsync("H2testw", TimeSpan.FromSeconds(30));
            
            // 3. 设置目标驱动器
            await SetTargetDriveAsync(mainWindow, config.TargetDrive);
            
            // 4. 配置测试参数
            await ConfigureTestParametersAsync(mainWindow, config);
            
            // 5. 开始测试
            await StartTestAsync(mainWindow);
            
            // 6. 监控测试进度
            var result = await MonitorTestProgressAsync(mainWindow, cancellationToken);
            
            // 7. 解析测试结果
            return await ParseTestResultAsync(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "H2testw测试执行失败");
            throw;
        }
    }
    
    private async Task SetTargetDriveAsync(Window window, string targetDrive)
    {
        // 使用FlaUI查找并操作驱动器选择控件
        var driveComboBox = window.FindFirstDescendant(cf => cf.ByControlType(ControlType.ComboBox));
        await driveComboBox.AsComboBox().SelectAsync(targetDrive);
    }
}
```

#### 2.2.3 CLI调用控制器基类
```csharp
// CLI调用控制器基类
public abstract class CliTestToolController : BaseTestToolController
{
    protected CliTestToolController(ILogger logger, TestToolConfig config) : base(logger, config)
    {
    }

    public override TestToolType ToolType => TestToolType.CLI;

    protected virtual async Task<ProcessResult> ExecuteCommandAsync(
        string arguments,
        CancellationToken cancellationToken,
        IProgress<string> progress = null)
    {
        var processStartInfo = new ProcessStartInfo
        {
            FileName = _toolConfig.ExecutablePath,
            Arguments = arguments,
            UseShellExecute = false,
            RedirectStandardOutput = true,
            RedirectStandardError = true,
            CreateNoWindow = true
        };

        using var process = new Process { StartInfo = processStartInfo };
        var outputBuilder = new StringBuilder();
        var errorBuilder = new StringBuilder();

        process.OutputDataReceived += (sender, e) =>
        {
            if (!string.IsNullOrEmpty(e.Data))
            {
                outputBuilder.AppendLine(e.Data);
                progress?.Report(e.Data);
            }
        };

        process.ErrorDataReceived += (sender, e) =>
        {
            if (!string.IsNullOrEmpty(e.Data))
            {
                errorBuilder.AppendLine(e.Data);
            }
        };

        process.Start();
        process.BeginOutputReadLine();
        process.BeginErrorReadLine();

        await process.WaitForExitAsync(cancellationToken);

        return new ProcessResult
        {
            ExitCode = process.ExitCode,
            StandardOutput = outputBuilder.ToString(),
            StandardError = errorBuilder.ToString()
        };
    }
}

// fio控制器示例（CLI模式）
public class FioController : CliTestToolController
{
    public override string ToolName => "fio";

    public FioController(ILogger<FioController> logger, TestToolConfig config)
        : base(logger, config)
    {
    }

    public override async Task<TestResult> ExecuteTestAsync(TestConfiguration config, CancellationToken cancellationToken)
    {
        var result = new TestResult
        {
            ToolName = ToolName,
            StartTime = DateTime.Now
        };

        try
        {
            // 构建fio命令参数
            var arguments = BuildFioArguments(config);

            // 执行命令
            var progress = new Progress<string>(line =>
            {
                _logger.LogInformation($"fio输出: {line}");
                // 可以在这里解析实时进度
            });

            var processResult = await ExecuteCommandAsync(arguments, cancellationToken, progress);

            if (processResult.ExitCode == 0)
            {
                // 解析输出结果
                result.Data = ParseFioOutput(processResult.StandardOutput);
                result.Success = true;
            }
            else
            {
                result.Success = false;
                result.ErrorMessage = processResult.StandardError;
            }
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.ErrorMessage = ex.Message;
            _logger.LogError(ex, $"{ToolName} 测试失败");
        }
        finally
        {
            result.EndTime = DateTime.Now;
        }

        return result;
    }

    private string BuildFioArguments(TestConfiguration config)
    {
        var args = new StringBuilder();

        // 基本参数
        args.Append($"--name=sdcard_test ");
        args.Append($"--filename={config.TargetDrive}\\testfile ");
        args.Append($"--size=1G ");
        args.Append($"--rw=randrw ");
        args.Append($"--bs=4k ");
        args.Append($"--numjobs=1 ");
        args.Append($"--runtime=300 ");
        args.Append($"--output-format=json ");

        // 从配置中获取自定义参数
        if (config.Parameters.ContainsKey("BlockSize"))
        {
            args.Replace("--bs=4k", $"--bs={config.Parameters["BlockSize"]}");
        }

        if (config.Parameters.ContainsKey("TestSize"))
        {
            args.Replace("--size=1G", $"--size={config.Parameters["TestSize"]}");
        }

        return args.ToString();
    }

    private Dictionary<string, object> ParseFioOutput(string output)
    {
        var data = new Dictionary<string, object>();

        try
        {
            // 解析JSON格式的fio输出
            var jsonDoc = JsonDocument.Parse(output);
            var jobs = jsonDoc.RootElement.GetProperty("jobs")[0];

            // 读取性能数据
            var read = jobs.GetProperty("read");
            data["ReadIOPS"] = read.GetProperty("iops").GetDouble();
            data["ReadBandwidth"] = read.GetProperty("bw").GetDouble();
            data["ReadLatency"] = read.GetProperty("lat_ns").GetProperty("mean").GetDouble();

            // 写入性能数据
            var write = jobs.GetProperty("write");
            data["WriteIOPS"] = write.GetProperty("iops").GetDouble();
            data["WriteBandwidth"] = write.GetProperty("bw").GetDouble();
            data["WriteLatency"] = write.GetProperty("lat_ns").GetProperty("mean").GetDouble();
        }
        catch (Exception ex)
        {
            _logger.LogWarning($"解析fio输出时出现异常: {ex.Message}");
            // 如果JSON解析失败，尝试文本解析
            data = ParseFioTextOutput(output);
        }

        return data;
    }

    private Dictionary<string, object> ParseFioTextOutput(string output)
    {
        var data = new Dictionary<string, object>();
        var lines = output.Split('\n');

        foreach (var line in lines)
        {
            // 解析文本格式的关键指标
            if (line.Contains("read:"))
            {
                var match = Regex.Match(line, @"BW=(\d+\.?\d*)(\w+)");
                if (match.Success)
                {
                    data["ReadBandwidth"] = double.Parse(match.Groups[1].Value);
                    data["ReadBandwidthUnit"] = match.Groups[2].Value;
                }
            }
            else if (line.Contains("write:"))
            {
                var match = Regex.Match(line, @"BW=(\d+\.?\d*)(\w+)");
                if (match.Success)
                {
                    data["WriteBandwidth"] = double.Parse(match.Groups[1].Value);
                    data["WriteBandwidthUnit"] = match.Groups[2].Value;
                }
            }
        }

        return data;
    }
}

// diskspd控制器示例（CLI模式）
public class DiskSpdController : CliTestToolController
{
    public override string ToolName => "diskspd";

    public DiskSpdController(ILogger<DiskSpdController> logger, TestToolConfig config)
        : base(logger, config)
    {
    }

    public override async Task<TestResult> ExecuteTestAsync(TestConfiguration config, CancellationToken cancellationToken)
    {
        var result = new TestResult
        {
            ToolName = ToolName,
            StartTime = DateTime.Now
        };

        try
        {
            // 构建diskspd命令参数
            var arguments = BuildDiskSpdArguments(config);

            var processResult = await ExecuteCommandAsync(arguments, cancellationToken);

            if (processResult.ExitCode == 0)
            {
                result.Data = ParseDiskSpdOutput(processResult.StandardOutput);
                result.Success = true;
            }
            else
            {
                result.Success = false;
                result.ErrorMessage = processResult.StandardError;
            }
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.ErrorMessage = ex.Message;
            _logger.LogError(ex, $"{ToolName} 测试失败");
        }
        finally
        {
            result.EndTime = DateTime.Now;
        }

        return result;
    }

    private string BuildDiskSpdArguments(TestConfiguration config)
    {
        var args = new StringBuilder();

        // 基本参数：随机4K读写，持续5分钟
        args.Append($"-b4K ");                    // 块大小4K
        args.Append($"-d300 ");                   // 持续时间300秒
        args.Append($"-r ");                      // 随机访问
        args.Append($"-w25 ");                    // 25%写入，75%读取
        args.Append($"-t4 ");                     // 4个线程
        args.Append($"-o32 ");                    // 每个线程32个未完成的I/O
        args.Append($"-W5 ");                     // 5秒预热时间
        args.Append($"-C5 ");                     // 5秒冷却时间
        args.Append($"-h ");                      // 禁用软件和硬件缓存
        args.Append($"{config.TargetDrive}\\testfile.dat");

        return args.ToString();
    }

    private Dictionary<string, object> ParseDiskSpdOutput(string output)
    {
        var data = new Dictionary<string, object>();
        var lines = output.Split('\n');

        foreach (var line in lines)
        {
            // 解析总体性能指标
            if (line.Contains("total:"))
            {
                var parts = line.Split('|');
                if (parts.Length >= 4)
                {
                    // 解析IOPS
                    var iopsMatch = Regex.Match(parts[1], @"(\d+\.?\d*)");
                    if (iopsMatch.Success)
                    {
                        data["TotalIOPS"] = double.Parse(iopsMatch.Groups[1].Value);
                    }

                    // 解析带宽
                    var bwMatch = Regex.Match(parts[2], @"(\d+\.?\d*)");
                    if (bwMatch.Success)
                    {
                        data["TotalBandwidth"] = double.Parse(bwMatch.Groups[1].Value);
                    }

                    // 解析延迟
                    var latMatch = Regex.Match(parts[3], @"(\d+\.?\d*)");
                    if (latMatch.Success)
                    {
                        data["AverageLatency"] = double.Parse(latMatch.Groups[1].Value);
                    }
                }
            }

            // 解析读取性能
            else if (line.Contains("Read IO"))
            {
                var match = Regex.Match(line, @"(\d+\.?\d*)\s+MiB/s");
                if (match.Success)
                {
                    data["ReadBandwidth"] = double.Parse(match.Groups[1].Value);
                }
            }

            // 解析写入性能
            else if (line.Contains("Write IO"))
            {
                var match = Regex.Match(line, @"(\d+\.?\d*)\s+MiB/s");
                if (match.Success)
                {
                    data["WriteBandwidth"] = double.Parse(match.Groups[1].Value);
                }
            }
        }

        return data;
    }
}
```

#### 2.2.4 混合模式控制器
```csharp
// 混合模式控制器基类
public abstract class HybridTestToolController : BaseTestToolController
{
    protected readonly AutomationBase _automation;
    protected Application _application;

    protected HybridTestToolController(ILogger logger, TestToolConfig config) : base(logger, config)
    {
        _automation = new UIA3Automation();
    }

    public override TestToolType ToolType => TestToolType.Hybrid;

    // 可以同时使用GUI和CLI方法
    protected virtual async Task<Application> LaunchApplicationAsync(string arguments = "")
    {
        // GUI启动逻辑
    }

    protected virtual async Task<ProcessResult> ExecuteCommandAsync(string arguments, CancellationToken cancellationToken)
    {
        // CLI执行逻辑
    }
}

// HD Tune Pro控制器示例（混合模式）
public class HdTuneProController : HybridTestToolController
{
    public override string ToolName => "HD Tune Pro";

    public HdTuneProController(ILogger<HdTuneProController> logger, TestToolConfig config)
        : base(logger, config)
    {
    }

    public override async Task<TestResult> ExecuteTestAsync(TestConfiguration config, CancellationToken cancellationToken)
    {
        // 根据配置决定使用GUI还是CLI模式
        if (config.Parameters.ContainsKey("UseCommandLine") &&
            (bool)config.Parameters["UseCommandLine"])
        {
            return await ExecuteCliModeAsync(config, cancellationToken);
        }
        else
        {
            return await ExecuteGuiModeAsync(config, cancellationToken);
        }
    }

    private async Task<TestResult> ExecuteCliModeAsync(TestConfiguration config, CancellationToken cancellationToken)
    {
        // 使用命令行模式执行
        var arguments = $"-drive {config.TargetDrive} -test benchmark -output results.txt";
        var processResult = await ExecuteCommandAsync(arguments, cancellationToken);

        // 解析命令行输出结果
        return ParseCliResult(processResult);
    }

    private async Task<TestResult> ExecuteGuiModeAsync(TestConfiguration config, CancellationToken cancellationToken)
    {
        // 使用GUI自动化模式执行
        var app = await LaunchApplicationAsync();
        var mainWindow = await WaitForMainWindowAsync(TimeSpan.FromSeconds(30));

        // GUI操作逻辑
        await SetTargetDriveAsync(mainWindow, config.TargetDrive);
        await StartBenchmarkAsync(mainWindow);
        await MonitorProgressAsync(mainWindow, cancellationToken);

        // 解析GUI结果
        return await ParseGuiResultAsync(mainWindow);
    }
}
```

---

## 3. 详细技术实现

### 3.1 FlaUI集成架构

#### 3.1.1 自动化会话管理
```csharp
public class AutomationSessionManager : IDisposable
{
    private readonly AutomationBase _automation;
    private readonly Dictionary<string, Application> _runningApplications;
    
    public AutomationSessionManager()
    {
        // 优先使用UIA3，回退到UIA2
        _automation = new UIA3Automation() ?? new UIA2Automation();
        _runningApplications = new Dictionary<string, Application>();
    }
    
    public async Task<Application> LaunchApplicationAsync(string executablePath, string arguments = "")
    {
        var processStartInfo = new ProcessStartInfo
        {
            FileName = executablePath,
            Arguments = arguments,
            UseShellExecute = true
        };
        
        var app = Application.Launch(processStartInfo);
        _runningApplications[Path.GetFileNameWithoutExtension(executablePath)] = app;
        
        return app;
    }
}
```

#### 3.1.2 UI元素识别策略
```csharp
public class UIElementFinder
{
    public static async Task<AutomationElement> FindElementWithRetryAsync(
        AutomationElement parent, 
        ConditionFactory condition, 
        TimeSpan timeout,
        TimeSpan retryInterval = default)
    {
        if (retryInterval == default) retryInterval = TimeSpan.FromMilliseconds(500);
        
        var endTime = DateTime.Now.Add(timeout);
        
        while (DateTime.Now < endTime)
        {
            var element = parent.FindFirstDescendant(condition);
            if (element != null) return element;
            
            await Task.Delay(retryInterval);
        }
        
        throw new ElementNotFoundException($"未能在{timeout}内找到指定UI元素");
    }
}
```

### 3.2 测试工具适配器设计

#### 3.2.1 CrystalDiskMark适配器
```csharp
public class CrystalDiskMarkController : BaseTestToolController
{
    private readonly CrystalDiskMarkConfig _config;
    
    public override async Task<TestResult> ExecuteTestAsync(TestConfiguration config, CancellationToken cancellationToken)
    {
        var app = await LaunchApplicationAsync(_toolConfig.ExecutablePath);
        var mainWindow = await WaitForWindowAsync("CrystalDiskMark", TimeSpan.FromSeconds(30));
        
        // 设置测试参数
        await SetTestSizeAsync(mainWindow, config.TestSize);
        await SetTestCountAsync(mainWindow, config.TestCount);
        await SetTargetDriveAsync(mainWindow, config.TargetDrive);
        
        // 开始测试
        var startButton = await UIElementFinder.FindElementWithRetryAsync(
            mainWindow, 
            cf => cf.ByName("All").And(cf.ByControlType(ControlType.Button)),
            TimeSpan.FromSeconds(10));
            
        await startButton.AsButton().InvokeAsync();
        
        // 监控测试进度
        return await MonitorCrystalDiskMarkProgressAsync(mainWindow, cancellationToken);
    }
    
    private async Task<TestResult> MonitorCrystalDiskMarkProgressAsync(Window window, CancellationToken cancellationToken)
    {
        var progressBar = window.FindFirstDescendant(cf => cf.ByControlType(ControlType.ProgressBar));
        
        while (!cancellationToken.IsCancellationRequested)
        {
            var progress = progressBar.AsProgressBar().Value;
            
            // 报告进度
            OnProgressChanged?.Invoke(new ProgressEventArgs { Progress = progress });
            
            // 检查是否完成
            if (progress >= 100) break;
            
            await Task.Delay(1000, cancellationToken);
        }
        
        // 解析结果
        return await ParseCrystalDiskMarkResultAsync(window);
    }
}
```

### 3.3 结果解析引擎

#### 3.3.1 通用结果解析接口
```csharp
public interface IResultParser<T> where T : TestResult
{
    Task<T> ParseResultAsync(Window applicationWindow);
    Task<T> ParseResultFromFileAsync(string filePath);
    bool CanParseResult(string toolName);
}

public abstract class BaseResultParser<T> : IResultParser<T> where T : TestResult
{
    protected readonly ILogger _logger;
    
    public abstract Task<T> ParseResultAsync(Window applicationWindow);
    public abstract Task<T> ParseResultFromFileAsync(string filePath);
    public abstract bool CanParseResult(string toolName);
    
    protected virtual async Task<string> ExtractTextFromUIElementAsync(AutomationElement element)
    {
        // 从UI元素提取文本的通用方法
        return element.AsTextBox()?.Text ?? element.Name ?? string.Empty;
    }
}
```

#### 3.3.2 H2testw结果解析器
```csharp
public class H2testwResultParser : BaseResultParser<H2testwResult>
{
    public override async Task<H2testwResult> ParseResultAsync(Window applicationWindow)
    {
        var result = new H2testwResult();
        
        // 查找结果显示区域
        var resultTextBox = applicationWindow.FindFirstDescendant(
            cf => cf.ByControlType(ControlType.Edit).And(cf.ByName("Result")));
            
        if (resultTextBox != null)
        {
            var resultText = await ExtractTextFromUIElementAsync(resultTextBox);
            result = ParseH2testwText(resultText);
        }
        
        return result;
    }
    
    private H2testwResult ParseH2testwText(string resultText)
    {
        var result = new H2testwResult();
        
        // 解析写入速度
        var writeSpeedMatch = Regex.Match(resultText, @"Writing speed: ([\d.]+) MByte/s");
        if (writeSpeedMatch.Success)
        {
            result.WriteSpeed = double.Parse(writeSpeedMatch.Groups[1].Value);
        }
        
        // 解析读取速度
        var readSpeedMatch = Regex.Match(resultText, @"Reading speed: ([\d.]+) MByte/s");
        if (readSpeedMatch.Success)
        {
            result.ReadSpeed = double.Parse(readSpeedMatch.Groups[1].Value);
        }
        
        // 解析测试结果
        result.TestPassed = resultText.Contains("Test finished without errors");
        
        return result;
    }
}
```

---

## 4. 主应用程序设计

### 4.1 WPF主界面架构
```csharp
public class MainViewModel : ViewModelBase
{
    private readonly ITestOrchestrator _testOrchestrator;
    private readonly IConfigurationService _configService;
    private readonly IReportService _reportService;
    
    public ObservableCollection<DriveInfo> AvailableDrives { get; set; }
    public ObservableCollection<TestToolViewModel> TestTools { get; set; }
    public string CurrentStatus { get; set; }
    public double OverallProgress { get; set; }
    
    public ICommand StartTestCommand { get; }
    public ICommand StopTestCommand { get; }
    public ICommand RefreshDrivesCommand { get; }
    
    public async Task StartTestAsync()
    {
        var selectedTools = TestTools.Where(t => t.IsSelected).ToList();
        var selectedDrive = SelectedDrive;
        
        var testConfiguration = new TestConfiguration
        {
            TargetDrive = selectedDrive.Name,
            SelectedTools = selectedTools.Select(t => t.ToolName).ToList(),
            TestParameters = BuildTestParameters()
        };
        
        await _testOrchestrator.ExecuteTestSuiteAsync(testConfiguration, CancellationToken.None);
    }
}
```

### 4.2 测试编排器
```csharp
public class TestOrchestrator : ITestOrchestrator
{
    private readonly IEnumerable<ITestToolController> _controllers;
    private readonly IReportGenerator _reportGenerator;
    private readonly ILogger _logger;
    
    public async Task<TestSuiteResult> ExecuteTestSuiteAsync(
        TestConfiguration config, 
        CancellationToken cancellationToken)
    {
        var suiteResult = new TestSuiteResult
        {
            StartTime = DateTime.Now,
            Configuration = config,
            TestResults = new List<TestResult>()
        };
        
        foreach (var toolName in config.SelectedTools)
        {
            if (cancellationToken.IsCancellationRequested) break;
            
            var controller = _controllers.FirstOrDefault(c => c.GetType().Name.StartsWith(toolName));
            if (controller == null)
            {
                _logger.LogWarning($"未找到{toolName}的控制器");
                continue;
            }
            
            try
            {
                OnStatusChanged?.Invoke($"正在执行 {toolName} 测试...");
                
                var testResult = await controller.ExecuteTestAsync(config, cancellationToken);
                suiteResult.TestResults.Add(testResult);
                
                OnStatusChanged?.Invoke($"{toolName} 测试完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"{toolName} 测试失败");
                suiteResult.TestResults.Add(new TestResult 
                { 
                    ToolName = toolName, 
                    Success = false, 
                    ErrorMessage = ex.Message 
                });
            }
        }
        
        suiteResult.EndTime = DateTime.Now;
        
        // 生成报告
        await _reportGenerator.GenerateReportAsync(suiteResult);
        
        return suiteResult;
    }
}
```

---

## 5. 配置管理系统

### 5.1 配置文件结构
```json
{
  "TestTools": {
    "H2testw": {
      "Type": "GUI",
      "ExecutablePath": "./third_party_tools/h2testw.exe",
      "WindowTitle": "H2testw",
      "DefaultParameters": {
        "TestAllSpace": true,
        "VerifyData": true
      },
      "Timeouts": {
        "LaunchTimeout": 30,
        "TestTimeout": 7200
      }
    },
    "CrystalDiskMark": {
      "Type": "GUI",
      "ExecutablePath": "./third_party_tools/CrystalDiskMark.exe",
      "WindowTitle": "CrystalDiskMark",
      "DefaultParameters": {
        "TestSize": "1GiB",
        "TestCount": 5
      },
      "Timeouts": {
        "LaunchTimeout": 30,
        "TestTimeout": 1800
      }
    },
    "fio": {
      "Type": "CLI",
      "ExecutablePath": "./third_party_tools/fio.exe",
      "DefaultParameters": {
        "BlockSize": "4k",
        "TestSize": "1G",
        "Runtime": 300,
        "ReadWriteRatio": "randrw",
        "OutputFormat": "json"
      },
      "Timeouts": {
        "TestTimeout": 600
      }
    },
    "diskspd": {
      "Type": "CLI",
      "ExecutablePath": "./third_party_tools/diskspd.exe",
      "DefaultParameters": {
        "BlockSize": "4K",
        "Duration": 300,
        "Threads": 4,
        "OutstandingIO": 32,
        "WritePercentage": 25
      },
      "Timeouts": {
        "TestTimeout": 600
      }
    },
    "dd": {
      "Type": "CLI",
      "ExecutablePath": "C:\\msys64\\usr\\bin\\dd.exe",
      "DefaultParameters": {
        "BlockSize": "4M",
        "Count": 256,
        "InputFile": "/dev/zero",
        "Flags": "direct"
      },
      "Timeouts": {
        "TestTimeout": 1800
      }
    },
    "HDTunePro": {
      "Type": "Hybrid",
      "ExecutablePath": "./third_party_tools/HDTunePro.exe",
      "WindowTitle": "HD Tune Pro",
      "DefaultParameters": {
        "UseCommandLine": false,
        "TestType": "benchmark",
        "FileSize": "1GB"
      },
      "Timeouts": {
        "LaunchTimeout": 30,
        "TestTimeout": 1800
      }
    },
    "IOMeter": {
      "Type": "Hybrid",
      "ExecutablePath": "./third_party_tools/IOMeter.exe",
      "WindowTitle": "Iometer",
      "DefaultParameters": {
        "UseCommandLine": true,
        "ConfigFile": "./configs/sdcard_test.icf",
        "Duration": 300
      },
      "Timeouts": {
        "LaunchTimeout": 30,
        "TestTimeout": 600
      }
    }
  },
  "Reporting": {
    "OutputDirectory": "./Reports",
    "FileNameTemplate": "{DriveLabel}_{DateTime:yyyy-MM-dd_HH-mm-ss}_Report",
    "IncludeSystemInfo": true,
    "Formats": ["txt", "csv", "json"]
  },
  "Logging": {
    "LogLevel": "Information",
    "LogDirectory": "./Logs",
    "EnableConsoleOutput": true
  },
  "Performance": {
    "MaxConcurrentTests": 1,
    "ProcessCleanupTimeout": 30,
    "MemoryThreshold": 80
  }
}
```

### 5.2 配置服务实现
```csharp
public class ConfigurationService : IConfigurationService
{
    private readonly string _configFilePath;
    private TestToolConfiguration _configuration;
    
    public async Task<TestToolConfiguration> LoadConfigurationAsync()
    {
        if (!File.Exists(_configFilePath))
        {
            _configuration = CreateDefaultConfiguration();
            await SaveConfigurationAsync(_configuration);
        }
        else
        {
            var json = await File.ReadAllTextAsync(_configFilePath);
            _configuration = JsonSerializer.Deserialize<TestToolConfiguration>(json);
        }
        
        return _configuration;
    }
    
    private TestToolConfiguration CreateDefaultConfiguration()
    {
        return new TestToolConfiguration
        {
            TestTools = new Dictionary<string, TestToolConfig>
            {
                ["H2testw"] = new TestToolConfig
                {
                    ExecutablePath = "./third_party_tools/h2testw.exe",
                    WindowTitle = "H2testw",
                    DefaultParameters = new Dictionary<string, object>
                    {
                        ["TestAllSpace"] = true,
                        ["VerifyData"] = true
                    }
                }
            }
        };
    }
}
```

---

## 6. 错误处理与恢复机制

### 6.1 异常处理策略
```csharp
public class RobustTestController : BaseTestToolController
{
    private readonly int _maxRetryAttempts = 3;
    private readonly TimeSpan _retryDelay = TimeSpan.FromSeconds(5);
    
    public override async Task<TestResult> ExecuteTestAsync(TestConfiguration config, CancellationToken cancellationToken)
    {
        for (int attempt = 1; attempt <= _maxRetryAttempts; attempt++)
        {
            try
            {
                return await ExecuteTestInternalAsync(config, cancellationToken);
            }
            catch (Exception ex) when (attempt < _maxRetryAttempts)
            {
                _logger.LogWarning($"测试执行失败，第{attempt}次重试: {ex.Message}");
                
                // 清理可能残留的进程
                await CleanupProcessesAsync();
                
                await Task.Delay(_retryDelay, cancellationToken);
            }
        }
        
        // 最后一次尝试，不捕获异常
        return await ExecuteTestInternalAsync(config, cancellationToken);
    }
    
    private async Task CleanupProcessesAsync()
    {
        // 强制关闭可能卡住的测试工具进程
        var processesToKill = new[] { "h2testw", "CrystalDiskMark", "ATTO", "HDTune" };
        
        foreach (var processName in processesToKill)
        {
            var processes = Process.GetProcessesByName(processName);
            foreach (var process in processes)
            {
                try
                {
                    process.Kill();
                    await process.WaitForExitAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogWarning($"无法终止进程 {processName}: {ex.Message}");
                }
            }
        }
    }
}
```

---

## 7. 项目结构

```
MassStorageStableTestTool/
├── src/
│   ├── MassStorageStableTestTool.Core/          # 核心业务逻辑
│   │   ├── Interfaces/                          # 接口定义
│   │   │   ├── ITestToolController.cs           # 统一控制器接口
│   │   │   ├── IResultParser.cs                 # 结果解析器接口
│   │   │   └── ITestOrchestrator.cs             # 测试编排器接口
│   │   ├── Models/                              # 数据模型
│   │   │   ├── TestConfiguration.cs             # 测试配置模型
│   │   │   ├── TestResult.cs                    # 测试结果模型
│   │   │   ├── ProcessResult.cs                 # 进程执行结果
│   │   │   └── TestToolConfig.cs                # 工具配置模型
│   │   ├── Services/                            # 业务服务
│   │   │   ├── ConfigurationService.cs          # 配置管理服务
│   │   │   ├── ReportService.cs                 # 报告服务
│   │   │   └── SystemInfoService.cs             # 系统信息服务
│   │   ├── Enums/                               # 枚举定义
│   │   │   ├── TestToolType.cs                  # 工具类型枚举
│   │   │   └── TestStatus.cs                    # 测试状态枚举
│   │   └── Exceptions/                          # 自定义异常
│   │       ├── TestToolNotFoundException.cs     # 工具未找到异常
│   │       └── TestExecutionException.cs        # 测试执行异常
│   ├── MassStorageStableTestTool.Automation/    # 自动化引擎
│   │   ├── GUI/                                 # GUI自动化组件
│   │   │   ├── Controllers/                     # GUI控制器
│   │   │   │   ├── GuiTestToolController.cs     # GUI控制器基类
│   │   │   │   ├── H2testwController.cs         # H2testw控制器
│   │   │   │   ├── CrystalDiskMarkController.cs # CrystalDiskMark控制器
│   │   │   │   └── AttoController.cs            # ATTO控制器
│   │   │   ├── Common/                          # 通用GUI组件
│   │   │   │   ├── UIElementFinder.cs           # UI元素查找器
│   │   │   │   ├── AutomationSessionManager.cs  # 自动化会话管理
│   │   │   │   └── WindowManager.cs             # 窗口管理器
│   │   │   └── Extensions/                      # FlaUI扩展
│   │   │       ├── AutomationElementExtensions.cs
│   │   │       └── WindowExtensions.cs
│   │   ├── CLI/                                 # CLI调用组件
│   │   │   ├── Controllers/                     # CLI控制器
│   │   │   │   ├── CliTestToolController.cs     # CLI控制器基类
│   │   │   │   ├── FioController.cs             # fio控制器
│   │   │   │   ├── DiskSpdController.cs         # diskspd控制器
│   │   │   │   └── DdController.cs              # dd控制器
│   │   │   ├── Common/                          # 通用CLI组件
│   │   │   │   ├── ProcessExecutor.cs           # 进程执行器
│   │   │   │   ├── CommandBuilder.cs            # 命令构建器
│   │   │   │   └── OutputParser.cs              # 输出解析器
│   │   │   └── Parsers/                         # CLI结果解析器
│   │   │       ├── FioOutputParser.cs           # fio输出解析器
│   │   │       ├── DiskSpdOutputParser.cs       # diskspd输出解析器
│   │   │       └── DdOutputParser.cs            # dd输出解析器
│   │   ├── Hybrid/                              # 混合模式组件
│   │   │   ├── Controllers/                     # 混合控制器
│   │   │   │   ├── HybridTestToolController.cs  # 混合控制器基类
│   │   │   │   ├── HdTuneProController.cs       # HD Tune Pro控制器
│   │   │   │   └── IOMeterController.cs         # IOMeter控制器
│   │   │   └── Common/                          # 混合模式通用组件
│   │   │       └── ModeSelector.cs              # 模式选择器
│   │   └── Common/                              # 通用自动化组件
│   │       ├── TestOrchestrator.cs              # 测试编排器
│   │       ├── ControllerFactory.cs             # 控制器工厂
│   │       └── ResultAggregator.cs              # 结果聚合器
│   ├── MassStorageStableTestTool.UI/           # WPF用户界面
│   │   ├── Views/                              # 视图
│   │   │   ├── MainWindow.xaml                 # 主窗口
│   │   │   ├── TestConfigurationView.xaml      # 测试配置视图
│   │   │   ├── TestProgressView.xaml           # 测试进度视图
│   │   │   └── ReportView.xaml                 # 报告视图
│   │   ├── ViewModels/                         # 视图模型
│   │   │   ├── MainViewModel.cs                # 主视图模型
│   │   │   ├── TestConfigurationViewModel.cs   # 配置视图模型
│   │   │   ├── TestProgressViewModel.cs        # 进度视图模型
│   │   │   └── ReportViewModel.cs              # 报告视图模型
│   │   ├── Controls/                           # 自定义控件
│   │   │   ├── TestToolSelector.xaml           # 测试工具选择器
│   │   │   ├── DriveSelector.xaml              # 驱动器选择器
│   │   │   └── ProgressIndicator.xaml          # 进度指示器
│   │   ├── Converters/                         # 值转换器
│   │   │   ├── BoolToVisibilityConverter.cs    # 布尔到可见性转换器
│   │   │   └── TestStatusToColorConverter.cs   # 状态到颜色转换器
│   │   └── Resources/                          # 资源文件
│   │       ├── Styles.xaml                     # 样式定义
│   │       └── Icons/                          # 图标资源
│   └── MassStorageStableTestTool.Reports/      # 报告生成
│       ├── Generators/                         # 报告生成器
│       │   ├── TextReportGenerator.cs          # 文本报告生成器
│       │   ├── CsvReportGenerator.cs           # CSV报告生成器
│       │   └── JsonReportGenerator.cs          # JSON报告生成器
│       ├── Templates/                          # 报告模板
│       │   ├── comprehensive_report.template   # 综合报告模板
│       │   ├── summary_report.template         # 摘要报告模板
│       │   └── detailed_report.template        # 详细报告模板
│       └── Exporters/                          # 导出器
│           ├── FileExporter.cs                 # 文件导出器
│           └── EmailExporter.cs                # 邮件导出器
├── tests/                                      # 单元测试
│   ├── MassStorageStableTestTool.Core.Tests/   # 核心模块测试
│   ├── MassStorageStableTestTool.Automation.Tests/ # 自动化模块测试
│   ├── MassStorageStableTestTool.UI.Tests/     # UI模块测试
│   └── MassStorageStableTestTool.Reports.Tests/ # 报告模块测试
├── third_party_tools/                          # 第三方测试工具
│   ├── GUI/                                    # GUI工具
│   │   ├── h2testw.exe                         # H2testw
│   │   ├── CrystalDiskMark.exe                 # CrystalDiskMark
│   │   └── ATTO.exe                            # ATTO Disk Benchmark
│   ├── CLI/                                    # CLI工具
│   │   ├── fio.exe                             # fio
│   │   ├── diskspd.exe                         # diskspd
│   │   └── dd.exe                              # dd (from msys2)
│   └── Hybrid/                                 # 混合模式工具
│       ├── HDTunePro.exe                       # HD Tune Pro
│       └── IOMeter.exe                         # IOMeter
├── config/                                     # 配置文件
│   ├── appsettings.json                        # 主配置文件
│   ├── NLog.config                             # 日志配置
│   └── test_profiles/                          # 测试配置文件
│       ├── quick_test.json                     # 快速测试配置
│       ├── comprehensive_test.json             # 全面测试配置
│       └── stress_test.json                    # 压力测试配置
├── docs/                                       # 文档
│   ├── user_manual.md                          # 用户手册
│   ├── developer_guide.md                      # 开发者指南
│   └── api_reference.md                        # API参考
└── scripts/                                    # 构建脚本
    ├── build.ps1                               # 构建脚本
    ├── test.ps1                                # 测试脚本
    └── deploy.ps1                              # 部署脚本
```

---

## 8. 报告生成系统

### 8.1 报告生成器架构
```csharp
public interface IReportGenerator
{
    Task<string> GenerateReportAsync(TestSuiteResult suiteResult);
    Task<string> GenerateReportAsync(TestSuiteResult suiteResult, ReportFormat format);
    Task ExportReportAsync(string reportContent, string filePath, ReportFormat format);
}

public class ComprehensiveReportGenerator : IReportGenerator
{
    private readonly ISystemInfoService _systemInfoService;
    private readonly ITemplateEngine _templateEngine;

    public async Task<string> GenerateReportAsync(TestSuiteResult suiteResult)
    {
        var reportData = new ReportData
        {
            TestSuite = suiteResult,
            SystemInfo = await _systemInfoService.GetSystemInfoAsync(),
            GeneratedAt = DateTime.Now
        };

        var template = await LoadReportTemplateAsync("comprehensive_report.template");
        return await _templateEngine.RenderAsync(template, reportData);
    }
}
```

### 8.2 报告模板示例
```
===============================================
SD卡稳定性测试报告
===============================================

测试信息:
- 测试日期: {{GeneratedAt:yyyy-MM-dd HH:mm:ss}}
- 目标驱动器: {{TestSuite.Configuration.TargetDrive}}
- 测试工具: {{#each TestSuite.TestResults}}{{ToolName}}{{#unless @last}}, {{/unless}}{{/each}}

系统信息:
- 操作系统: {{SystemInfo.OperatingSystem}}
- 处理器: {{SystemInfo.Processor}}
- 内存: {{SystemInfo.TotalMemory}} GB

===============================================
测试结果汇总
===============================================

总体状态: {{#if TestSuite.AllTestsPassed}}通过{{else}}失败{{/if}}
测试时长: {{TestSuite.Duration}}

{{#each TestSuite.TestResults}}
--- {{ToolName}} ---
状态: {{#if Success}}通过{{else}}失败{{/if}}
{{#if Success}}
{{#if WriteSpeed}}写入速度: {{WriteSpeed}} MB/s{{/if}}
{{#if ReadSpeed}}读取速度: {{ReadSpeed}} MB/s{{/if}}
{{else}}
错误信息: {{ErrorMessage}}
{{/if}}

{{/each}}
```

---

## 9. 部署与分发

### 9.1 应用程序打包
```xml
<!-- MassStorageStableTestTool.UI.csproj -->
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>true</SelfContained>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <PublishReadyToRun>true</PublishReadyToRun>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FlaUI.Core" Version="4.0.0" />
    <PackageReference Include="FlaUI.UIA3" Version="4.0.0" />
    <PackageReference Include="NLog" Version="5.2.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>
</Project>
```

### 9.2 安装包制作
```powershell
# 发布脚本 (publish.ps1)
param(
    [string]$Configuration = "Release",
    [string]$OutputPath = "./dist"
)

Write-Host "开始构建SD卡自动化测试工具..."

# 清理输出目录
if (Test-Path $OutputPath) {
    Remove-Item $OutputPath -Recurse -Force
}
New-Item -ItemType Directory -Path $OutputPath

# 发布应用程序
dotnet publish src/MassStorageStableTestTool.UI/MassStorageStableTestTool.UI.csproj `
    -c $Configuration `
    -o "$OutputPath/app" `
    --self-contained true `
    -r win-x64

# 复制第三方工具
Copy-Item "third_party_tools" "$OutputPath/app/third_party_tools" -Recurse

# 复制配置文件
Copy-Item "config/appsettings.json" "$OutputPath/app/"

# 创建启动脚本
@"
@echo off
cd /d "%~dp0"
MassStorageStableTestTool.UI.exe
pause
"@ | Out-File "$OutputPath/app/启动测试工具.bat" -Encoding ASCII

Write-Host "构建完成! 输出目录: $OutputPath"
```

---

## 10. 测试策略

### 10.1 单元测试
```csharp
[Fact]
public async Task H2testwController_ExecuteTest_ShouldReturnValidResult()
{
    // Arrange
    var mockLogger = new Mock<ILogger<H2testwController>>();
    var mockConfig = new TestToolConfig
    {
        ExecutablePath = "test_h2testw.exe",
        WindowTitle = "H2testw"
    };

    var controller = new H2testwController(mockLogger.Object, mockConfig);
    var testConfig = new TestConfiguration
    {
        TargetDrive = "E:",
        TestSize = "1GB"
    };

    // Act
    var result = await controller.ExecuteTestAsync(testConfig, CancellationToken.None);

    // Assert
    Assert.NotNull(result);
    Assert.Equal("H2testw", result.ToolName);
}
```

### 10.2 集成测试
```csharp
[Fact]
public async Task TestOrchestrator_ExecuteFullTestSuite_ShouldGenerateReport()
{
    // Arrange
    var orchestrator = CreateTestOrchestrator();
    var config = new TestConfiguration
    {
        TargetDrive = "E:",
        SelectedTools = new[] { "H2testw", "CrystalDiskMark" }
    };

    // Act
    var result = await orchestrator.ExecuteTestSuiteAsync(config, CancellationToken.None);

    // Assert
    Assert.True(result.TestResults.Count >= 2);
    Assert.True(File.Exists(result.ReportFilePath));
}
```

---

## 11. 性能优化

### 11.1 UI响应性优化
```csharp
public class AsyncTestViewModel : ViewModelBase
{
    private readonly SemaphoreSlim _testSemaphore = new(1, 1);

    public async Task StartTestAsync()
    {
        if (!await _testSemaphore.WaitAsync(100))
        {
            ShowMessage("测试正在进行中，请稍候...");
            return;
        }

        try
        {
            IsTestRunning = true;

            // 在后台线程执行测试
            await Task.Run(async () =>
            {
                var progress = new Progress<ProgressEventArgs>(OnProgressChanged);
                await _testOrchestrator.ExecuteTestSuiteAsync(_configuration, _cancellationTokenSource.Token, progress);
            });
        }
        finally
        {
            IsTestRunning = false;
            _testSemaphore.Release();
        }
    }

    private void OnProgressChanged(ProgressEventArgs args)
    {
        // 切换到UI线程更新进度
        Application.Current.Dispatcher.Invoke(() =>
        {
            CurrentProgress = args.Progress;
            CurrentStatus = args.Status;
        });
    }
}
```

### 11.2 内存管理
```csharp
public class ResourceManagedController : BaseTestToolController, IDisposable
{
    private readonly List<IDisposable> _disposables = new();
    private bool _disposed = false;

    protected override async Task<Application> LaunchApplicationAsync(string executablePath, string arguments = "")
    {
        var app = await base.LaunchApplicationAsync(executablePath, arguments);
        _disposables.Add(app);
        return app;
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            foreach (var disposable in _disposables)
            {
                try
                {
                    disposable?.Dispose();
                }
                catch (Exception ex)
                {
                    _logger.LogWarning($"资源释放时出现异常: {ex.Message}");
                }
            }

            _disposables.Clear();
            _disposed = true;
        }
    }
}
```

---

## 12. 安全考虑

### 12.1 权限管理
```csharp
public class SecurityService : ISecurityService
{
    public async Task<bool> ValidateExecutableAsync(string executablePath)
    {
        // 验证可执行文件的数字签名
        var fileInfo = new FileInfo(executablePath);
        if (!fileInfo.Exists) return false;

        // 检查文件是否在允许的目录中
        var allowedDirectories = new[]
        {
            Path.GetFullPath("./third_party_tools"),
            Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles),
            Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86)
        };

        var fullPath = Path.GetFullPath(executablePath);
        return allowedDirectories.Any(dir => fullPath.StartsWith(dir, StringComparison.OrdinalIgnoreCase));
    }

    public async Task<bool> CheckDriveAccessAsync(string driveLetter)
    {
        try
        {
            var drive = new DriveInfo(driveLetter);
            return drive.IsReady && drive.DriveType == DriveType.Removable;
        }
        catch
        {
            return false;
        }
    }
}
```

---

## 13. 监控与日志

### 13.1 日志配置
```xml
<!-- NLog.config -->
<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

  <targets>
    <target xsi:type="File" name="fileTarget"
            fileName="logs/MassStorageTestTool-${shortdate}.log"
            layout="${longdate} ${level:uppercase=true} ${logger} ${message} ${exception:format=tostring}" />

    <target xsi:type="Console" name="consoleTarget"
            layout="${time} [${level}] ${message}" />
  </targets>

  <rules>
    <logger name="*" minlevel="Info" writeTo="fileTarget" />
    <logger name="*" minlevel="Debug" writeTo="consoleTarget" />
  </rules>
</nlog>
```

### 13.2 性能监控
```csharp
public class PerformanceMonitor : IPerformanceMonitor
{
    private readonly ILogger _logger;
    private readonly PerformanceCounter _cpuCounter;
    private readonly PerformanceCounter _memoryCounter;

    public async Task<SystemPerformance> GetCurrentPerformanceAsync()
    {
        return new SystemPerformance
        {
            CpuUsage = _cpuCounter.NextValue(),
            MemoryUsage = _memoryCounter.NextValue(),
            Timestamp = DateTime.Now
        };
    }

    public async Task MonitorTestPerformanceAsync(CancellationToken cancellationToken)
    {
        while (!cancellationToken.IsCancellationRequested)
        {
            var performance = await GetCurrentPerformanceAsync();

            if (performance.CpuUsage > 90 || performance.MemoryUsage > 80)
            {
                _logger.LogWarning($"系统资源使用率过高: CPU {performance.CpuUsage:F1}%, 内存 {performance.MemoryUsage:F1}%");
            }

            await Task.Delay(5000, cancellationToken);
        }
    }
}
```

---

## 14. 总结

本设计文档详细描述了基于C# FlaUI的SD卡自动化稳定性测试工具的完整技术方案。主要特点包括：

### 14.1 技术优势
- **GUI自动化**: 使用FlaUI框架实现对第三方工具的完全自动化控制
- **模块化设计**: 采用清晰的分层架构，便于维护和扩展
- **错误恢复**: 完善的异常处理和重试机制，确保测试的可靠性
- **配置驱动**: 通过配置文件管理工具参数，无需重新编译

### 14.2 实施建议
1. **分阶段开发**: 建议先实现H2testw和CrystalDiskMark的自动化，验证技术可行性
2. **充分测试**: 在不同版本的测试工具上验证GUI自动化的稳定性
3. **用户培训**: 提供详细的用户手册和操作视频
4. **持续维护**: 建立版本管理机制，及时适配第三方工具的更新

### 14.3 风险评估
- **GUI变化风险**: 第三方工具界面更新可能影响自动化脚本
- **性能风险**: 长时间运行可能导致内存泄漏或系统资源耗尽
- **兼容性风险**: 不同Windows版本或系统配置可能影响自动化效果

通过本设计方案，可以实现一个稳定、可靠、易用的SD卡自动化测试工具，大幅提升测试效率和结果的一致性。

## 15. 附录：开发工具
- **Inspect.exe / FlaUIInspenct:** 用于探查和识别Windows UI 元素的属性，是编写FlaUI脚本的**必备工具**。
- **Visual Studio Code**：主要开发工具。
- **Visual Studio 2019**: 用于构建、调试、发布。
