namespace MassStorageStableTestTool.Core.Models;

/// <summary>
/// 进程执行结果模型
/// </summary>
public class ProcessResult
{
    /// <summary>
    /// 进程退出码
    /// </summary>
    public int ExitCode { get; set; }

    /// <summary>
    /// 标准输出
    /// </summary>
    public string StandardOutput { get; set; } = string.Empty;

    /// <summary>
    /// 标准错误输出
    /// </summary>
    public string StandardError { get; set; } = string.Empty;

    /// <summary>
    /// 进程开始时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 进程结束时间
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 进程运行时间
    /// </summary>
    public TimeSpan Duration => EndTime - StartTime;

    /// <summary>
    /// 进程ID
    /// </summary>
    public int ProcessId { get; set; }

    /// <summary>
    /// 进程名称
    /// </summary>
    public string ProcessName { get; set; } = string.Empty;

    /// <summary>
    /// 命令行参数
    /// </summary>
    public string Arguments { get; set; } = string.Empty;

    /// <summary>
    /// 工作目录
    /// </summary>
    public string WorkingDirectory { get; set; } = string.Empty;

    /// <summary>
    /// 是否成功执行（退出码为0）
    /// </summary>
    public bool IsSuccess => ExitCode == 0;

    /// <summary>
    /// 是否有错误输出
    /// </summary>
    public bool HasError => !string.IsNullOrEmpty(StandardError);

    /// <summary>
    /// 是否有标准输出
    /// </summary>
    public bool HasOutput => !string.IsNullOrEmpty(StandardOutput);

    /// <summary>
    /// 环境变量
    /// </summary>
    public Dictionary<string, string> EnvironmentVariables { get; set; } = new();

    /// <summary>
    /// 获取所有输出（标准输出 + 错误输出）
    /// </summary>
    /// <returns>合并的输出</returns>
    public string GetAllOutput()
    {
        var output = new List<string>();
        
        if (HasOutput)
        {
            output.Add("=== 标准输出 ===");
            output.Add(StandardOutput);
        }
        
        if (HasError)
        {
            output.Add("=== 错误输出 ===");
            output.Add(StandardError);
        }
        
        return string.Join(Environment.NewLine, output);
    }

    /// <summary>
    /// 获取输出行列表
    /// </summary>
    /// <param name="includeEmpty">是否包含空行</param>
    /// <returns>输出行列表</returns>
    public List<string> GetOutputLines(bool includeEmpty = false)
    {
        var lines = StandardOutput.Split(new[] { '\r', '\n' }, StringSplitOptions.None);
        
        if (!includeEmpty)
        {
            lines = lines.Where(line => !string.IsNullOrWhiteSpace(line)).ToArray();
        }
        
        return lines.ToList();
    }

    /// <summary>
    /// 获取错误行列表
    /// </summary>
    /// <param name="includeEmpty">是否包含空行</param>
    /// <returns>错误行列表</returns>
    public List<string> GetErrorLines(bool includeEmpty = false)
    {
        var lines = StandardError.Split(new[] { '\r', '\n' }, StringSplitOptions.None);
        
        if (!includeEmpty)
        {
            lines = lines.Where(line => !string.IsNullOrWhiteSpace(line)).ToArray();
        }
        
        return lines.ToList();
    }

    /// <summary>
    /// 搜索输出中的特定模式
    /// </summary>
    /// <param name="pattern">搜索模式（正则表达式）</param>
    /// <param name="searchInError">是否也在错误输出中搜索</param>
    /// <returns>匹配的行列表</returns>
    public List<string> SearchOutput(string pattern, bool searchInError = false)
    {
        var matches = new List<string>();
        var regex = new System.Text.RegularExpressions.Regex(pattern, System.Text.RegularExpressions.RegexOptions.IgnoreCase);
        
        // 搜索标准输出
        foreach (var line in GetOutputLines())
        {
            if (regex.IsMatch(line))
            {
                matches.Add(line);
            }
        }
        
        // 搜索错误输出
        if (searchInError)
        {
            foreach (var line in GetErrorLines())
            {
                if (regex.IsMatch(line))
                {
                    matches.Add(line);
                }
            }
        }
        
        return matches;
    }

    /// <summary>
    /// 转换为字典格式
    /// </summary>
    /// <returns>进程结果字典</returns>
    public Dictionary<string, object> ToDictionary()
    {
        return new Dictionary<string, object>
        {
            ["ExitCode"] = ExitCode,
            ["IsSuccess"] = IsSuccess,
            ["ProcessName"] = ProcessName,
            ["Arguments"] = Arguments,
            ["StartTime"] = StartTime,
            ["EndTime"] = EndTime,
            ["Duration"] = Duration.TotalSeconds,
            ["HasOutput"] = HasOutput,
            ["HasError"] = HasError,
            ["OutputLineCount"] = GetOutputLines().Count,
            ["ErrorLineCount"] = GetErrorLines().Count
        };
    }

    /// <summary>
    /// 创建成功的进程结果
    /// </summary>
    /// <param name="output">标准输出</param>
    /// <param name="processName">进程名称</param>
    /// <returns>成功的进程结果</returns>
    public static ProcessResult Success(string output, string processName = "")
    {
        return new ProcessResult
        {
            ExitCode = 0,
            StandardOutput = output,
            ProcessName = processName,
            StartTime = DateTime.Now.AddSeconds(-1),
            EndTime = DateTime.Now
        };
    }

    /// <summary>
    /// 创建失败的进程结果
    /// </summary>
    /// <param name="exitCode">退出码</param>
    /// <param name="error">错误信息</param>
    /// <param name="processName">进程名称</param>
    /// <returns>失败的进程结果</returns>
    public static ProcessResult Failure(int exitCode, string error, string processName = "")
    {
        return new ProcessResult
        {
            ExitCode = exitCode,
            StandardError = error,
            ProcessName = processName,
            StartTime = DateTime.Now.AddSeconds(-1),
            EndTime = DateTime.Now
        };
    }
}
