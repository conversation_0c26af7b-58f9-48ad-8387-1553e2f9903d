using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using MassStorageStableTestTool.Core.Common;
using MassStorageStableTestTool.Core.Enums;
using MassStorageStableTestTool.Core.Models;
using MassStorageStableTestTool.Core.Interfaces;
using MassStorageStableTestTool.Automation.GUI;
using FlaUI.Core.AutomationElements;
using System.Text.RegularExpressions;

namespace MassStorageStableTestTool.Automation.Controllers
{
    /// <summary>
    /// CrystalDiskMark测试工具控制器
    /// 负责自动化控制CrystalDiskMark进行磁盘性能测试
    /// </summary>
    public class CrystalDiskMarkController : BaseTestToolController
    {
        private readonly AutomationHelper _automationHelper;
        private readonly ILogger<CrystalDiskMarkController> _logger;
        private Process? _crystalDiskMarkProcess;
        private Window? _crystalDiskMarkWindow;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="configuration">工具配置</param>
        /// <param name="automationHelper">自动化助手</param>
        /// <param name="logger">日志记录器</param>
        public CrystalDiskMarkController(
            TestToolConfig configuration,
            AutomationHelper automationHelper,
            ILogger<CrystalDiskMarkController> logger)
            : base(configuration)
        {
            _automationHelper = automationHelper ?? throw new ArgumentNullException(nameof(automationHelper));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 工具名称
        /// </summary>
        public override string ToolName => "CrystalDiskMark";

        /// <summary>
        /// 工具类型
        /// </summary>
        public override TestToolType ToolType => TestToolType.GUI;

        /// <summary>
        /// 执行具体的测试逻辑
        /// </summary>
        /// <param name="config">测试配置</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <param name="progress">进度报告器</param>
        /// <returns>测试结果</returns>
        protected override async Task<TestResult> ExecuteTestInternalAsync(
            TestConfiguration config,
            CancellationToken cancellationToken,
            IProgress<ProgressEventArgs>? progress = null)
        {
            var result = new TestResult
            {
                ToolName = ToolName,
                StartTime = DateTime.Now
            };

            try
            {
                // 启动CrystalDiskMark程序
                progress?.Report(new ProgressEventArgs { Progress = 10, Status = "启动CrystalDiskMark程序..." });
                if (!await StartCrystalDiskMarkAsync(cancellationToken))
                {
                    throw new Exception("启动CrystalDiskMark程序失败");
                }

                // 等待窗口出现
                progress?.Report(new ProgressEventArgs { Progress = 20, Status = "等待CrystalDiskMark窗口..." });
                if (_crystalDiskMarkProcess != null)
                {
                    _crystalDiskMarkWindow = _automationHelper.WaitForWindowByProcess(_crystalDiskMarkProcess.Id, "CrystalDiskMark", _configuration.Timeouts.LaunchTimeout);
                }
                else
                {
                    _crystalDiskMarkWindow = _automationHelper.WaitForWindow("CrystalDiskMark", _configuration.Timeouts.LaunchTimeout);
                }

                if (_crystalDiskMarkWindow == null)
                {
                    throw new Exception("CrystalDiskMark窗口未出现");
                }

                // 配置测试参数
                progress?.Report(new ProgressEventArgs { Progress = 30, Status = "配置测试参数..." });
                if (!await ConfigureTestParametersAsync(config, cancellationToken))
                {
                    throw new Exception("配置测试参数失败");
                }

                // 选择目标驱动器
                progress?.Report(new ProgressEventArgs { Progress = 40, Status = "选择目标驱动器..." });
                if (!await SelectTargetDriveAsync(config, cancellationToken))
                {
                    throw new Exception("选择目标驱动器失败");
                }
              
                // 开始测试
                progress?.Report(new ProgressEventArgs { Progress = 50, Status = "开始性能测试..." });
                await StartTestAsync(config, cancellationToken);

                // 监控测试进度
                progress?.Report(new ProgressEventArgs { Progress = 60, Status = "监控测试进度..." });
                await MonitorTestProgressAsync(cancellationToken, progress);

                // 解析测试结果
                progress?.Report(new ProgressEventArgs { Progress = 90, Status = "解析测试结果..." });
                await ParseTestResultsAsync(config, result);

                result.Success = true;
                result.EndTime = DateTime.Now;
                progress?.Report(new ProgressEventArgs { Progress = 100, Status = "测试完成" });

                _logger.LogInformation("CrystalDiskMark测试完成，耗时: {Duration}", result.Duration);
            }
            catch (OperationCanceledException)
            {
                result.Success = false;
                result.ErrorMessage = "测试被用户取消";
                result.EndTime = DateTime.Now;
                _logger.LogWarning("CrystalDiskMark测试被取消");
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
                result.EndTime = DateTime.Now;
                _logger.LogError(ex, "CrystalDiskMark测试失败: {Message}", ex.Message);
            }
            finally
            {
                await CleanupAsync();
            }

            return result;
        }

        /// <summary>
        /// 启动CrystalDiskMark程序
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>启动结果</returns>
        private async Task<bool> StartCrystalDiskMarkAsync(CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("启动CrystalDiskMark程序: {ExecutablePath}", _configuration.ExecutablePath);

                var absolutePath = Path.GetFullPath(_configuration.ExecutablePath);
                if (!File.Exists(absolutePath))
                {
                    _logger.LogError("CrystalDiskMark可执行文件不存在: {Path}", absolutePath);
                    return false;
                }

                var workingDirectory = Path.GetDirectoryName(absolutePath);
                var startInfo = new ProcessStartInfo
                {
                    FileName = absolutePath,
                    UseShellExecute = false,
                    WorkingDirectory = workingDirectory
                };

                _crystalDiskMarkProcess = Process.Start(startInfo);
                if (_crystalDiskMarkProcess == null)
                {
                    _logger.LogError("无法启动CrystalDiskMark进程");
                    return false;
                }

                // 等待进程启动
                await Task.Delay(3000, cancellationToken);

                _logger.LogInformation("CrystalDiskMark进程已启动，PID: {ProcessId}", _crystalDiskMarkProcess.Id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启动CrystalDiskMark程序失败: {Message}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 配置测试参数
        /// </summary>
        /// <param name="config">测试配置</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>配置是否成功</returns>
        private async Task<bool> ConfigureTestParametersAsync(TestConfiguration config, CancellationToken cancellationToken)
        {
            try
            {
                if (_crystalDiskMarkWindow == null)
                    return false;

                _logger.LogInformation("配置CrystalDiskMark测试参数");

                // 设置测试大小
                var testSize = config.GetToolParameter(ToolName, "TestSize", _configuration.DefaultParameters.GetValueOrDefault("TestSize", 6));
                if (testSize != null)
                {
                    var success = await SetTestSizeAsync(ExtractValue(testSize), cancellationToken);
                    if (!success)
                    {
                        _logger.LogWarning("设置测试大小失败!");
                        return false;
                    }
                }

                // 设置测试次数
                var testCount = config.GetToolParameter(ToolName, "TestCount", _configuration.DefaultParameters.GetValueOrDefault("TestCount", 5));
                if (testCount != null)
                {
                    var success = await SetTestCountAsync(ExtractValue(testCount), cancellationToken);
                    if (!success)
                    {
                        _logger.LogWarning("设置测试次数失败，将使用默认值");
                        return false;
                    }
                }

                // 设置测试单位
                var testUnit = config.GetToolParameter(ToolName, "TestUnit", _configuration.DefaultParameters.GetValueOrDefault("TestUnit", "MB/s"));
                if (testUnit != null)
                {
                    var success = await SetTestUnitAsync(ExtractValue(testUnit), cancellationToken);
                    if (!success)
                    {
                        _logger.LogWarning("设置测试单位失败，将使用默认值");
                        return false;
                    }
                }

                _logger.LogInformation("CrystalDiskMark测试参数配置完成");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "配置测试参数失败: {Message}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 设置测试大小
        /// </summary>
        /// <param name="testSize">测试大小</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>设置是否成功</returns>
        private Task<bool> SetTestSizeAsync(object testSize, CancellationToken cancellationToken)
        {
            try
            {
                if (_crystalDiskMarkWindow != null)
                {
                    var TestSizeComboBoxAutoId = GetUIConfigValue("TestSizeComboBoxAutoId", "1028");
                    if (string.IsNullOrEmpty(TestSizeComboBoxAutoId))
                    {
                        _logger.LogWarning("未找到测试大小设置控件的自动化ID配置");
                        return Task.FromResult(false);
                    }

                    if(!_automationHelper.FindAndSelectComboBoxOption(_crystalDiskMarkWindow, TestSizeComboBoxAutoId, testSize, TimeSpan.FromSeconds(5)))
                    {
                        _logger.LogWarning("设置测试大小失败!");
                        return Task.FromResult(false);
                    }
                }

                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置测试大小失败: {Message}", ex.Message);
                return Task.FromResult(false);
            }
        }

        /// <summary>
        /// 设置测试次数
        /// </summary>
        /// <param name="testCount">测试次数</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>设置是否成功</returns>
        private Task<bool> SetTestCountAsync(object testCount, CancellationToken cancellationToken)
        {
            try
            {
                if (_crystalDiskMarkWindow != null)
                {
                    var TestCountComboBoxAutoId = GetUIConfigValue("TestCountComboBoxAutoId", "1026");
                    if (string.IsNullOrEmpty(TestCountComboBoxAutoId))
                    {
                        _logger.LogWarning("未找到测试次数设置控件的自动化ID配置");
                        return Task.FromResult(false);
                    }

                    if(!_automationHelper.FindAndSelectComboBoxOption(_crystalDiskMarkWindow, TestCountComboBoxAutoId, testCount, TimeSpan.FromSeconds(5)))
                    {
                        _logger.LogWarning("设置测试次数失败!");
                        return Task.FromResult(false);
                    }
                }

                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置测试次数失败: {Message}", ex.Message);
                return Task.FromResult(false);
            }
        }

        /// <summary>
        /// 设置测试单位
        /// </summary>
        /// <param name="testUnit">测试单位</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>设置是否成功</returns>
        private Task<bool> SetTestUnitAsync(object testUnit, CancellationToken cancellationToken)
        {
            try
            {
                // 查找测试单位下拉框并设置值
                if (_crystalDiskMarkWindow == null)
                    return Task.FromResult(false);

                var TestUnitComboBoxAutoId = GetUIConfigValue("TestUnitComboBoxAutoId", "1025");
                if (string.IsNullOrEmpty(TestUnitComboBoxAutoId))
                {
                    _logger.LogWarning("未找到测试单位设置控件的自动化ID配置");
                    return Task.FromResult(false);
                }
                
                if (!_automationHelper.FindAndSelectComboBoxOption(_crystalDiskMarkWindow, TestUnitComboBoxAutoId, testUnit, TimeSpan.FromSeconds(5)))
                {
                    _logger.LogWarning("设置测试单位失败!");
                    return Task.FromResult(false);
                }

                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置测试单位失败: {Message}", ex.Message);
                return Task.FromResult(false);
            }
        }

        /// <summary>
        /// 选择目标驱动器
        /// </summary>
        /// <param name="config">测试配置</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>选择是否成功</returns>
        private async Task<bool> SelectTargetDriveAsync(TestConfiguration config, CancellationToken cancellationToken)
        {
            try
            {
                if (_crystalDiskMarkWindow == null)
                    return false;

                _logger.LogInformation("选择目标驱动器: {TargetDrive}", config.TargetDrive);

                var targetDrive = config.TargetDrive;
                //这里的TargetDrive可能是J:\我只需要提取前面J:
                if (targetDrive.EndsWith("\\"))
                    targetDrive = targetDrive.Substring(0, targetDrive.Length - 1);

                // 查找驱动器选择下拉框
                var DriveComboBoxAutoId = GetUIConfigValue("DriveComboBoxAutoId", "1027");
                if (string.IsNullOrEmpty(DriveComboBoxAutoId))
                {
                    _logger.LogWarning("未找到驱动器选择控件的自动化ID配置");
                    return false;
                }

                if(!_automationHelper.FindAndSelectComboBoxOption(_crystalDiskMarkWindow, DriveComboBoxAutoId, targetDrive as object, TimeSpan.FromSeconds(5)))
                {
                    _logger.LogWarning("选择目标驱动器失败!");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "选择目标驱动器失败: {Message}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 开始测试
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>开始任务</returns>
        private async Task StartTestAsync(TestConfiguration config, CancellationToken cancellationToken)
        {
            try
            {
                if (_crystalDiskMarkWindow == null)
                    return;

                _logger.LogInformation("开始CrystalDiskMark性能测试");
                var TestModeButtonName = config.GetToolParameter(ToolName, "TestMode", "All") as string;
                if (string.IsNullOrEmpty(TestModeButtonName))
                {
                    _logger.LogWarning("未找到测试模式按钮名称配置");
                    return;
                }
                _logger.LogInformation("测试模式按钮名称: {TestModeButtonName}", TestModeButtonName);
                var startButton = _automationHelper.ElementFinder.FindElementByName(_crystalDiskMarkWindow, TestModeButtonName, TimeSpan.FromSeconds(5));
                if (startButton == null)
                {
                    // 尝试查找其他可能的按钮名称
                    startButton = _automationHelper.ElementFinder.FindElementByName(_crystalDiskMarkWindow, "Start", TimeSpan.FromSeconds(5));
                }

                if (startButton != null)
                {
                    await _automationHelper.InputSimulator.ClickElement(startButton);
                    await Task.Delay(1000, cancellationToken);
                    _logger.LogInformation("已点击开始测试按钮");
                }
                else
                {
                    _logger.LogWarning("未找到开始测试按钮");
                    throw new Exception("未找到开始测试按钮");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "开始测试失败: {Message}", ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 监控测试进度
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <param name="progress">进度报告器</param>
        /// <returns>监控任务</returns>
        private async Task MonitorTestProgressAsync(CancellationToken cancellationToken, IProgress<ProgressEventArgs>? progress = null)
        {
            try
            {
                if (_crystalDiskMarkWindow == null)
                    return;

                _logger.LogInformation("开始监控CrystalDiskMark测试进度");

                var startTime = DateTime.Now;
                var lastProgressReport = DateTime.Now;
                var progressValue = 60; // 起始进度值

                while (!cancellationToken.IsCancellationRequested)
                {
                    // 检查是否超时
                    if (DateTime.Now - startTime > _configuration.Timeouts.TestTimeout)
                    {
                        _logger.LogWarning("CrystalDiskMark测试超时");
                        throw new TimeoutException("测试超时");
                    }

                    // 检查测试是否完成
                    if (await IsTestCompletedAsync())
                    {
                        _logger.LogInformation("CrystalDiskMark测试已完成");
                        break;
                    }

                    // 更新进度报告
                    if (DateTime.Now - lastProgressReport > TimeSpan.FromSeconds(5))
                    {
                        progressValue = Math.Min(85, progressValue + 1);
                        progress?.Report(new ProgressEventArgs
                        {
                            Progress = progressValue,
                            Status = $"测试进行中... ({(DateTime.Now - startTime).TotalMinutes:F1}分钟)"
                        });
                        lastProgressReport = DateTime.Now;
                    }

                    await Task.Delay(2000, cancellationToken);
                }

                _logger.LogInformation("CrystalDiskMark测试监控完成，总耗时: {Duration}", DateTime.Now - startTime);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "监控测试进度失败: {Message}", ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 检查测试是否完成
        /// </summary>
        /// <returns>是否完成</returns>
        private async Task<bool> IsTestCompletedAsync()
        {
            try
            {
                if (_crystalDiskMarkWindow == null)
                    return false;

                // 检查是否有"All"按钮可用（测试完成后按钮会重新启用）
                var startButton = _automationHelper.ElementFinder.FindElementByName(_crystalDiskMarkWindow, "All", TimeSpan.FromSeconds(2));
                if (startButton != null && startButton.IsEnabled)
                {
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogDebug("检查测试完成状态时出错: {Message}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 解析测试结果
        /// </summary>
        /// <param name="config">测试配置</param>
        /// <param name="result">测试结果对象</param>
        /// <returns>解析任务</returns>
        private async Task ParseTestResultsAsync(TestConfiguration config, TestResult result)
        {
            try
            {
                if (_crystalDiskMarkWindow == null)
                    return;

                _logger.LogInformation("解析CrystalDiskMark测试结果");

                // 尝试截图保存结果
                var screenshotPath = _automationHelper.CaptureForDebugging(_crystalDiskMarkWindow, $"CrystalDiskMark_Result_{Environment.UserName}_Drive({config.TargetDrive.TrimEnd(':', '\\', '/')})");
                if (!string.IsNullOrEmpty(screenshotPath))
                {
                    result.OutputFiles.Add(screenshotPath);
                }

                // 查找结果显示区域
                var testMode = config.GetToolParameter(ToolName, "TestMode", "All") as string;
                //如果是All就要获取所有的结果，如果是某一项只需获取那一项的结果即可
                if (testMode == "All" || testMode == "SEQ1MQ8T1")
                {
                    var SEQ1MQ8T1_ReadAutoId = GetUIConfigValue("SEQ1MQ8T1_ReadAutoId", "1009");
                    if (string.IsNullOrEmpty(SEQ1MQ8T1_ReadAutoId))
                    {
                        _logger.LogWarning("未找到SEQ1MQ8T1_Read的自动化ID配置");
                        return;
                    }
                    string SEQ1MQ8T1_ReadSpeed = string.Empty;
                    if (!_automationHelper.FindAndGetTextBoxByAutomationId(_crystalDiskMarkWindow, SEQ1MQ8T1_ReadAutoId, out SEQ1MQ8T1_ReadSpeed))
                    {
                        _logger.LogWarning("未找到SEQ1MQ8T1_Read的结果");
                        return;
                    }

                    result.SetPerformanceData("SEQ1MQ8T1_Read", SEQ1MQ8T1_ReadSpeed);

                    var SEQ1MQ8T1_WriteAutoId = GetUIConfigValue("SEQ1MQ8T1_WriteAutoId", "1014");
                    if (string.IsNullOrEmpty(SEQ1MQ8T1_WriteAutoId))
                    {
                        _logger.LogWarning("未找到SEQ1MQ8T1_Write的自动化ID配置");
                        return;
                    }
                    string SEQ1MQ8T1_WriteSpeed = string.Empty;
                    if (!_automationHelper.FindAndGetTextBoxByAutomationId(_crystalDiskMarkWindow, SEQ1MQ8T1_WriteAutoId, out SEQ1MQ8T1_WriteSpeed))
                    {
                        _logger.LogWarning("未找到SEQ1MQ8T1_Write的结果");
                        return;
                    }

                    result.SetPerformanceData("SEQ1MQ8T1_Write", SEQ1MQ8T1_WriteSpeed);
                }

                if (testMode == "All" || testMode == "SEQ1MQ1T1")
                {
                    var SEQ1MQ1T1_ReadAutoId = GetUIConfigValue("SEQ1MQ1T1_ReadAutoId", "1010");
                    if (string.IsNullOrEmpty(SEQ1MQ1T1_ReadAutoId))
                    {
                        _logger.LogWarning("未找到SEQ1MQ1T1_Read的自动化ID配置");
                        return;
                    }
                    string SEQ1MQ1T1_ReadSpeed = string.Empty;
                    if (!_automationHelper.FindAndGetTextBoxByAutomationId(_crystalDiskMarkWindow, SEQ1MQ1T1_ReadAutoId, out SEQ1MQ1T1_ReadSpeed))
                    {
                        _logger.LogWarning("未找到SEQ1MQ1T1_Read的结果");
                        return;
                    }

                      result.SetPerformanceData("SEQ1MQ1T1_Read", SEQ1MQ1T1_ReadSpeed);

                    var SEQ1MQ1T1_WriteAutoId = GetUIConfigValue("SEQ1MQ1T1_WriteAutoId", "1015");
                    if (string.IsNullOrEmpty(SEQ1MQ1T1_WriteAutoId))
                    {
                        _logger.LogWarning("未找到SEQ1MQ1T1_Write的自动化ID配置");
                        return;
                    }
                    string SEQ1MQ1T1_WriteSpeed = string.Empty;
                    if (!_automationHelper.FindAndGetTextBoxByAutomationId(_crystalDiskMarkWindow, SEQ1MQ1T1_WriteAutoId, out SEQ1MQ1T1_WriteSpeed))
                    {
                        _logger.LogWarning("未找到SEQ1MQ1T1_Write的结果");
                        return;
                    }

                    result.SetPerformanceData("SEQ1MQ1T1_Write", SEQ1MQ1T1_WriteSpeed);
                }

                if (testMode == "All" || testMode == "RND4KQ32T1")
                {
                    var RND4KQ32T1_ReadAutoId = GetUIConfigValue("RND4KQ32T1_ReadAutoId", "1011");
                    if (string.IsNullOrEmpty(RND4KQ32T1_ReadAutoId))
                    {
                        _logger.LogWarning("未找到RND4KQ32T1_Read的自动化ID配置");
                        return;
                    }

                    string RND4KQ32T1_ReadSpeed = string.Empty;
                    if (!_automationHelper.FindAndGetTextBoxByAutomationId(_crystalDiskMarkWindow, RND4KQ32T1_ReadAutoId, out RND4KQ32T1_ReadSpeed))
                    {
                        _logger.LogWarning("未找到RND4KQ32T1_Read的结果");
                        return;
                    }

                    result.SetPerformanceData("RND4KQ32T1_Read", RND4KQ32T1_ReadSpeed);

                    var RND4KQ32T1_WriteAutoId = GetUIConfigValue("RND4KQ32T1_WriteAutoId", "1016");
                    if (string.IsNullOrEmpty(RND4KQ32T1_WriteAutoId))
                    {
                        _logger.LogWarning("未找到RND4KQ32T1_Write的自动化ID配置");
                        return;
                    }
                    string RND4KQ32T1_WriteSpeed = string.Empty;
                    if (!_automationHelper.FindAndGetTextBoxByAutomationId(_crystalDiskMarkWindow, RND4KQ32T1_WriteAutoId, out RND4KQ32T1_WriteSpeed))
                    {
                        _logger.LogWarning("未找到RND4KQ32T1_Write的结果");
                        return;
                    }

                    result.SetPerformanceData("RND4KQ32T1_Write", RND4KQ32T1_WriteSpeed);
                }

                if (testMode == "All" || testMode == "RND4KQ1T1")
                {
                    var RND4KQ1T1_ReadAutoId = GetUIConfigValue("RND4KQ1T1_ReadAutoId", "1012");
                    if (string.IsNullOrEmpty(RND4KQ1T1_ReadAutoId))
                    {
                        _logger.LogWarning("未找到RND4KQ1T1_Read的自动化ID配置");
                        return;
                    }
                    string RND4KQ1T1_ReadSpeed = string.Empty;
                    if (!_automationHelper.FindAndGetTextBoxByAutomationId(_crystalDiskMarkWindow, RND4KQ1T1_ReadAutoId, out RND4KQ1T1_ReadSpeed))
                    {
                        _logger.LogWarning("未找到RND4KQ1T1_Read的结果");
                        return;
                    }

                    result.SetPerformanceData("RND4KQ1T1_Read", RND4KQ1T1_ReadSpeed);

                    var RND4KQ1T1_WriteAutoId = GetUIConfigValue("RND4KQ1T1_WriteAutoId", "1017");
                    if (string.IsNullOrEmpty(RND4KQ1T1_WriteAutoId))
                    {
                        _logger.LogWarning("未找到RND4KQ1T1_Write的自动化ID配置");
                        return;
                    }
                    string RND4KQ1T1_WriteSpeed = string.Empty;
                    if (!_automationHelper.FindAndGetTextBoxByAutomationId(_crystalDiskMarkWindow, RND4KQ1T1_WriteAutoId, out RND4KQ1T1_WriteSpeed))
                    {
                        _logger.LogWarning("未找到RND4KQ1T1_Write的结果");
                        return;
                    }

                    result.SetPerformanceData("RND4KQ1T1_Write", RND4KQ1T1_WriteSpeed);
                }

                result.Success = true;

                result.AddLog("CrystalDiskMark测试完成");

                _logger.LogInformation("CrystalDiskMark结果解析完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解析测试结果失败: {Message}", ex.Message);
                result.Success = false;
                result.ErrorMessage = $"解析测试结果失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 解析性能指标
        /// </summary>
        /// <param name="resultText">结果文本</param>
        /// <param name="result">测试结果对象</param>
        private void ParsePerformanceMetrics(string resultText, TestResult result)
        {
            try
            {
                _logger.LogDebug("解析性能指标，结果文本长度: {Length}", resultText.Length);

                // 解析顺序读取速度 (SEQ1M Q8T1 Read)
                var seqReadMatch = Regex.Match(resultText, @"SEQ1M.*?Read.*?(\d+\.?\d*)\s*MB/s", RegexOptions.IgnoreCase);
                if (seqReadMatch.Success)
                {
                    if (double.TryParse(seqReadMatch.Groups[1].Value, out var seqRead))
                    {
                        result.SetPerformanceData("SequentialRead_MBps", seqRead);
                        _logger.LogInformation("顺序读取速度: {Speed} MB/s", seqRead);
                    }
                }

                // 解析顺序写入速度 (SEQ1M Q8T1 Write)
                var seqWriteMatch = Regex.Match(resultText, @"SEQ1M.*?Write.*?(\d+\.?\d*)\s*MB/s", RegexOptions.IgnoreCase);
                if (seqWriteMatch.Success)
                {
                    if (double.TryParse(seqWriteMatch.Groups[1].Value, out var seqWrite))
                    {
                        result.SetPerformanceData("SequentialWrite_MBps", seqWrite);
                        _logger.LogInformation("顺序写入速度: {Speed} MB/s", seqWrite);
                    }
                }

                // 解析随机读取速度 (RND4K Q32T1 Read)
                var rndReadMatch = Regex.Match(resultText, @"RND4K.*?Read.*?(\d+\.?\d*)\s*MB/s", RegexOptions.IgnoreCase);
                if (rndReadMatch.Success)
                {
                    if (double.TryParse(rndReadMatch.Groups[1].Value, out var rndRead))
                    {
                        result.SetPerformanceData("RandomRead_MBps", rndRead);
                        _logger.LogInformation("随机读取速度: {Speed} MB/s", rndRead);
                    }
                }

                // 解析随机写入速度 (RND4K Q32T1 Write)
                var rndWriteMatch = Regex.Match(resultText, @"RND4K.*?Write.*?(\d+\.?\d*)\s*MB/s", RegexOptions.IgnoreCase);
                if (rndWriteMatch.Success)
                {
                    if (double.TryParse(rndWriteMatch.Groups[1].Value, out var rndWrite))
                    {
                        result.SetPerformanceData("RandomWrite_MBps", rndWrite);
                        _logger.LogInformation("随机写入速度: {Speed} MB/s", rndWrite);
                    }
                }

                // 解析IOPS值
                var iopsMatches = Regex.Matches(resultText, @"(\d+\.?\d*)\s*IOPS", RegexOptions.IgnoreCase);
                if (iopsMatches.Count > 0)
                {
                    for (int i = 0; i < iopsMatches.Count; i++)
                    {
                        if (double.TryParse(iopsMatches[i].Groups[1].Value, out var iops))
                        {
                            result.SetPerformanceData($"IOPS_{i + 1}", iops);
                            _logger.LogInformation("IOPS {Index}: {Value}", i + 1, iops);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解析性能指标失败: {Message}", ex.Message);
            }
        }

        /// <summary>
        /// 从UI中提取结果
        /// </summary>
        /// <param name="result">测试结果对象</param>
        /// <returns>提取任务</returns>
        private async Task ExtractResultsFromUIAsync(TestResult result)
        {
            try
            {
                if (_crystalDiskMarkWindow == null)
                    return;

                // 尝试从不同的UI元素中提取结果
                var textElements = _automationHelper.ElementFinder.FindAllElementsByCondition(_crystalDiskMarkWindow,
                    _crystalDiskMarkWindow.ConditionFactory.ByControlType(FlaUI.Core.Definitions.ControlType.Text));
                var editElements = _automationHelper.ElementFinder.FindAllElementsByCondition(_crystalDiskMarkWindow,
                    _crystalDiskMarkWindow.ConditionFactory.ByControlType(FlaUI.Core.Definitions.ControlType.Edit));

                var allText = new List<string>();

                foreach (var element in textElements)
                {
                    try
                    {
                        var text = element.AsLabel()?.Text ?? element.Name ?? "";
                        if (!string.IsNullOrEmpty(text) && (text.Contains("MB/s") || text.Contains("IOPS")))
                        {
                            allText.Add(text);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug("读取文本元素失败: {Message}", ex.Message);
                    }
                }

                foreach (var element in editElements)
                {
                    try
                    {
                        var text = element.AsTextBox()?.Text ?? "";
                        if (!string.IsNullOrEmpty(text) && (text.Contains("MB/s") || text.Contains("IOPS")))
                        {
                            allText.Add(text);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug("读取编辑框元素失败: {Message}", ex.Message);
                    }
                }

                if (allText.Count > 0)
                {
                    var rawOutput = string.Join("\n", allText);
                    result.SetPerformanceData("RawOutput", rawOutput);
                    ParsePerformanceMetrics(rawOutput, result);
                }

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从UI提取结果失败: {Message}", ex.Message);
            }
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        /// <returns>清理任务</returns>
        private async Task CleanupAsync()
        {
            try
            {
                _logger.LogInformation("开始清理CrystalDiskMark资源");

                // 关闭窗口
                if (_crystalDiskMarkWindow != null)
                {
                    try
                    {
                        _crystalDiskMarkWindow.Close();
                        await Task.Delay(1000);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug("关闭窗口失败: {Message}", ex.Message);
                    }
                    finally
                    {
                        _crystalDiskMarkWindow = null;
                    }
                }

                // 终止进程
                if (_crystalDiskMarkProcess != null && !_crystalDiskMarkProcess.HasExited)
                {
                    try
                    {
                        _crystalDiskMarkProcess.Kill();
                        _crystalDiskMarkProcess.WaitForExit(5000);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug("终止进程失败: {Message}", ex.Message);
                    }
                    finally
                    {
                        _crystalDiskMarkProcess?.Dispose();
                        _crystalDiskMarkProcess = null;
                    }
                }

                _logger.LogInformation("CrystalDiskMark资源清理完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理资源失败: {Message}", ex.Message);
            }
        }


    }
}
