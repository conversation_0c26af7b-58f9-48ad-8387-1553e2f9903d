namespace MassStorageStableTestTool.Core.Exceptions;

/// <summary>
/// 测试超时异常
/// </summary>
public class TestTimeoutException : Exception
{
    /// <summary>
    /// 工具名称
    /// </summary>
    public string ToolName { get; }

    /// <summary>
    /// 超时时间
    /// </summary>
    public TimeSpan Timeout { get; }

    /// <summary>
    /// 操作类型
    /// </summary>
    public string? OperationType { get; }

    /// <summary>
    /// 使用工具名称和超时时间初始化异常。
    /// </summary>
    /// <param name="toolName">工具名称</param>
    /// <param name="timeout">超时时间</param>
    public TestTimeoutException(string toolName, TimeSpan timeout) 
        : base($"测试工具 '{toolName}' 在 {timeout.TotalSeconds} 秒后超时")
    {
        ToolName = toolName;
        Timeout = timeout;
    }

    /// <summary>
    /// 使用工具名称、超时时间和操作类型初始化异常。
    /// </summary>
    /// <param name="toolName">工具名称</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="operationType">操作类型</param>
    public TestTimeoutException(string toolName, TimeSpan timeout, string operationType) 
        : base($"测试工具 '{toolName}' 的 '{operationType}' 操作在 {timeout.TotalSeconds} 秒后超时")
    {
        ToolName = toolName;
        Timeout = timeout;
        OperationType = operationType;
    }

    /// <summary>
    /// 使用工具名称、超时时间和内部异常初始化异常。
    /// </summary>
    /// <param name="toolName">工具名称</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="innerException">内部异常</param>
    public TestTimeoutException(string toolName, TimeSpan timeout, Exception innerException) 
        : base($"测试工具 '{toolName}' 在 {timeout.TotalSeconds} 秒后超时", innerException)
    {
        ToolName = toolName;
        Timeout = timeout;
    }

    /// <summary>
    /// 使用工具名称、超时时间、操作类型和内部异常初始化异常。
    /// </summary>
    /// <param name="toolName">工具名称</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="operationType">操作类型</param>
    /// <param name="innerException">内部异常</param>
    public TestTimeoutException(string toolName, TimeSpan timeout, string operationType, Exception innerException) 
        : base($"测试工具 '{toolName}' 的 '{operationType}' 操作在 {timeout.TotalSeconds} 秒后超时", innerException)
    {
        ToolName = toolName;
        Timeout = timeout;
        OperationType = operationType;
    }
}
