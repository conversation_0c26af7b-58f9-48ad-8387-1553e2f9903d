# {{ configuration.title }}
# 报告生成时间: {{ generated_at | date: 'yyyy-MM-dd HH:mm:ss' }}
# 生成器版本: {{ version }}
# 报告作者: {{ configuration.author }}
# 组织: {{ configuration.organization }}
#
# 测试概览
# 测试开始时间: {{ test_suite.start_time | date: 'yyyy-MM-dd HH:mm:ss' }}
# 测试结束时间: {{ test_suite.end_time | date: 'yyyy-MM-dd HH:mm:ss' }}
# 测试总耗时: {{ test_suite.duration.total_minutes | math.round: 2 }} 分钟
# 目标驱动器: {{ test_suite.configuration.target_drive }}
# 测试工具: {{ test_suite.configuration.selected_tools | array.join: ', ' }}
# 总测试数: {{ test_suite.total_tests_count }}
# 成功测试数: {{ test_suite.successful_tests_count }}
# 失败测试数: {{ test_suite.failed_tests_count }}
# 成功率: {{ test_suite.success_rate | math.round: 1 }}%
# 整体状态: {{ test_suite.status }}
#
{{ if test_suite.drive_info }}# 驱动器信息
# 驱动器: {{ test_suite.drive_info.name }}
# 标签: {{ test_suite.drive_info.label }}
# 文件系统: {{ test_suite.drive_info.file_system }}
# 驱动器类型: {{ test_suite.drive_info.drive_type }}
# 总容量: {{ test_suite.drive_info.total_size_gb | math.round: 2 }} GB
# 可用空间: {{ test_suite.drive_info.available_free_space_gb | math.round: 2 }} GB
# 驱动器状态: {{ if test_suite.drive_info.is_ready }}就绪{{ else }}未就绪{{ end }}
#
{{ end }}# 详细测试结果
工具名称,开始时间,结束时间,耗时(分钟),状态,成功,错误信息,输出文件,退出码,进程ID,命令行,工作目录
{{ for test_result in test_suite.test_results }}{{ test_result.tool_name }},"{{ test_result.start_time | date: 'yyyy-MM-dd HH:mm:ss' }}","{{ test_result.end_time | date: 'yyyy-MM-dd HH:mm:ss' }}",{{ test_result.duration.total_minutes | math.round: 2 }},{{ test_result.status }},{{ if test_result.success }}是{{ else }}否{{ end }},"{{ test_result.error_message | replace: '"', '""' }}","{{ test_result.output_file_path }}",{{ test_result.exit_code }},{{ test_result.process_id }},"{{ test_result.command_line | replace: '"', '""' }}","{{ test_result.working_directory }}"
{{ end }}
