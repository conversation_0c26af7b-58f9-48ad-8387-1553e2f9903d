using MassStorageStableTestTool.Core.Enums;
using MassStorageStableTestTool.Core.Models;
using MassStorageStableTestTool.Core.Interfaces;
using Microsoft.Extensions.Logging;

namespace MassStorageStableTestTool.Tests.TestHelpers;

/// <summary>
/// 测试数据构建器，用于创建测试用的配置和数据
/// </summary>
public static class TestDataBuilder
{
    /// <summary>
    /// 创建默认的H2testw配置
    /// </summary>
    /// <returns>H2testw测试配置</returns>
    public static TestToolConfig CreateH2TestConfig()
    {
        return new TestToolConfig
        {
            Name = "H2testw",
            Type = TestToolType.GUI,
            ExecutablePath = "../third_party_tools/h2testw.exe",
            WindowTitle = "H2testw",
            Description = "H2testw测试工具",
            Version = "1.4",
            DefaultParameters = new Dictionary<string, object>
            {
                ["language"] = "english",
                ["TestAllSpace"] = 1000,
                ["WriteVerify"] = true,
                ["WriteSpeed"] = "Auto",
                ["ReadSpeed"] = "Auto"
            },

            Timeouts = new TimeoutSettings
            {
                LaunchTimeout = TimeSpan.FromSeconds(30),
                TestTimeout = TimeSpan.FromHours(1),
                ShutdownTimeout = TimeSpan.FromSeconds(15),
                ElementTimeout = TimeSpan.FromSeconds(10)
            },

            UIConfig = new Dictionary<string, object>
            {
                ["MainWindowName"] = "H2testw",
                ["SelectTargetAutoId"] = "1002",
                ["WriteVerifyButtonAutoId"] = "1006",
                ["englishAutomationId"] = "1023",
                ["resultEditorAutoId"] = "1011",
                ["SelectFolderWindowName"] = "浏览文件夹",
                ["FolderEditBoxAutoId"] = "14148",
                ["ConfirmButtonAutoId"] = "1",
                ["AllSpaceRadioButtonAutoId"] = "1000",
                ["SpecificSizeRadioButtonAutoId"] = "1001",
                ["SizeTextBoxAutoId"] = "1004",
                ["WriteVerifyButtonAutoId"] = "1006",
                ["ProcessBarAutoId"] = "1010",
                ["testWindowName"] = "H2testw | Progress"
            },

            OutputParsing = new OutputParsingConfig
            {
                Format = "text",
                ParsingRules = new Dictionary<string, string>
                {
                    ["WriteSpeed"] = @"Writing speed: ([\d.]+) MByte/s",
                    ["ReadSpeed"] = @"Reading speed: ([\d.]+) MByte/s",
                    ["TestResult"] = @"Test (finished|failed)",
                    ["ErrorCount"] = @"(\d+) errors"
                }
            },

            Enabled = true,
            Priority = 1,
            SupportedFileSystems = new List<string> { "FAT32", "NTFS", "exFAT" },
            MinimumDiskSpace = 100
        };
    }

    /// <summary>
    /// 创建CrystalDiskMark配置
    /// </summary>
    /// <returns>CrystalDiskMark测试配置</returns>
    public static TestToolConfig CreateCrystalDiskMarkConfig()
    {
        return new TestToolConfig
        {
            Name = "CrystalDiskMark",
            Type = TestToolType.GUI,
            ExecutablePath = "./test/CrystalDiskMark.exe",
            WindowTitle = "CrystalDiskMark",
            Description = "CrystalDiskMark磁盘基准测试工具",
            Version = "8.0.1",
            DefaultParameters = new Dictionary<string, object>
            {
                ["TestSize"] = "1GiB",
                ["TestCount"] = 5,
                ["TestMode"] = "All",
                ["QueueDepth"] = 32,
                ["ThreadCount"] = 1
            },
            Timeouts = new TimeoutSettings
            {
                LaunchTimeout = TimeSpan.FromSeconds(30),
                TestTimeout = TimeSpan.FromMinutes(45),
                ShutdownTimeout = TimeSpan.FromSeconds(10),
                ElementTimeout = TimeSpan.FromSeconds(10)
            },
            UIConfig = new Dictionary<string, object>
            {
                ["MainWindowClass"] = "TMainForm",
                ["DriveComboBoxName"] = "ComboBox1",
                ["AllButtonName"] = "Button1",
                ["TestSizeComboBoxName"] = "ComboBox2",
                ["TestCountComboBoxName"] = "ComboBox3"
            },
            OutputParsing = new OutputParsingConfig
            {
                Format = "text",
                ParsingRules = new Dictionary<string, string>
                {
                    ["SEQ1M_Read"] = @"SEQ1M Q8T1.*?(\d+\.\d+)",
                    ["SEQ1M_Write"] = @"SEQ1M Q8T1.*?(\d+\.\d+).*?(\d+\.\d+)",
                    ["RND4K_Read"] = @"RND4K Q32T1.*?(\d+\.\d+)",
                    ["RND4K_Write"] = @"RND4K Q32T1.*?(\d+\.\d+).*?(\d+\.\d+)"
                }
            },
            Enabled = true,
            Priority = 2,
            SupportedFileSystems = new List<string> { "NTFS", "FAT32", "exFAT" },
            MinimumDiskSpace = 1024
        };
    }

    /// <summary>
    /// 创建测试配置
    /// </summary>
    /// <param name="targetDrive">目标驱动器</param>
    /// <param name="parameters">额外参数</param>
    /// <returns>测试配置</returns>
    public static TestConfiguration CreateTestConfiguration(
        string targetDrive = "C:\\",
        Dictionary<string, object>? parameters = null)
    {
        return new TestConfiguration
        {
            TargetDrive = targetDrive,
            Parameters = parameters ?? new Dictionary<string, object>(),
            ConfigurationName = "单元测试",
            Description = "单元测试配置"
        };
    }

    /// <summary>
    /// 创建成功的测试结果
    /// </summary>
    /// <param name="toolName">工具名称</param>
    /// <returns>测试结果</returns>
    public static TestResult CreateSuccessfulTestResult(string toolName = "H2testw")
    {
        var result = new TestResult
        {
            ToolName = toolName,
            StartTime = DateTime.Now.AddMinutes(-10),
            EndTime = DateTime.Now,
            Success = true
        };

        result.SetPerformanceData("WriteSpeed", 25.5);
        result.SetPerformanceData("ReadSpeed", 30.2);
        result.SetPerformanceData("ErrorCount", 0);
        result.AddLog("测试成功完成");

        return result;
    }

    /// <summary>
    /// 创建失败的测试结果
    /// </summary>
    /// <param name="toolName">工具名称</param>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>测试结果</returns>
    public static TestResult CreateFailedTestResult(string toolName = "H2testw", string errorMessage = "测试失败")
    {
        var result = new TestResult
        {
            ToolName = toolName,
            StartTime = DateTime.Now.AddMinutes(-5),
            EndTime = DateTime.Now,
            Success = false,
            ErrorMessage = errorMessage
        };

        result.SetPerformanceData("ErrorCount", 5);
        result.AddLog($"测试失败: {errorMessage}");

        return result;
    }

    /// <summary>
    /// 创建超时设置
    /// </summary>
    /// <param name="launchTimeout">启动超时</param>
    /// <param name="testTimeout">测试超时</param>
    /// <param name="shutdownTimeout">关闭超时</param>
    /// <param name="elementTimeout">元素超时</param>
    /// <returns>超时设置</returns>
    public static TimeoutSettings CreateTimeoutSettings(
        TimeSpan? launchTimeout = null,
        TimeSpan? testTimeout = null,
        TimeSpan? shutdownTimeout = null,
        TimeSpan? elementTimeout = null)
    {
        return new TimeoutSettings
        {
            LaunchTimeout = launchTimeout ?? TimeSpan.FromSeconds(30),
            TestTimeout = testTimeout ?? TimeSpan.FromMinutes(30),
            ShutdownTimeout = shutdownTimeout ?? TimeSpan.FromSeconds(10),
            ElementTimeout = elementTimeout ?? TimeSpan.FromSeconds(10)
        };
    }

    /// <summary>
    /// 创建输出解析配置
    /// </summary>
    /// <param name="format">格式</param>
    /// <param name="rules">解析规则</param>
    /// <returns>输出解析配置</returns>
    public static OutputParsingConfig CreateOutputParsingConfig(
        string format = "text",
        Dictionary<string, string>? rules = null)
    {
        return new OutputParsingConfig
        {
            Format = format,
            ParsingRules = rules ?? new Dictionary<string, string>
            {
                ["WriteSpeed"] = @"Writing speed: ([\d.]+) MByte/s",
                ["ReadSpeed"] = @"Reading speed: ([\d.]+) MByte/s",
                ["TestResult"] = @"Test (finished|failed)",
                ["ErrorCount"] = @"(\d+) errors"
            }
        };
    }

    /// <summary>
    /// 创建进度事件参数
    /// </summary>
    /// <param name="progress">进度百分比</param>
    /// <param name="status">状态消息</param>
    /// <returns>进度事件参数</returns>
    public static ProgressEventArgs CreateProgressEventArgs(int progress = 50, string status = "测试进行中...")
    {
        return new ProgressEventArgs
        {
            Progress = progress,
            Status = status
        };
    }

    /// <summary>
    /// 创建测试状态变更事件参数
    /// </summary>
    /// <param name="status">测试状态</param>
    /// <param name="toolName">工具名称</param>
    /// <returns>状态变更事件参数</returns>
    public static TestStatusChangedEventArgs CreateStatusChangedEventArgs(
        TestStatus status = TestStatus.Running,
        string toolName = "H2testw")
    {
        return new TestStatusChangedEventArgs
        {
            NewStatus = status,
            ToolName = toolName,
            Timestamp = DateTime.Now
        };
    }

    /// <summary>
    /// 创建日志事件参数
    /// </summary>
    /// <param name="level">日志级别</param>
    /// <param name="message">消息</param>
    /// <param name="source">来源</param>
    /// <param name="exception">异常</param>
    /// <returns>日志事件参数</returns>
    public static LogEventArgs CreateLogEventArgs(
        LogLevel level = LogLevel.Information,
        string message = "测试日志消息",
        string source = "H2testw",
        Exception? exception = null)
    {
        return new LogEventArgs
        {
            Level = level,
            Message = message,
            Source = source,
            Exception = exception,
            Timestamp = DateTime.Now
        };
    }
}
