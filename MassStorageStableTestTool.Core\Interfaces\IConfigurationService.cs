using MassStorageStableTestTool.Core.Models;

namespace MassStorageStableTestTool.Core.Interfaces;

/// <summary>
/// 配置服务接口
/// </summary>
public interface IConfigurationService
{
    /// <summary>
    /// 加载配置
    /// </summary>
    /// <param name="configPath">配置文件路径</param>
    /// <returns>配置对象</returns>
    Task<ApplicationConfiguration> LoadConfigurationAsync(string? configPath = null);

    /// <summary>
    /// 保存配置
    /// </summary>
    /// <param name="configuration">配置对象</param>
    /// <param name="configPath">配置文件路径</param>
    /// <returns>保存结果</returns>
    Task<bool> SaveConfigurationAsync(ApplicationConfiguration configuration, string? configPath = null);

    /// <summary>
    /// 创建默认配置
    /// </summary>
    /// <returns>默认配置</returns>
    ApplicationConfiguration CreateDefaultConfiguration();

    /// <summary>
    /// 验证配置
    /// </summary>
    /// <param name="configuration">配置对象</param>
    /// <returns>验证结果</returns>
    (bool IsValid, List<string> Errors) ValidateConfiguration(ApplicationConfiguration configuration);

    /// <summary>
    /// 获取测试工具配置
    /// </summary>
    /// <param name="toolName">工具名称</param>
    /// <returns>工具配置</returns>
    Task<TestToolConfig?> GetToolConfigurationAsync(string toolName);

    /// <summary>
    /// 更新测试工具配置
    /// </summary>
    /// <param name="toolName">工具名称</param>
    /// <param name="config">工具配置</param>
    /// <returns>更新结果</returns>
    Task<bool> UpdateToolConfigurationAsync(string toolName, TestToolConfig config);

    /// <summary>
    /// 获取所有测试工具配置
    /// </summary>
    /// <returns>工具配置字典</returns>
    Task<Dictionary<string, TestToolConfig>> GetAllToolConfigurationsAsync();

    /// <summary>
    /// 导入配置
    /// </summary>
    /// <param name="filePath">配置文件路径</param>
    /// <param name="format">配置格式</param>
    /// <returns>导入的配置</returns>
    Task<ApplicationConfiguration> ImportConfigurationAsync(string filePath, string format = "json");

    /// <summary>
    /// 导出配置
    /// </summary>
    /// <param name="configuration">配置对象</param>
    /// <param name="filePath">导出文件路径</param>
    /// <param name="format">导出格式</param>
    /// <returns>导出结果</returns>
    Task<bool> ExportConfigurationAsync(ApplicationConfiguration configuration, string filePath, string format = "json");

    /// <summary>
    /// 重置配置为默认值
    /// </summary>
    /// <returns>重置结果</returns>
    Task<bool> ResetToDefaultAsync();

    /// <summary>
    /// 配置变化事件
    /// </summary>
    event EventHandler<ConfigurationChangedEventArgs>? ConfigurationChanged;
}

/// <summary>
/// 配置变化事件参数
/// </summary>
public class ConfigurationChangedEventArgs : EventArgs
{
    /// <summary>
    /// 变化类型
    /// </summary>
    public ConfigurationChangeType ChangeType { get; set; }

    /// <summary>
    /// 配置键
    /// </summary>
    public string ConfigKey { get; set; } = string.Empty;

    /// <summary>
    /// 旧值
    /// </summary>
    public object? OldValue { get; set; }

    /// <summary>
    /// 新值
    /// </summary>
    public object? NewValue { get; set; }

    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;
}

/// <summary>
/// 配置变化类型
/// </summary>
public enum ConfigurationChangeType
{
    /// <summary>
    /// 添加
    /// </summary>
    Added,

    /// <summary>
    /// 修改
    /// </summary>
    Modified,

    /// <summary>
    /// 删除
    /// </summary>
    Removed,

    /// <summary>
    /// 重置
    /// </summary>
    Reset
}