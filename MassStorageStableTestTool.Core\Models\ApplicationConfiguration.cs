namespace MassStorageStableTestTool.Core.Models;

/// <summary>
/// 应用程序配置模型
/// </summary>
public class ApplicationConfiguration
{
    /// <summary>
    /// 测试工具配置字典
    /// </summary>
    public Dictionary<string, TestToolConfig> TestTools { get; set; } = new();

    /// <summary>
    /// 报告配置
    /// </summary>
    public ReportConfiguration Reporting { get; set; } = new();

    /// <summary>
    /// 日志配置
    /// </summary>
    public LoggingConfiguration Logging { get; set; } = new();

    /// <summary>
    /// 性能配置
    /// </summary>
    public PerformanceConfiguration Performance { get; set; } = new();

    /// <summary>
    /// UI配置
    /// </summary>
    public UIConfiguration UI { get; set; } = new();

    /// <summary>
    /// 安全配置
    /// </summary>
    public SecurityConfiguration Security { get; set; } = new();

    /// <summary>
    /// 配置版本
    /// </summary>
    public string Version { get; set; } = "1.0";

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 最后修改时间
    /// </summary>
    public DateTime LastModified { get; set; } = DateTime.Now;

    /// <summary>
    /// 配置描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 自定义设置
    /// </summary>
    public Dictionary<string, object> CustomSettings { get; set; } = new();

    /// <summary>
    /// 验证配置有效性
    /// </summary>
    /// <returns>验证结果</returns>
    public (bool IsValid, List<string> Errors) Validate()
    {
        var errors = new List<string>();

        // 验证测试工具配置
        foreach (var tool in TestTools)
        {
            var (isValid, toolErrors) = tool.Value.Validate();
            if (!isValid)
            {
                errors.AddRange(toolErrors.Select(e => $"工具 '{tool.Key}': {e}"));
            }
        }

        // 验证报告配置
        if (string.IsNullOrWhiteSpace(Reporting.OutputDirectory))
        {
            errors.Add("报告输出目录不能为空");
        }

        // 验证日志配置
        if (string.IsNullOrWhiteSpace(Logging.LogDirectory))
        {
            errors.Add("日志目录不能为空");
        }

        return (errors.Count == 0, errors);
    }

    /// <summary>
    /// 克隆配置
    /// </summary>
    /// <returns>配置副本</returns>
    public ApplicationConfiguration Clone()
    {
        return new ApplicationConfiguration
        {
            TestTools = TestTools.ToDictionary(kvp => kvp.Key, kvp => kvp.Value.Clone()),
            Reporting = Reporting.Clone(),
            Logging = Logging.Clone(),
            Performance = Performance.Clone(),
            UI = UI.Clone(),
            Security = Security.Clone(),
            Version = Version,
            CreatedAt = CreatedAt,
            LastModified = DateTime.Now,
            Description = Description,
            CustomSettings = new Dictionary<string, object>(CustomSettings)
        };
    }
}

/// <summary>
/// 报告配置
/// </summary>
public class ReportConfiguration
{
    /// <summary>
    /// 输出目录
    /// </summary>
    public string OutputDirectory { get; set; } = "./Reports";

    /// <summary>
    /// 文件名模板
    /// </summary>
    public string FileNameTemplate { get; set; } = "{DriveLabel}_{DateTime:yyyy-MM-dd_HH-mm-ss}_Report";

    /// <summary>
    /// 是否包含系统信息
    /// </summary>
    public bool IncludeSystemInfo { get; set; } = true;

    /// <summary>
    /// 支持的格式
    /// </summary>
    public List<string> Formats { get; set; } = new() { "txt", "csv", "json" };

    /// <summary>
    /// 自动打开报告
    /// </summary>
    public bool AutoOpenReport { get; set; } = true;

    /// <summary>
    /// 报告模板路径
    /// </summary>
    public string? TemplatePath { get; set; }

    /// <summary>
    /// 克隆配置
    /// </summary>
    /// <returns>配置副本</returns>
    public ReportConfiguration Clone()
    {
        return new ReportConfiguration
        {
            OutputDirectory = OutputDirectory,
            FileNameTemplate = FileNameTemplate,
            IncludeSystemInfo = IncludeSystemInfo,
            Formats = new List<string>(Formats),
            AutoOpenReport = AutoOpenReport,
            TemplatePath = TemplatePath
        };
    }
}

/// <summary>
/// 日志配置
/// </summary>
public class LoggingConfiguration
{
    /// <summary>
    /// 日志级别
    /// </summary>
    public string LogLevel { get; set; } = "Information";

    /// <summary>
    /// 日志目录
    /// </summary>
    public string LogDirectory { get; set; } = "./Logs";

    /// <summary>
    /// 是否启用控制台输出
    /// </summary>
    public bool EnableConsoleOutput { get; set; } = true;

    /// <summary>
    /// 是否启用文件输出
    /// </summary>
    public bool EnableFileOutput { get; set; } = true;

    /// <summary>
    /// 日志文件最大大小（MB）
    /// </summary>
    public int MaxFileSizeMB { get; set; } = 10;

    /// <summary>
    /// 保留的日志文件数量
    /// </summary>
    public int RetainedFileCount { get; set; } = 7;

    /// <summary>
    /// 克隆配置
    /// </summary>
    /// <returns>配置副本</returns>
    public LoggingConfiguration Clone()
    {
        return new LoggingConfiguration
        {
            LogLevel = LogLevel,
            LogDirectory = LogDirectory,
            EnableConsoleOutput = EnableConsoleOutput,
            EnableFileOutput = EnableFileOutput,
            MaxFileSizeMB = MaxFileSizeMB,
            RetainedFileCount = RetainedFileCount
        };
    }
}

/// <summary>
/// 性能配置
/// </summary>
public class PerformanceConfiguration
{
    /// <summary>
    /// 最大并发测试数
    /// </summary>
    public int MaxConcurrentTests { get; set; } = 1;

    /// <summary>
    /// 进程清理超时时间（秒）
    /// </summary>
    public int ProcessCleanupTimeout { get; set; } = 30;

    /// <summary>
    /// 内存阈值（百分比）
    /// </summary>
    public int MemoryThreshold { get; set; } = 80;

    /// <summary>
    /// 是否启用性能监控
    /// </summary>
    public bool EnablePerformanceMonitoring { get; set; } = true;

    /// <summary>
    /// 监控间隔（秒）
    /// </summary>
    public int MonitoringInterval { get; set; } = 5;

    /// <summary>
    /// 克隆配置
    /// </summary>
    /// <returns>配置副本</returns>
    public PerformanceConfiguration Clone()
    {
        return new PerformanceConfiguration
        {
            MaxConcurrentTests = MaxConcurrentTests,
            ProcessCleanupTimeout = ProcessCleanupTimeout,
            MemoryThreshold = MemoryThreshold,
            EnablePerformanceMonitoring = EnablePerformanceMonitoring,
            MonitoringInterval = MonitoringInterval
        };
    }
}

/// <summary>
/// UI配置
/// </summary>
public class UIConfiguration
{
    /// <summary>
    /// 主题
    /// </summary>
    public string Theme { get; set; } = "Light";

    /// <summary>
    /// 语言
    /// </summary>
    public string Language { get; set; } = "zh-CN";

    /// <summary>
    /// 窗口大小
    /// </summary>
    public WindowSize WindowSize { get; set; } = new();

    /// <summary>
    /// 是否自动保存窗口状态
    /// </summary>
    public bool AutoSaveWindowState { get; set; } = true;

    /// <summary>
    /// 刷新间隔（毫秒）
    /// </summary>
    public int RefreshInterval { get; set; } = 1000;

    /// <summary>
    /// 克隆配置
    /// </summary>
    /// <returns>配置副本</returns>
    public UIConfiguration Clone()
    {
        return new UIConfiguration
        {
            Theme = Theme,
            Language = Language,
            WindowSize = WindowSize.Clone(),
            AutoSaveWindowState = AutoSaveWindowState,
            RefreshInterval = RefreshInterval
        };
    }
}

/// <summary>
/// 窗口大小配置
/// </summary>
public class WindowSize
{
    /// <summary>
    /// 宽度
    /// </summary>
    public int Width { get; set; } = 1200;

    /// <summary>
    /// 高度
    /// </summary>
    public int Height { get; set; } = 800;

    /// <summary>
    /// 是否最大化
    /// </summary>
    public bool IsMaximized { get; set; } = false;

    /// <summary>
    /// 克隆配置
    /// </summary>
    /// <returns>配置副本</returns>
    public WindowSize Clone()
    {
        return new WindowSize
        {
            Width = Width,
            Height = Height,
            IsMaximized = IsMaximized
        };
    }
}

/// <summary>
/// 安全配置
/// </summary>
public class SecurityConfiguration
{
    /// <summary>
    /// 允许的可执行文件目录
    /// </summary>
    public List<string> AllowedExecutableDirectories { get; set; } = new();

    /// <summary>
    /// 是否验证数字签名
    /// </summary>
    public bool VerifyDigitalSignature { get; set; } = false;

    /// <summary>
    /// 是否限制驱动器访问
    /// </summary>
    public bool RestrictDriveAccess { get; set; } = true;

    /// <summary>
    /// 允许的驱动器类型
    /// </summary>
    public List<string> AllowedDriveTypes { get; set; } = new() { "Removable" };

    /// <summary>
    /// 克隆配置
    /// </summary>
    /// <returns>配置副本</returns>
    public SecurityConfiguration Clone()
    {
        return new SecurityConfiguration
        {
            AllowedExecutableDirectories = new List<string>(AllowedExecutableDirectories),
            VerifyDigitalSignature = VerifyDigitalSignature,
            RestrictDriveAccess = RestrictDriveAccess,
            AllowedDriveTypes = new List<string>(AllowedDriveTypes)
        };
    }
}
