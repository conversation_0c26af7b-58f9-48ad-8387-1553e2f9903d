using System;
using MassStorageStableTestTool.Automation.Controllers;

namespace TestATTOLogAnalysis
{
    class Program
    {
        static void Main(string[] args)
        {
            // 测试成功的日志内容
            var successLogContent = @"Friday, October 10, 2025   9:57 AM
Running ATTO_Test_20251010_095530_J.bmk....
Testing completed sucessfully.";

            // 测试失败的日志内容
            var failureLogContent = @"Friday, October 10, 2025   9:57 AM
Running ATTO_Test_20251010_095530_J.bmk....
Error: Test failed due to disk access error.";

            // 测试不完整的日志内容
            var incompleteLogContent = @"Friday, October 10, 2025   9:57 AM
Running ATTO_Test_20251010_095530_J.bmk....";

            Console.WriteLine("测试ATTO日志分析功能:");
            Console.WriteLine("======================");

            // 由于ATTOController的AnalyzeLogForSuccess方法是私有的，
            // 我们需要创建一个公共的测试方法或者使用反射来测试
            // 这里我们展示预期的行为

            Console.WriteLine("\n1. 测试成功日志:");
            Console.WriteLine("输入日志:");
            Console.WriteLine(successLogContent);
            Console.WriteLine("预期结果: 成功");

            Console.WriteLine("\n2. 测试失败日志:");
            Console.WriteLine("输入日志:");
            Console.WriteLine(failureLogContent);
            Console.WriteLine("预期结果: 失败 - 发现错误信息");

            Console.WriteLine("\n3. 测试不完整日志:");
            Console.WriteLine("输入日志:");
            Console.WriteLine(incompleteLogContent);
            Console.WriteLine("预期结果: 失败 - 未找到成功完成的消息");

            Console.WriteLine("\n日志分析逻辑说明:");
            Console.WriteLine("- 检查是否包含 'Running' 开头的消息");
            Console.WriteLine("- 检查是否包含 'Testing completed sucessfully' 消息");
            Console.WriteLine("- 检查是否包含错误关键词: error, failed, exception");
            Console.WriteLine("- 只有同时找到运行消息和成功完成消息，且没有错误消息时，才判定为成功");
        }
    }
}
