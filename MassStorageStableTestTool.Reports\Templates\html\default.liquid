<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ Configuration.Title }}</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #007acc;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #007acc;
            margin: 0;
            font-size: 2.5em;
        }
        .header .subtitle {
            color: #666;
            margin-top: 10px;
            font-size: 1.1em;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border-left: 4px solid #007acc;
            background-color: #f9f9f9;
        }
        .section h2 {
            color: #007acc;
            margin-top: 0;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .overview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .overview-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            text-align: center;
        }
        .overview-card h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .overview-card .value {
            font-size: 2em;
            font-weight: bold;
            color: #007acc;
        }
        .test-result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            border-left: 5px solid;
        }
        .test-success {
            background-color: #d4edda;
            border-left-color: #28a745;
        }
        .test-failure {
            background-color: #f8d7da;
            border-left-color: #dc3545;
        }
        .test-timeout {
            background-color: #fff3cd;
            border-left-color: #ffc107;
        }
        .test-cancelled {
            background-color: #e2e3e5;
            border-left-color: #6c757d;
        }
        .test-result h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .test-result .test-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .test-result .test-info div {
            background: rgba(255,255,255,0.7);
            padding: 8px;
            border-radius: 3px;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background-color: #28a745;
            transition: width 0.3s ease;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-success { background-color: #28a745; color: white; }
        .status-failure { background-color: #dc3545; color: white; }
        .status-timeout { background-color: #ffc107; color: black; }
        .status-cancelled { background-color: #6c757d; color: white; }
        .status-running { background-color: #007bff; color: white; }
        
        @media (max-width: 768px) {
            .container { padding: 15px; }
            .overview-grid { grid-template-columns: 1fr; }
            .test-result .test-info { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ Configuration.Title }}</h1>
            <div class="subtitle">
                报告生成时间: {{ GeneratedAt | date: 'yyyy-MM-dd HH:mm:ss' }} | 
                生成器版本: {{ version }}
                {{ if Configuration.Author }} | 报告作者: {{ Configuration.Author }}{{ end }}
                {{ if Configuration.Organization }} | 组织: {{ Configuration.Organization }}{{ end }}
            </div>
        </div>

        <div class="section">
            <h2>📊 测试概览</h2>
            <div class="overview-grid">
                <div class="overview-card">
                    <h3>测试开始时间</h3>
                    <div class="value">{{ TestSuite.StartTime | date: 'HH:mm:ss' }}</div>
                    <small>{{ TestSuite.StartTime | date: 'yyyy-MM-dd' }}</small>
                </div>
                <div class="overview-card">
                    <h3>测试耗时</h3>
                    <div class="value">{{ TestSuite.Duration.TotalMinutes | round: 1 }}</div>
                    <small>分钟</small>
                </div>
                <div class="overview-card">
                    <h3>成功率</h3>
                    <div class="value">{{ TestSuite.SuccessRate | round: 1 }}%</div>
                    <small>{{ TestSuite.SuccessfulTestsCount }}/{{ TestSuite.TotalTestsCount }}</small>
                </div>
                <div class="overview-card">
                    <h3>整体状态</h3>
                    <div class="value">
                        <span class="status-badge status-{{ TestSuite.Status | downcase }}">
                            {{ TestSuite.Status }}
                        </span>
                    </div>
                    <small>{{ if TestSuite.AllTestsPassed }}✅ 全部通过{{ else }}❌ 存在失败{{ end }}</small>
                </div>
            </div>
            
            <div class="progress-bar">
                <div class="progress-fill" style="width: {{ TestSuite.SuccessRate }}%"></div>
            </div>
            
            <p><strong>目标驱动器:</strong> {{ TestSuite.Configuration.TargetDrive }}</p>
            <p><strong>测试工具:</strong> {{ TestSuite.Configuration.SelectedTools | join: ', ' }}</p>
        </div>

        <div class="section">
            <h2>🔍 详细测试结果</h2>
            {{ for test_result in TestSuite.TestResults }}
            <div class="test-result test-{{ test_result.Status | downcase }}">
                <h4>
                    {{ test_result.ToolName }}
                    <span class="status-badge status-{{ test_result.Status | downcase }}">
                        {{ test_result.Status }}
                    </span>
                </h4>
                
                <div class="test-info">
                    <div><strong>开始时间:</strong> {{ test_result.StartTime | date: 'yyyy-MM-dd HH:mm:ss' }}</div>
                    <div><strong>结束时间:</strong> {{ test_result.EndTime | date: 'yyyy-MM-dd HH:mm:ss' }}</div>
                    <div><strong>耗时:</strong> {{ test_result.Duration.TotalMinutes | round: 2 }} 分钟</div>
                    <div><strong>成功:</strong> {{ if test_result.Success }}是{{ else }}否{{ end }}</div>
                </div>
                
                {{ if test_result.ErrorMessage }}
                <div style="margin-top: 10px;">
                    <strong>错误信息:</strong>
                    <pre style="background: rgba(220,53,69,0.1); padding: 10px; border-radius: 3px; white-space: pre-wrap;">{{ test_result.ErrorMessage }}</pre>
                </div>
                {{ end }}
                
                {{ if test_result.OutputFiles }}
                <div style="margin-top: 10px;">
                    <strong>输出文件:</strong> {{ test_result.OutputFiles }}
                </div>
                {{ end }}
            </div>
            {{ end }}
        </div>

        {{ if TestSuite.DriveInfo }}
        <div class="section">
            <h2>💾 驱动器信息</h2>
            <div class="overview-grid">
                <div class="overview-card">
                    <h3>驱动器</h3>
                    <div class="value">{{ TestSuite.DriveInfo.Name }}</div>
                    <small>{{ TestSuite.DriveInfo.Label }}</small>
                </div>
                <div class="overview-card">
                    <h3>文件系统</h3>
                    <div class="value">{{ TestSuite.DriveInfo.FileSystem }}</div>
                    <small>{{ TestSuite.DriveInfo.DriveType }}</small>
                </div>
                <div class="overview-card">
                    <h3>总容量</h3>
                    <div class="value">{{ TestSuite.DriveInfo.TotalSize / (1024.0 * 1024.0 * 1024.0) | round: 2 }}</div>
                    <small>GB</small>
                </div>
                <div class="overview-card">
                    <h3>可用空间</h3>
                    <div class="value">{{ TestSuite.DriveInfo.AvailableFreeSpace / (1024.0 * 1024.0 * 1024.0) | round: 2 }}</div>
                    <small>GB ({{ (TestSuite.DriveInfo.AvailableFreeSpace / TestSuite.DriveInfo.TotalSize) * 100 | round: 1 }}%)</small>
                </div>
            </div>
            
            <p><strong>驱动器状态:</strong> {{ if TestSuite.DriveInfo.IsReady }}✅ 就绪{{ else }}❌ 未就绪{{ end }}</p>
        </div>
        {{ end }}

        <div class="footer">
            <p>报告由 {{ GeneratedBy }} 生成 | 版本 {{ Version }}</p>
        </div>
    </div>

    {{ if Configuration.CustomJavaScript }}
    <script>
    {{ Configuration.CustomJavaScript }}
    </script>
    {{ end }}
</body>
</html>
