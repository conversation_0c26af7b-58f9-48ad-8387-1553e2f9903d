﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MassStorageStableTestTool.Automation", "MassStorageStableTestTool.Automation\MassStorageStableTestTool.Automation.csproj", "{BE629BB2-D030-410B-A91F-289F97885A27}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MassStorageStableTestTool.Core", "MassStorageStableTestTool.Core\MassStorageStableTestTool.Core.csproj", "{B5DB4110-7051-4D81-A887-1F85D23AF6BE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MassStorageStableTestTool.Reports", "MassStorageStableTestTool.Reports\MassStorageStableTestTool.Reports.csproj", "{A140AB10-E232-45FE-AD0B-78F7DF3EB9C1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MassStorageStableTestTool.Tests", "MassStorageStableTestTool.Tests\MassStorageStableTestTool.Tests.csproj", "{69277689-187C-4BE7-93CF-DEE64B822B7F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MassStorageStableTestTool.UI", "MassStorageStableTestTool.UI\MassStorageStableTestTool.UI.csproj", "{618EB0FE-F8BD-42BF-8696-23D34044A110}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{BE629BB2-D030-410B-A91F-289F97885A27}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BE629BB2-D030-410B-A91F-289F97885A27}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BE629BB2-D030-410B-A91F-289F97885A27}.Debug|x64.ActiveCfg = Debug|Any CPU
		{BE629BB2-D030-410B-A91F-289F97885A27}.Debug|x64.Build.0 = Debug|Any CPU
		{BE629BB2-D030-410B-A91F-289F97885A27}.Debug|x86.ActiveCfg = Debug|Any CPU
		{BE629BB2-D030-410B-A91F-289F97885A27}.Debug|x86.Build.0 = Debug|Any CPU
		{BE629BB2-D030-410B-A91F-289F97885A27}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BE629BB2-D030-410B-A91F-289F97885A27}.Release|Any CPU.Build.0 = Release|Any CPU
		{BE629BB2-D030-410B-A91F-289F97885A27}.Release|x64.ActiveCfg = Release|Any CPU
		{BE629BB2-D030-410B-A91F-289F97885A27}.Release|x64.Build.0 = Release|Any CPU
		{BE629BB2-D030-410B-A91F-289F97885A27}.Release|x86.ActiveCfg = Release|Any CPU
		{BE629BB2-D030-410B-A91F-289F97885A27}.Release|x86.Build.0 = Release|Any CPU
		{B5DB4110-7051-4D81-A887-1F85D23AF6BE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B5DB4110-7051-4D81-A887-1F85D23AF6BE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B5DB4110-7051-4D81-A887-1F85D23AF6BE}.Debug|x64.ActiveCfg = Debug|Any CPU
		{B5DB4110-7051-4D81-A887-1F85D23AF6BE}.Debug|x64.Build.0 = Debug|Any CPU
		{B5DB4110-7051-4D81-A887-1F85D23AF6BE}.Debug|x86.ActiveCfg = Debug|Any CPU
		{B5DB4110-7051-4D81-A887-1F85D23AF6BE}.Debug|x86.Build.0 = Debug|Any CPU
		{B5DB4110-7051-4D81-A887-1F85D23AF6BE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B5DB4110-7051-4D81-A887-1F85D23AF6BE}.Release|Any CPU.Build.0 = Release|Any CPU
		{B5DB4110-7051-4D81-A887-1F85D23AF6BE}.Release|x64.ActiveCfg = Release|Any CPU
		{B5DB4110-7051-4D81-A887-1F85D23AF6BE}.Release|x64.Build.0 = Release|Any CPU
		{B5DB4110-7051-4D81-A887-1F85D23AF6BE}.Release|x86.ActiveCfg = Release|Any CPU
		{B5DB4110-7051-4D81-A887-1F85D23AF6BE}.Release|x86.Build.0 = Release|Any CPU
		{A140AB10-E232-45FE-AD0B-78F7DF3EB9C1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A140AB10-E232-45FE-AD0B-78F7DF3EB9C1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A140AB10-E232-45FE-AD0B-78F7DF3EB9C1}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A140AB10-E232-45FE-AD0B-78F7DF3EB9C1}.Debug|x64.Build.0 = Debug|Any CPU
		{A140AB10-E232-45FE-AD0B-78F7DF3EB9C1}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A140AB10-E232-45FE-AD0B-78F7DF3EB9C1}.Debug|x86.Build.0 = Debug|Any CPU
		{A140AB10-E232-45FE-AD0B-78F7DF3EB9C1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A140AB10-E232-45FE-AD0B-78F7DF3EB9C1}.Release|Any CPU.Build.0 = Release|Any CPU
		{A140AB10-E232-45FE-AD0B-78F7DF3EB9C1}.Release|x64.ActiveCfg = Release|Any CPU
		{A140AB10-E232-45FE-AD0B-78F7DF3EB9C1}.Release|x64.Build.0 = Release|Any CPU
		{A140AB10-E232-45FE-AD0B-78F7DF3EB9C1}.Release|x86.ActiveCfg = Release|Any CPU
		{A140AB10-E232-45FE-AD0B-78F7DF3EB9C1}.Release|x86.Build.0 = Release|Any CPU
		{69277689-187C-4BE7-93CF-DEE64B822B7F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{69277689-187C-4BE7-93CF-DEE64B822B7F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{69277689-187C-4BE7-93CF-DEE64B822B7F}.Debug|x64.ActiveCfg = Debug|Any CPU
		{69277689-187C-4BE7-93CF-DEE64B822B7F}.Debug|x64.Build.0 = Debug|Any CPU
		{69277689-187C-4BE7-93CF-DEE64B822B7F}.Debug|x86.ActiveCfg = Debug|Any CPU
		{69277689-187C-4BE7-93CF-DEE64B822B7F}.Debug|x86.Build.0 = Debug|Any CPU
		{69277689-187C-4BE7-93CF-DEE64B822B7F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{69277689-187C-4BE7-93CF-DEE64B822B7F}.Release|Any CPU.Build.0 = Release|Any CPU
		{69277689-187C-4BE7-93CF-DEE64B822B7F}.Release|x64.ActiveCfg = Release|Any CPU
		{69277689-187C-4BE7-93CF-DEE64B822B7F}.Release|x64.Build.0 = Release|Any CPU
		{69277689-187C-4BE7-93CF-DEE64B822B7F}.Release|x86.ActiveCfg = Release|Any CPU
		{69277689-187C-4BE7-93CF-DEE64B822B7F}.Release|x86.Build.0 = Release|Any CPU
		{618EB0FE-F8BD-42BF-8696-23D34044A110}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{618EB0FE-F8BD-42BF-8696-23D34044A110}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{618EB0FE-F8BD-42BF-8696-23D34044A110}.Debug|x64.ActiveCfg = Debug|Any CPU
		{618EB0FE-F8BD-42BF-8696-23D34044A110}.Debug|x64.Build.0 = Debug|Any CPU
		{618EB0FE-F8BD-42BF-8696-23D34044A110}.Debug|x86.ActiveCfg = Debug|Any CPU
		{618EB0FE-F8BD-42BF-8696-23D34044A110}.Debug|x86.Build.0 = Debug|Any CPU
		{618EB0FE-F8BD-42BF-8696-23D34044A110}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{618EB0FE-F8BD-42BF-8696-23D34044A110}.Release|Any CPU.Build.0 = Release|Any CPU
		{618EB0FE-F8BD-42BF-8696-23D34044A110}.Release|x64.ActiveCfg = Release|Any CPU
		{618EB0FE-F8BD-42BF-8696-23D34044A110}.Release|x64.Build.0 = Release|Any CPU
		{618EB0FE-F8BD-42BF-8696-23D34044A110}.Release|x86.ActiveCfg = Release|Any CPU
		{618EB0FE-F8BD-42BF-8696-23D34044A110}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
