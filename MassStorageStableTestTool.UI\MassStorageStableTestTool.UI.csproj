﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <ApplicationIcon>favicon.ico</ApplicationIcon>
    <UseWPF>true</UseWPF>
    </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="NLog.Extensions.Logging" Version="5.3.5" />
    <PackageReference Include="NLog" Version="5.2.5" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\MassStorageStableTestTool.Core\MassStorageStableTestTool.Core.csproj" />
    <ProjectReference Include="..\MassStorageStableTestTool.Automation\MassStorageStableTestTool.Automation.csproj" />
    <ProjectReference Include="..\MassStorageStableTestTool.Reports\MassStorageStableTestTool.Reports.csproj" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Resources\Strings.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Strings.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <Compile Update="Resources\Strings.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Strings.resx</DependentUpon>
    </Compile>
    <EmbeddedResource Update="Resources\Strings.zh-CN.resx"/>
      <!-- <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Strings.zh-CN.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <Compile Update="Resources\Strings.zh-CN.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Strings.zh-CN.resx</DependentUpon>
    </Compile> -->
  </ItemGroup>
</Project>
