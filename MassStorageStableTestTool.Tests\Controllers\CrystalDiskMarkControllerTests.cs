using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using FluentAssertions;
using MassStorageStableTestTool.Core.Models;
using MassStorageStableTestTool.Core.Enums;
using MassStorageStableTestTool.Automation.Controllers;
using MassStorageStableTestTool.Automation.GUI;

namespace MassStorageStableTestTool.Tests.Controllers
{
    /// <summary>
    /// CrystalDiskMarkController单元测试
    /// </summary>
    public class CrystalDiskMarkControllerTests
    {
        private readonly Mock<AutomationHelper> _mockAutomationHelper;
        private readonly Mock<ILogger<CrystalDiskMarkController>> _mockLogger;
        private readonly TestToolConfig _testConfig;

        public CrystalDiskMarkControllerTests()
        {
            _mockAutomationHelper = new Mock<AutomationHelper>();
            _mockLogger = new Mock<ILogger<CrystalDiskMarkController>>();
            
            _testConfig = new TestToolConfig
            {
                Name = "CrystalDiskMark",
                Type = TestToolType.GUI,
                ExecutablePath = @".\test_tools\DiskMark64.exe",
                WindowTitle = "CrystalDiskMark",
                Description = "CrystalDiskMark测试工具",
                Version = "8.0.1",
                DefaultParameters = new Dictionary<string, object>
                {
                    ["TestSize"] = "1GiB",
                    ["TestCount"] = 5,
                    ["TestMode"] = "All",
                    ["QueueDepth"] = 32,
                    ["ThreadCount"] = 1
                },
                Timeouts = new TimeoutSettings
                {
                    LaunchTimeout = TimeSpan.FromSeconds(30),
                    TestTimeout = TimeSpan.FromMinutes(45),
                    ShutdownTimeout = TimeSpan.FromSeconds(10),
                    ElementTimeout = TimeSpan.FromSeconds(10)
                },
                Enabled = true
            };
        }

        /// <summary>
        /// 测试控制器基本属性
        /// </summary>
        [Fact]
        public void Constructor_ShouldInitializeCorrectly()
        {
            // Act
            var controller = new CrystalDiskMarkController(_testConfig, _mockAutomationHelper.Object, _mockLogger.Object);

            // Assert
            controller.Should().NotBeNull();
            controller.ToolName.Should().Be("CrystalDiskMark");
            controller.ToolType.Should().Be(TestToolType.GUI);
            controller.Configuration.Should().Be(_testConfig);
        }

        /// <summary>
        /// 测试构造函数参数验证
        /// </summary>
        [Fact]
        public void Constructor_WithNullAutomationHelper_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => 
                new CrystalDiskMarkController(_testConfig, null!, _mockLogger.Object));
        }

        /// <summary>
        /// 测试构造函数参数验证
        /// </summary>
        [Fact]
        public void Constructor_WithNullLogger_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => 
                new CrystalDiskMarkController(_testConfig, _mockAutomationHelper.Object, null!));
        }

        /// <summary>
        /// 测试工具可用性检查
        /// </summary>
        [Fact]
        public void IsToolAvailable_WithNonExistentExecutable_ShouldReturnFalse()
        {
            // Arrange
            var config = _testConfig.Clone();
            config.ExecutablePath = @".\non_existent\DiskMark64.exe";
            var controller = new CrystalDiskMarkController(config, _mockAutomationHelper.Object, _mockLogger.Object);

            // Act
            var result = controller.IsToolAvailable();

            // Assert
            result.Should().BeFalse();
        }

        /// <summary>
        /// 测试配置验证
        /// </summary>
        [Fact]
        public void ValidateConfiguration_WithValidConfig_ShouldReturnValid()
        {
            // Arrange
            var controller = new CrystalDiskMarkController(_testConfig, _mockAutomationHelper.Object, _mockLogger.Object);
            var testConfiguration = new TestConfiguration
            {
                TargetDrive = "D:\\",
                Parameters = new Dictionary<string, object>
                {
                    ["TestSize"] = "1GiB",
                    ["TestCount"] = 5
                }
            };

            // Act
            var (isValid, errors) = controller.ValidateConfiguration(testConfiguration);

            // Assert
            isValid.Should().BeTrue();
            errors.Should().BeEmpty();
        }

        /// <summary>
        /// 测试配置验证 - 无效驱动器
        /// </summary>
        [Fact]
        public void ValidateConfiguration_WithInvalidDrive_ShouldReturnInvalid()
        {
            // Arrange
            var controller = new CrystalDiskMarkController(_testConfig, _mockAutomationHelper.Object, _mockLogger.Object);
            var testConfiguration = new TestConfiguration
            {
                TargetDrive = "", // 空驱动器路径
                Parameters = new Dictionary<string, object>()
            };

            // Act
            var (isValid, errors) = controller.ValidateConfiguration(testConfiguration);

            // Assert
            isValid.Should().BeFalse();
            errors.Should().NotBeEmpty();
            errors.Should().Contain(error => error.Contains("目标驱动器"));
        }

        /// <summary>
        /// 测试支持的参数获取
        /// </summary>
        [Fact]
        public void GetSupportedParameters_ShouldReturnExpectedParameters()
        {
            // Arrange
            var controller = new CrystalDiskMarkController(_testConfig, _mockAutomationHelper.Object, _mockLogger.Object);

            // Act
            var parameters = controller.GetSupportedParameters();

            // Assert
            parameters.Should().NotBeEmpty();
            parameters.Should().ContainKey("TestSize");
            parameters.Should().ContainKey("TestCount");
            parameters.Should().ContainKey("TestMode");
            
            // 验证参数类型和描述
            parameters["TestSize"].Type.Should().Be(typeof(string));
            parameters["TestSize"].Description.Should().NotBeNullOrEmpty();
            
            parameters["TestCount"].Type.Should().Be(typeof(int));
            parameters["TestCount"].Description.Should().NotBeNullOrEmpty();
        }

        /// <summary>
        /// 测试停止测试功能
        /// </summary>
        [Fact]
        public async Task StopTestAsync_ShouldReturnTrue()
        {
            // Arrange
            var controller = new CrystalDiskMarkController(_testConfig, _mockAutomationHelper.Object, _mockLogger.Object);

            // Act
            var result = await controller.StopTestAsync();

            // Assert
            result.Should().BeTrue();
        }

        /// <summary>
        /// 测试获取当前状态
        /// </summary>
        [Fact]
        public void GetCurrentStatus_InitialState_ShouldReturnNotStarted()
        {
            // Arrange
            var controller = new CrystalDiskMarkController(_testConfig, _mockAutomationHelper.Object, _mockLogger.Object);

            // Act
            var status = controller.GetCurrentStatus();

            // Assert
            status.Should().Be(TestStatus.NotStarted);
        }

        /// <summary>
        /// 测试准备测试环境
        /// </summary>
        [Fact]
        public async Task PrepareTestEnvironmentAsync_ShouldReturnTrue()
        {
            // Arrange
            var controller = new CrystalDiskMarkController(_testConfig, _mockAutomationHelper.Object, _mockLogger.Object);
            var testConfiguration = new TestConfiguration
            {
                TargetDrive = "D:\\",
                Parameters = new Dictionary<string, object>()
            };

            // Act
            var result = await controller.PrepareTestEnvironmentAsync(testConfiguration, CancellationToken.None);

            // Assert
            result.Should().BeTrue();
        }

        /// <summary>
        /// 测试清理测试环境
        /// </summary>
        [Fact]
        public async Task CleanupTestEnvironmentAsync_ShouldReturnTrue()
        {
            // Arrange
            var controller = new CrystalDiskMarkController(_testConfig, _mockAutomationHelper.Object, _mockLogger.Object);
            var testConfiguration = new TestConfiguration
            {
                TargetDrive = "D:\\",
                Parameters = new Dictionary<string, object>()
            };

            // Act
            var result = await controller.CleanupTestEnvironmentAsync(testConfiguration, CancellationToken.None);

            // Assert
            result.Should().BeTrue();
        }

        /// <summary>
        /// 测试获取工具版本
        /// </summary>
        [Fact]
        public async Task GetToolVersionAsync_ShouldReturnVersion()
        {
            // Arrange
            var controller = new CrystalDiskMarkController(_testConfig, _mockAutomationHelper.Object, _mockLogger.Object);

            // Act
            var version = await controller.GetToolVersionAsync();

            // Assert
            version.Should().NotBeNullOrEmpty();
        }
    }

    /// <summary>
    /// CrystalDiskMarkController集成测试 - 需要真实的CrystalDiskMark环境
    /// </summary>
    public class CrystalDiskMarkControllerIntegrationTests
    {
        /// <summary>
        /// 测试完整的CrystalDiskMark工作流程（需要真实环境）
        /// </summary>
        [Fact(Skip = "需要真实的CrystalDiskMark.exe文件和GUI环境")]
        public async Task ExecuteCrystalDiskMarkWorkflow_WithRealEnvironment_ShouldCompleteSuccessfully()
        {
            // 这个测试需要：
            // 1. 真实的CrystalDiskMark.exe文件
            // 2. GUI环境（不能在无头环境中运行）
            // 3. 可写的测试驱动器

            // Arrange
            var config = new TestToolConfig
            {
                Name = "CrystalDiskMark",
                Type = TestToolType.GUI,
                ExecutablePath = @".\third part tools\CrystalDiskMark8.0.1\DiskMark64.exe",
                WindowTitle = "CrystalDiskMark",
                Timeouts = new TimeoutSettings
                {
                    LaunchTimeout = TimeSpan.FromSeconds(30),
                    TestTimeout = TimeSpan.FromMinutes(10), // 短时间测试
                    ElementTimeout = TimeSpan.FromSeconds(10)
                }
            };

            var testConfig = new TestConfiguration
            {
                TargetDrive = "D:\\", // 根据实际环境调整
                Parameters = new Dictionary<string, object>
                {
                    ["TestSize"] = "100MiB", // 小尺寸快速测试
                    ["TestCount"] = 1
                }
            };

            // 在真实实现中，这里会：
            // 1. 启动CrystalDiskMark.exe
            // 2. 选择目标驱动器
            // 3. 设置测试参数（大小、次数）
            // 4. 点击"All"按钮开始测试
            // 5. 等待测试完成
            // 6. 解析性能结果

            // Act & Assert
            // var automationHelper = new AutomationHelper();
            // var logger = new Mock<ILogger<CrystalDiskMarkController>>().Object;
            // var controller = new CrystalDiskMarkController(config, automationHelper, logger);
            // var result = await controller.ExecuteTestAsync(testConfig, CancellationToken.None);
            // result.Should().NotBeNull();
            // result.Success.Should().BeTrue();
            // result.Data.Should().ContainKey("SequentialRead_MBps");
            // result.Data.Should().ContainKey("SequentialWrite_MBps");

            await Task.CompletedTask; // 占位符
        }
    }
}
