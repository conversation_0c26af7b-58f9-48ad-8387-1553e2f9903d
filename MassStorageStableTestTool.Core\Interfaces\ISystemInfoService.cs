using MassStorageStableTestTool.Core.Models;

namespace MassStorageStableTestTool.Core.Interfaces;

/// <summary>
/// 系统信息服务接口
/// </summary>
public interface ISystemInfoService
{
    /// <summary>
    /// 获取系统信息
    /// </summary>
    /// <returns>系统信息</returns>
    Task<SystemInfo> GetSystemInfoAsync();

    /// <summary>
    /// 获取驱动器信息
    /// </summary>
    /// <param name="driveName">驱动器名称</param>
    /// <returns>驱动器信息</returns>
    Task<Models.DriveInfo?> GetDriveInfoAsync(string driveName);

    /// <summary>
    /// 获取所有可用驱动器
    /// </summary>
    /// <returns>驱动器信息列表</returns>
    Task<List<Models.DriveInfo>> GetAvailableDrivesAsync();

    /// <summary>
    /// 获取可移动驱动器列表
    /// </summary>
    /// <returns>可移动驱动器列表</returns>
    Task<List<Models.DriveInfo>> GetRemovableDrivesAsync();

    /// <summary>
    /// 检查驱动器是否适合测试
    /// </summary>
    /// <param name="driveName">驱动器名称</param>
    /// <param name="requiredSpace">所需空间（字节）</param>
    /// <returns>检查结果</returns>
    Task<(bool IsSuitable, List<string> Issues)> CheckDriveSuitabilityAsync(string driveName, long requiredSpace = 0);

    /// <summary>
    /// 获取系统性能信息
    /// </summary>
    /// <returns>性能信息</returns>
    Task<SystemPerformanceInfo> GetSystemPerformanceAsync();

    /// <summary>
    /// 监控系统性能
    /// </summary>
    /// <param name="interval">监控间隔</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>性能监控任务</returns>
    Task StartPerformanceMonitoringAsync(TimeSpan interval, CancellationToken cancellationToken);

    /// <summary>
    /// 停止性能监控
    /// </summary>
    /// <returns>停止结果</returns>
    Task<bool> StopPerformanceMonitoringAsync();

    /// <summary>
    /// 获取已安装的软件列表
    /// </summary>
    /// <returns>已安装软件列表</returns>
    Task<List<InstalledSoftware>> GetInstalledSoftwareAsync();

    /// <summary>
    /// 检查软件是否已安装
    /// </summary>
    /// <param name="softwareName">软件名称</param>
    /// <returns>是否已安装</returns>
    Task<bool> IsSoftwareInstalledAsync(string softwareName);

    /// <summary>
    /// 获取环境变量
    /// </summary>
    /// <returns>环境变量字典</returns>
    Task<Dictionary<string, string>> GetEnvironmentVariablesAsync();

    /// <summary>
    /// 获取网络适配器信息
    /// </summary>
    /// <returns>网络适配器列表</returns>
    Task<List<NetworkAdapter>> GetNetworkAdaptersAsync();

    /// <summary>
    /// 性能数据更新事件
    /// </summary>
    event EventHandler<PerformanceDataEventArgs>? PerformanceDataUpdated;

    /// <summary>
    /// 驱动器状态变化事件
    /// </summary>
    event EventHandler<DriveStatusChangedEventArgs>? DriveStatusChanged;
}

/// <summary>
/// 系统性能信息
/// </summary>
public class SystemPerformanceInfo
{
    /// <summary>
    /// CPU使用率 (%)
    /// </summary>
    public double CpuUsage { get; set; }

    /// <summary>
    /// 内存使用率 (%)
    /// </summary>
    public double MemoryUsage { get; set; }

    /// <summary>
    /// 磁盘使用率 (%)
    /// </summary>
    public double DiskUsage { get; set; }

    /// <summary>
    /// 网络使用率 (%)
    /// </summary>
    public double NetworkUsage { get; set; }

    /// <summary>
    /// 可用内存 (GB)
    /// </summary>
    public double AvailableMemory { get; set; }

    /// <summary>
    /// 总内存 (GB)
    /// </summary>
    public double TotalMemory { get; set; }

    /// <summary>
    /// 系统温度 (°C)
    /// </summary>
    public double? SystemTemperature { get; set; }

    /// <summary>
    /// 运行的进程数
    /// </summary>
    public int ProcessCount { get; set; }

    /// <summary>
    /// 系统运行时间
    /// </summary>
    public TimeSpan Uptime { get; set; }

    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;

    /// <summary>
    /// 详细的CPU信息
    /// </summary>
    public Dictionary<string, double> CpuDetails { get; set; } = new();

    /// <summary>
    /// 详细的内存信息
    /// </summary>
    public Dictionary<string, double> MemoryDetails { get; set; } = new();

    /// <summary>
    /// 详细的磁盘信息
    /// </summary>
    public Dictionary<string, double> DiskDetails { get; set; } = new();
}

/// <summary>
/// 性能数据事件参数
/// </summary>
public class PerformanceDataEventArgs : EventArgs
{
    /// <summary>
    /// 性能信息
    /// </summary>
    public SystemPerformanceInfo PerformanceInfo { get; set; } = new();

    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;
}

/// <summary>
/// 驱动器状态变化事件参数
/// </summary>
public class DriveStatusChangedEventArgs : EventArgs
{
    /// <summary>
    /// 驱动器名称
    /// </summary>
    public string DriveName { get; set; } = string.Empty;

    /// <summary>
    /// 变化类型
    /// </summary>
    public DriveChangeType ChangeType { get; set; }

    /// <summary>
    /// 驱动器信息
    /// </summary>
    public Models.DriveInfo? DriveInfo { get; set; }

    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;
}

/// <summary>
/// 驱动器变化类型
/// </summary>
public enum DriveChangeType
{
    /// <summary>
    /// 插入
    /// </summary>
    Inserted,

    /// <summary>
    /// 移除
    /// </summary>
    Removed,

    /// <summary>
    /// 状态变化
    /// </summary>
    StatusChanged,

    /// <summary>
    /// 空间变化
    /// </summary>
    SpaceChanged
}
