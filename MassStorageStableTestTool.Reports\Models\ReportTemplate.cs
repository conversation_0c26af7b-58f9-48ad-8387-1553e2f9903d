using MassStorageStableTestTool.Core.Enums;

namespace MassStorageStableTestTool.Reports.Models;

/// <summary>
/// 报告模板
/// </summary>
public class ReportTemplate
{
    /// <summary>
    /// 模板名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 模板显示名称
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// 模板描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 支持的报告格式
    /// </summary>
    public ReportFormat Format { get; set; }

    /// <summary>
    /// 模板内容
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// 模板版本
    /// </summary>
    public string Version { get; set; } = "1.0";

    /// <summary>
    /// 模板作者
    /// </summary>
    public string Author { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 最后修改时间
    /// </summary>
    public DateTime ModifiedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 是否为默认模板
    /// </summary>
    public bool IsDefault { get; set; } = false;

    /// <summary>
    /// 是否为内置模板
    /// </summary>
    public bool IsBuiltIn { get; set; } = false;

    /// <summary>
    /// 模板标签
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// 模板参数
    /// </summary>
    public Dictionary<string, object> Parameters { get; set; } = new();

    /// <summary>
    /// 依赖的资源文件
    /// </summary>
    public List<string> Dependencies { get; set; } = new();
}
