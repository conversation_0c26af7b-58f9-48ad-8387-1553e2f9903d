<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 颜色资源 -->
    <SolidColorBrush x:Key="PrimaryColor" Color="#2196F3"/>
    <SolidColorBrush x:Key="PrimaryDarkColor" Color="#1976D2"/>
    <SolidColorBrush x:Key="PrimaryLightColor" Color="#BBDEFB"/>
    <SolidColorBrush x:Key="SuccessColor" Color="#4CAF50"/>
    <SolidColorBrush x:Key="WarningColor" Color="#FF9800"/>
    <SolidColorBrush x:Key="ErrorColor" Color="#F44336"/>
    <SolidColorBrush x:Key="BackgroundColor" Color="#FAFAFA"/>
    <SolidColorBrush x:Key="SurfaceColor" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="BorderColor" Color="#E0E0E0"/>
    <SolidColorBrush x:Key="TextPrimaryColor" Color="#212121"/>
    <SolidColorBrush x:Key="TextSecondaryColor" Color="#757575"/>

    <!-- 卡片样式 -->
    <Style x:Key="CardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource SurfaceColor}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="4"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 主要按钮样式 -->
    <Style x:Key="PrimaryButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource PrimaryColor}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}" 
                            CornerRadius="4"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center" 
                                        VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{StaticResource PrimaryDarkColor}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="#1565C0"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Background" Value="#BDBDBD"/>
                            <Setter Property="Foreground" Value="#FFFFFF"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 次要按钮样式 -->
    <Style x:Key="SecondaryButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryColor}"/>
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryColor}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}" 
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center" 
                                        VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{StaticResource PrimaryLightColor}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="#E3F2FD"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 标题样式 -->
    <Style x:Key="CardTitleStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}"/>
        <Setter Property="Margin" Value="0,0,0,12"/>
    </Style>

    <!-- 标签样式 -->
    <Style x:Key="LabelStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryColor}"/>
        <Setter Property="Margin" Value="0,0,0,4"/>
    </Style>

    <!-- 进度条样式 -->
    <Style x:Key="ModernProgressBarStyle" TargetType="ProgressBar">
        <Setter Property="Height" Value="20"/>
        <Setter Property="Background" Value="{StaticResource BorderColor}"/>
        <Setter Property="Foreground" Value="{StaticResource SuccessColor}"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ProgressBar">
                    <Border Background="{TemplateBinding Background}"
                            CornerRadius="10"
                            ClipToBounds="True">
                        <Grid>
                            <Rectangle Name="PART_Track" Fill="{TemplateBinding Background}"/>
                            <Rectangle Name="PART_Indicator"
                                     Fill="{TemplateBinding Foreground}"
                                     HorizontalAlignment="Left"/>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- ComboBox样式 -->
    <Style x:Key="ModernComboBoxStyle" TargetType="ComboBox">
        <Setter Property="Height" Value="32"/>
        <Setter Property="Padding" Value="8,4"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Background" Value="{StaticResource SurfaceColor}"/>
    </Style>

    <!-- CheckBox样式 -->
    <Style x:Key="ModernCheckBoxStyle" TargetType="CheckBox">
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}"/>
        <Setter Property="Margin" Value="0,2"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <!-- Expander样式 -->
    <Style x:Key="ModernExpanderStyle" TargetType="Expander">
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Margin" Value="0,0,0,8"/>
    </Style>

    <!-- ScrollViewer样式 -->
    <Style x:Key="ModernScrollViewerStyle" TargetType="ScrollViewer">
        <Setter Property="VerticalScrollBarVisibility" Value="Auto"/>
        <Setter Property="HorizontalScrollBarVisibility" Value="Auto"/>
        <Setter Property="CanContentScroll" Value="True"/>
    </Style>

    <!-- 状态栏样式 -->
    <Style x:Key="StatusBarStyle" TargetType="StatusBar">
        <Setter Property="Background" Value="{StaticResource SurfaceColor}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
        <Setter Property="BorderThickness" Value="0,1,0,0"/>
        <Setter Property="Height" Value="24"/>
    </Style>

</ResourceDictionary>
