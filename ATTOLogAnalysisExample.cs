using System;
using System.IO;

namespace ATTOLogAnalysisExample
{
    /// <summary>
    /// ATTO日志分析结果
    /// </summary>
    public class ATTOLogAnalysisResult
    {
        public bool IsSuccess { get; set; }
        public string SuccessMessage { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
    }

    /// <summary>
    /// ATTO日志分析器示例
    /// </summary>
    public class ATTOLogAnalyzer
    {
        /// <summary>
        /// 分析ATTO日志内容判断测试是否成功
        /// </summary>
        /// <param name="logContent">日志内容</param>
        /// <returns>分析结果</returns>
        public static ATTOLogAnalysisResult AnalyzeLogForSuccess(string logContent)
        {
            var result = new ATTOLogAnalysisResult();

            if (string.IsNullOrWhiteSpace(logContent))
            {
                result.IsSuccess = false;
                result.ErrorMessage = "日志内容为空";
                return result;
            }

            try
            {
                // 将日志内容按行分割进行分析
                var lines = logContent.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

                bool foundRunningMessage = false;
                bool foundCompletedMessage = false;
                string benchmarkFileName = string.Empty;

                foreach (var line in lines)
                {
                    var trimmedLine = line.Trim();

                    // 检查是否包含运行基准测试的消息
                    if (trimmedLine.StartsWith("Running ", StringComparison.OrdinalIgnoreCase))
                    {
                        foundRunningMessage = true;
                        // 提取基准测试文件名
                        var parts = trimmedLine.Split(' ');
                        if (parts.Length > 1)
                        {
                            benchmarkFileName = parts[1].Replace("....", "");
                        }
                        Console.WriteLine($"发现运行消息: {trimmedLine}");
                    }

                    // 检查是否包含测试成功完成的消息
                    if (trimmedLine.Contains("Testing completed", StringComparison.OrdinalIgnoreCase) &&
                        trimmedLine.Contains("sucessfully", StringComparison.OrdinalIgnoreCase))
                    {
                        foundCompletedMessage = true;
                        Console.WriteLine($"发现成功完成消息: {trimmedLine}");
                    }

                    // 检查是否有错误消息
                    if (trimmedLine.Contains("error", StringComparison.OrdinalIgnoreCase) ||
                        trimmedLine.Contains("failed", StringComparison.OrdinalIgnoreCase) ||
                        trimmedLine.Contains("exception", StringComparison.OrdinalIgnoreCase))
                    {
                        result.IsSuccess = false;
                        result.ErrorMessage = $"日志中发现错误信息: {trimmedLine}";
                        return result;
                    }
                }

                // 判断测试是否成功
                if (foundRunningMessage && foundCompletedMessage)
                {
                    result.IsSuccess = true;
                    result.SuccessMessage = $"ATTO测试成功完成，基准文件: {benchmarkFileName}";
                }
                else if (foundRunningMessage && !foundCompletedMessage)
                {
                    result.IsSuccess = false;
                    result.ErrorMessage = "测试已开始但未找到成功完成的消息";
                }
                else if (!foundRunningMessage)
                {
                    result.IsSuccess = false;
                    result.ErrorMessage = "未找到测试开始的消息";
                }
                else
                {
                    result.IsSuccess = false;
                    result.ErrorMessage = "日志分析结果不明确";
                }
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.ErrorMessage = $"分析日志时发生异常: {ex.Message}";
                Console.WriteLine($"分析ATTO日志时发生异常: {ex}");
            }

            return result;
        }
    }

    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("ATTO日志分析示例");
            Console.WriteLine("================");

            // 您提供的成功日志示例
            var successLogContent = @"Friday, October 10, 2025   9:57 AM
Running ATTO_Test_20251010_095530_J.bmk....
Testing completed sucessfully.";

            Console.WriteLine("\n分析您提供的日志内容:");
            Console.WriteLine("----------------------");
            Console.WriteLine(successLogContent);
            Console.WriteLine();

            var result = ATTOLogAnalyzer.AnalyzeLogForSuccess(successLogContent);

            Console.WriteLine("分析结果:");
            Console.WriteLine($"是否成功: {result.IsSuccess}");
            if (result.IsSuccess)
            {
                Console.WriteLine($"成功消息: {result.SuccessMessage}");
            }
            else
            {
                Console.WriteLine($"错误消息: {result.ErrorMessage}");
            }

            Console.WriteLine("\n分析逻辑说明:");
            Console.WriteLine("1. 检查是否包含 'Running' 开头的消息");
            Console.WriteLine("2. 检查是否包含 'Testing completed sucessfully' 消息");
            Console.WriteLine("3. 检查是否包含错误关键词: error, failed, exception");
            Console.WriteLine("4. 只有同时找到运行消息和成功完成消息，且没有错误消息时，才判定为成功");

            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
    }
}
