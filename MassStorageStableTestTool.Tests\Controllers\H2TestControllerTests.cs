using AutoFixture;
using AutoFixture.Xunit2;
using FluentAssertions;
using MassStorageStableTestTool.Automation.Controllers;
using MassStorageStableTestTool.Automation.GUI;
using MassStorageStableTestTool.Core.Models;
using MassStorageStableTestTool.Core.Interfaces;
using MassStorageStableTestTool.Tests.TestHelpers;
using Microsoft.Extensions.Logging;
using Moq;
using System.Diagnostics;
using Xunit;

namespace MassStorageStableTestTool.Tests.Controllers;

/// <summary>
/// H2TestController单元测试 - 简化版本，专注于核心逻辑测试
/// </summary>
public class H2TestControllerTests : IDisposable
{
    private readonly Mock<ILogger<H2TestController>> _mockLogger;
    private readonly TestToolConfig _testConfig;
    private readonly Fixture _fixture;

    public H2TestControllerTests()
    {
        _fixture = new Fixture();
        _mockLogger = MockHelper.CreateMockLogger<H2TestController>();

        // 创建测试配置
        _testConfig = TestDataBuilder.CreateH2TestConfig();
    }



    /// <summary>
    /// 测试工具配置属性
    /// </summary>
    [Fact]
    public void TestConfig_ShouldHaveCorrectProperties()
    {
        // Assert
        _testConfig.Name.Should().Be("H2testw");
        _testConfig.Type.Should().Be(Core.Enums.TestToolType.GUI);
        _testConfig.ExecutablePath.Should().NotBeNullOrEmpty();
        _testConfig.WindowTitle.Should().Be("H2testw");
        _testConfig.DefaultParameters.Should().ContainKey("TestAllSpace");
        _testConfig.UIConfig.Should().ContainKey("DriveComboBoxName");
        _testConfig.OutputParsing.ParsingRules.Should().ContainKey("WriteSpeed");
    }

    /// <summary>
    /// 测试构造函数参数验证 - 配置为空
    /// </summary>
    [Fact]
    public void Constructor_WithNullConfiguration_ShouldThrowArgumentNullException()
    {
        // Arrange
        var mockAutomationHelper = new Mock<AutomationHelper>();

        // Act & Assert
        var act = () => new H2TestController(null!, mockAutomationHelper.Object, _mockLogger.Object);
        act.Should().Throw<ArgumentNullException>().WithParameterName("configuration");
    }

    /// <summary>
    /// 测试构造函数参数验证 - 日志记录器为空
    /// </summary>
    [Fact]
    public void Constructor_WithNullLogger_ShouldThrowArgumentNullException()
    {
        // Arrange
        var mockAutomationHelper = new Mock<AutomationHelper>();

        // Act & Assert
        var act = () => new H2TestController(_testConfig, mockAutomationHelper.Object, null!);
        act.Should().Throw<ArgumentNullException>().WithParameterName("logger");
    }

    /// <summary>
    /// 测试TestConfiguration创建
    /// </summary>
    [Fact]
    public void CreateTestConfiguration_ShouldHaveCorrectProperties()
    {
        // Arrange & Act
        var config = TestDataBuilder.CreateTestConfiguration("C:\\");

        // Assert
        config.TargetDrive.Should().Be("C:\\");
        config.Parameters.Should().NotBeNull();
        config.ConfigurationName.Should().Be("单元测试");
        config.Description.Should().Be("单元测试配置");
    }

    /// <summary>
    /// 测试TestResult创建 - 成功场景
    /// </summary>
    [Fact]
    public void CreateSuccessfulTestResult_ShouldHaveCorrectProperties()
    {
        // Arrange & Act
        var result = TestDataBuilder.CreateSuccessfulTestResult("H2testw");

        // Assert
        result.ToolName.Should().Be("H2testw");
        result.Success.Should().BeTrue();
        result.StartTime.Should().BeCloseTo(DateTime.Now, TimeSpan.FromMinutes(15));
        result.EndTime.Should().BeCloseTo(DateTime.Now, TimeSpan.FromMinutes(5));
        result.GetPerformanceData<double>("WriteSpeed").Should().Be(25.5);
        result.GetPerformanceData<double>("ReadSpeed").Should().Be(30.2);
        result.GetPerformanceData<int>("ErrorCount").Should().Be(0);
    }

    /// <summary>
    /// 测试TestResult创建 - 失败场景
    /// </summary>
    [Fact]
    public void CreateFailedTestResult_ShouldHaveCorrectProperties()
    {
        // Arrange & Act
        var result = TestDataBuilder.CreateFailedTestResult("H2testw", "测试失败");

        // Assert
        result.ToolName.Should().Be("H2testw");
        result.Success.Should().BeFalse();
        result.ErrorMessage.Should().Be("测试失败");
        result.GetPerformanceData<int>("ErrorCount").Should().Be(5);
    }

    /// <summary>
    /// 测试超时设置创建
    /// </summary>
    [Fact]
    public void CreateTimeoutSettings_ShouldHaveCorrectDefaults()
    {
        // Arrange & Act
        var timeouts = TestDataBuilder.CreateTimeoutSettings();

        // Assert
        timeouts.LaunchTimeout.Should().Be(TimeSpan.FromSeconds(30));
        timeouts.TestTimeout.Should().Be(TimeSpan.FromMinutes(30));
        timeouts.ShutdownTimeout.Should().Be(TimeSpan.FromSeconds(10));
        timeouts.ElementTimeout.Should().Be(TimeSpan.FromSeconds(10));
    }

    /// <summary>
    /// 测试输出解析配置创建
    /// </summary>
    [Fact]
    public void CreateOutputParsingConfig_ShouldHaveCorrectRules()
    {
        // Arrange & Act
        var config = TestDataBuilder.CreateOutputParsingConfig();

        // Assert
        config.Format.Should().Be("text");
        config.ParsingRules.Should().ContainKey("WriteSpeed");
        config.ParsingRules.Should().ContainKey("ReadSpeed");
        config.ParsingRules.Should().ContainKey("TestResult");
        config.ParsingRules.Should().ContainKey("ErrorCount");
    }

    /// <summary>
    /// 测试进度事件参数创建
    /// </summary>
    [Fact]
    public void CreateProgressEventArgs_ShouldHaveCorrectProperties()
    {
        // Arrange & Act
        var args = TestDataBuilder.CreateProgressEventArgs(75, "测试进行中...");

        // Assert
        args.Progress.Should().Be(75);
        args.Status.Should().Be("测试进行中...");
    }

    /// <summary>
    /// 释放测试资源
    /// </summary>
    public void Dispose()
    {
        // 简化的Dispose，不需要释放复杂的Mock对象
    }
}

/// <summary>
/// H2TestController结果解析测试 - 基于真实的H2testw输出格式
/// </summary>
public class H2TestControllerResultParsingTests
{
    /// <summary>
    /// 测试解析成功的H2testw结果
    /// </summary>
    [Fact]
    public void ParseH2TestResult_WithSuccessfulOutput_ShouldExtractCorrectValues()
    {
        // Arrange - 基于图3的真实输出格式
        var h2testOutput = @"
H2testw | Progress

Writing
100 MByte
2 s
27.2 MByte/s

Verifying
100 MByte
2 s
33.9 MByte/s

Warning: Only 100 of 119270 MByte tested.
Test finished without errors.
You can now delete the test files *.h2w or verify them again.
Writing speed: 27.2 MByte/s
Reading speed: 33.9 MByte/s
H2testw v1.4
";

        // Act - 使用正则表达式解析结果（模拟H2TestController的解析逻辑）
        var writeSpeedMatch = System.Text.RegularExpressions.Regex.Match(h2testOutput, @"Writing speed: ([\d.]+) MByte/s");
        var readSpeedMatch = System.Text.RegularExpressions.Regex.Match(h2testOutput, @"Reading speed: ([\d.]+) MByte/s");
        var testFinishedMatch = System.Text.RegularExpressions.Regex.Match(h2testOutput, @"Test (finished|failed)");
        var errorMatch = System.Text.RegularExpressions.Regex.Match(h2testOutput, @"(\d+) errors");

        // Assert
        writeSpeedMatch.Success.Should().BeTrue();
        writeSpeedMatch.Groups[1].Value.Should().Be("27.2");

        readSpeedMatch.Success.Should().BeTrue();
        readSpeedMatch.Groups[1].Value.Should().Be("33.9");

        testFinishedMatch.Success.Should().BeTrue();
        testFinishedMatch.Groups[1].Value.Should().Be("finished");

        // 没有错误的情况下，错误匹配应该失败
        errorMatch.Success.Should().BeFalse();
    }

    /// <summary>
    /// 测试解析有错误的H2testw结果
    /// </summary>
    [Fact]
    public void ParseH2TestResult_WithErrors_ShouldDetectErrors()
    {
        // Arrange - 模拟有错误的输出
        var h2testOutputWithErrors = @"
H2testw | Progress

Writing
50 MByte
1 s
25.0 MByte/s

Verifying
50 MByte
2 s
20.0 MByte/s

Test finished with 5 errors.
Writing speed: 25.0 MByte/s
Reading speed: 20.0 MByte/s
H2testw v1.4
";

        // Act
        var writeSpeedMatch = System.Text.RegularExpressions.Regex.Match(h2testOutputWithErrors, @"Writing speed: ([\d.]+) MByte/s");
        var readSpeedMatch = System.Text.RegularExpressions.Regex.Match(h2testOutputWithErrors, @"Reading speed: ([\d.]+) MByte/s");
        var errorMatch = System.Text.RegularExpressions.Regex.Match(h2testOutputWithErrors, @"(\d+) errors");

        // Assert
        writeSpeedMatch.Groups[1].Value.Should().Be("25.0");
        readSpeedMatch.Groups[1].Value.Should().Be("20.0");
        errorMatch.Success.Should().BeTrue();
        errorMatch.Groups[1].Value.Should().Be("5");
    }

    /// <summary>
    /// 测试解析部分测试的警告信息
    /// </summary>
    [Fact]
    public void ParseH2TestResult_WithPartialTest_ShouldDetectWarning()
    {
        // Arrange - 基于图3显示的警告信息
        var h2testOutputWithWarning = @"
Warning: Only 100 of 119270 MByte tested.
Test finished without errors.
Writing speed: 27.2 MByte/s
Reading speed: 33.9 MByte/s
";

        // Act
        var warningMatch = System.Text.RegularExpressions.Regex.Match(h2testOutputWithWarning, @"Only (\d+) of (\d+) MByte tested");
        var testedSize = warningMatch.Success ? int.Parse(warningMatch.Groups[1].Value) : 0;
        var totalSize = warningMatch.Success ? int.Parse(warningMatch.Groups[2].Value) : 0;

        var testFinishedMatch = System.Text.RegularExpressions.Regex.Match(h2testOutputWithWarning, @"Test finished without errors.");
        var testFinished = testFinishedMatch.Success;

        var writeSpeedMatch = System.Text.RegularExpressions.Regex.Match(h2testOutputWithWarning, @"Writing speed: ([\d.]+) MByte/s");
        var writeSpeed = writeSpeedMatch.Success ? double.Parse(writeSpeedMatch.Groups[1].Value) : 0;

        var readSpeedMatch = System.Text.RegularExpressions.Regex.Match(h2testOutputWithWarning, @"Reading speed: ([\d.]+) MByte/s");
        var readSpeed = readSpeedMatch.Success ? double.Parse(readSpeedMatch.Groups[1].Value) : 0;

        // Assert
        warningMatch.Success.Should().BeTrue();
        testedSize.Should().Be(100);
        totalSize.Should().Be(119270);

        testFinishedMatch.Success.Should().BeTrue();
        testFinished.Should().BeTrue();

        writeSpeedMatch.Success.Should().BeTrue();
        writeSpeed.Should().Be(27.2);

        readSpeedMatch.Success.Should().BeTrue();
        readSpeed.Should().Be(33.9);

    }
}

/// <summary>
/// H2TestController工作流程测试 - 基于真实的操作步骤
/// </summary>
public class H2TestControllerWorkflowTests
{
    private readonly TestToolConfig _config;

    public H2TestControllerWorkflowTests()
    {
        _config = TestDataBuilder.CreateH2TestConfig();
    }

    /// <summary>
    /// 测试H2testw配置验证 - 基于图1的界面元素
    /// </summary>
    [Fact]
    public void ValidateH2TestConfiguration_ShouldHaveCorrectUIElements()
    {
        // Assert - 验证配置包含必要的UI元素
        _config.UIConfig.Should().ContainKey("MainWindowName");
        _config.UIConfig.Should().ContainKey("SelectTargetAutoId");
        _config.UIConfig.Should().ContainKey("WriteVerifyButtonAutoId");
        _config.UIConfig.Should().ContainKey("englishAutomationId");
        _config.UIConfig.Should().ContainKey("resultEditorAutoId");

        // 基于截图，我们知道需要这些元素
        _config.WindowTitle.Should().Be("H2testw");
        _config.DefaultParameters.Should().ContainKey("TestAllSpace");
    }

    /// <summary>
    /// 测试目标驱动器选择逻辑
    /// </summary>
    [Theory]
    [InlineData("J:\\", true)]  // 基于图2的示例
    [InlineData("C:\\", true)]
    [InlineData("Z:\\", false)] // 不存在的驱动器
    [InlineData("", false)]     // 空字符串
    [InlineData("invalid", false)] // 无效格式
    public void ValidateTargetDrive_ShouldReturnCorrectResult(string targetDrive, bool expectedValid)
    {
        // Act - 模拟驱动器验证逻辑
        bool isValid = !string.IsNullOrEmpty(targetDrive) &&
                      targetDrive.Length >= 2 &&
                      targetDrive.EndsWith("\\") &&
                      char.IsLetter(targetDrive[0]) &&
                      (targetDrive[0] != 'Z'); // 假设Z盘不存在

        // Assert
        isValid.Should().Be(expectedValid);
    }

    /// <summary>
    /// 测试数据量选择配置
    /// </summary>
    [Fact]
    public void ConfigureDataVolume_ShouldHandleBothOptions()
    {
        // Arrange - 基于图1的两个选项：all available space 和 only XXX MByte
        var testAllSpace = true;
        var testOnlySize = 100;

        // Act & Assert
        if (testAllSpace)
        {
            // 选择"all available space"选项
            _config.DefaultParameters["TestAllSpace"].Should().Be(true);
        }
        else
        {
            // 选择"only XXX MByte"选项
            testOnlySize.Should().BeGreaterThan(0);
            testOnlySize.Should().BeLessOrEqualTo(1000000); // 合理的上限
        }
    }

    /// <summary>
    /// 测试语言选择功能
    /// </summary>
    [Theory]
    [InlineData("English", true)]
    [InlineData("Deutsch", true)]
    [InlineData("Chinese", false)] // 不支持的语言
    public void SelectLanguage_ShouldValidateLanguageOptions(string language, bool isSupported)
    {
        // Act - 模拟语言选择逻辑
        var supportedLanguages = new[] { "English", "Deutsch" };
        var isLanguageSupported = supportedLanguages.Contains(language);

        // Assert
        isLanguageSupported.Should().Be(isSupported);
    }

    /// <summary>
    /// 测试确认对话框处理
    /// </summary>
    [Fact]
    public void HandleConfirmationDialog_ShouldReturnTrue()
    {
        // Arrange - 模拟可能出现的确认对话框
        var dialogTitle = "H2testw";
        var dialogMessage = "Are you sure you want to start the test?";
        var shouldConfirm = true;

        // Act - 模拟对话框处理逻辑
        var result = shouldConfirm; // 在真实实现中，这里会点击"确定"按钮

        // Assert
        result.Should().BeTrue();
        dialogTitle.Should().NotBeNullOrEmpty();
        dialogMessage.Should().NotBeNullOrEmpty();
    }

    /// <summary>
    /// 测试多实例场景下的窗口识别
    /// </summary>
    [Fact]
    public void MultiInstanceScenario_ShouldUseProcessIdForWindowIdentification()
    {
        // Arrange - 模拟多个H2testw实例的场景
        var instance1ProcessId = 1234;
        var instance2ProcessId = 5678;
        var targetDrive1 = "J:\\";
        var targetDrive2 = "K:\\";

        // Act - 验证每个实例都有唯一的进程ID
        var processIds = new[] { instance1ProcessId, instance2ProcessId };
        var drives = new[] { targetDrive1, targetDrive2 };

        // Assert - 确保可以通过进程ID区分不同的实例
        processIds.Should().HaveCount(2);
        processIds.Should().OnlyHaveUniqueItems();
        drives.Should().HaveCount(2);
        drives.Should().OnlyHaveUniqueItems();

        // 验证进程ID查找逻辑
        for (int i = 0; i < processIds.Length; i++)
        {
            var processId = processIds[i];
            var drive = drives[i];

            // 模拟通过进程ID查找窗口的逻辑
            processId.Should().BeGreaterThan(0);
            drive.Should().NotBeNullOrEmpty();

            // 在真实实现中，这里会调用：
            // _automationHelper.WaitForWindowByProcess(processId, "H2testw", timeout);
        }
    }

    /// <summary>
    /// 测试实例ID生成的唯一性
    /// </summary>
    [Fact]
    public void InstanceId_ShouldBeUnique()
    {
        // Arrange & Act - 生成多个实例ID
        var instanceIds = new HashSet<string>();
        for (int i = 0; i < 100; i++)
        {
            var instanceId = Guid.NewGuid().ToString("N")[..8];
            instanceIds.Add(instanceId);
        }

        // Assert - 确保所有ID都是唯一的
        instanceIds.Should().HaveCount(100);
    }
}

/// <summary>
/// H2TestController集成测试 - 需要真实的H2testw.exe环境
/// </summary>
public class H2TestControllerIntegrationTests
{
    /// <summary>
    /// 测试完整的H2testw工作流程（需要真实环境）
    /// </summary>
    [Fact(Skip = "需要真实的H2testw.exe文件和GUI环境")]
    public async Task ExecuteH2TestWorkflow_WithRealEnvironment_ShouldCompleteSuccessfully()
    {
        // 这个测试需要：
        // 1. 真实的H2testw.exe文件
        // 2. GUI环境（不能在无头环境中运行）
        // 3. 可写的测试驱动器

        // Arrange
        var config = new TestToolConfig
        {
            Name = "H2testw",
            ExecutablePath = @".\third part tools\h2testw.exe",
            WindowTitle = "H2testw",
            Timeouts = new TimeoutSettings
            {
                LaunchTimeout = TimeSpan.FromSeconds(30),
                TestTimeout = TimeSpan.FromMinutes(5), // 短时间测试
                ElementTimeout = TimeSpan.FromSeconds(10)
            }
        };

        var testConfig = new TestConfiguration
        {
            TargetDrive = "J:\\", // 基于图2的示例
            Parameters = new Dictionary<string, object>
            {
                ["TestAllSpace"] = false, // 只测试少量数据
                ["TestSize"] = 100 // 100MB
            }
        };

        // 在真实实现中，这里会：
        // 1. 启动H2testw.exe
        // 2. 选择English语言
        // 3. 点击"Select target"按钮
        // 4. 在文件夹选择对话框中输入"J:"
        // 5. 点击"Write + Verify"按钮
        // 6. 处理可能的确认对话框
        // 7. 等待测试完成
        // 8. 解析结果

        // Act & Assert
        // var controller = new H2TestController(config, automationHelper, logger);
        // var result = await controller.ExecuteTestAsync(testConfig, CancellationToken.None);
        // result.Should().NotBeNull();
        // result.Success.Should().BeTrue();

        await Task.CompletedTask; // 占位符
    }

    /// <summary>
    /// 测试H2testw启动失败的情况
    /// </summary>
    [Fact]
    public void StartH2Test_WithInvalidPath_ShouldReturnFalse()
    {
        // Arrange
        var invalidPath = @"C:\NonExistent\h2testw.exe";

        // Act
        var fileExists = File.Exists(invalidPath);

        // Assert
        fileExists.Should().BeFalse();
    }

    /// <summary>
    /// 测试目标驱动器不可访问的情况
    /// </summary>
    [Fact]
    public void ValidateTargetDrive_WithInaccessibleDrive_ShouldReturnFalse()
    {
        // Arrange
        var inaccessibleDrive = "Z:\\"; // 通常不存在的驱动器

        // Act
        var driveExists = Directory.Exists(inaccessibleDrive);

        // Assert
        driveExists.Should().BeFalse();
    }
}

/// <summary>
/// H2TestController错误处理测试
/// </summary>
public class H2TestControllerErrorHandlingTests
{
    /// <summary>
    /// 测试解析失败的H2testw结果
    /// </summary>
    [Fact]
    public void ParseH2TestResult_WithCorruptedOutput_ShouldHandleGracefully()
    {
        // Arrange - 损坏或不完整的输出
        var corruptedOutput = @"
H2testw | Prog
Writing
50 MBy
Incomplete output...
";

        // Act
        var writeSpeedMatch = System.Text.RegularExpressions.Regex.Match(corruptedOutput, @"Writing speed: ([\d.]+) MByte/s");
        var readSpeedMatch = System.Text.RegularExpressions.Regex.Match(corruptedOutput, @"Reading speed: ([\d.]+) MByte/s");

        // Assert - 应该优雅地处理解析失败
        writeSpeedMatch.Success.Should().BeFalse();
        readSpeedMatch.Success.Should().BeFalse();
    }

    /// <summary>
    /// 测试超时情况的处理
    /// </summary>
    [Fact]
    public void HandleTestTimeout_ShouldCancelGracefully()
    {
        // Arrange
        var timeout = TimeSpan.FromSeconds(1);
        var cts = new CancellationTokenSource(timeout);

        // Act & Assert
        cts.Token.IsCancellationRequested.Should().BeFalse();

        // 等待超时
        Thread.Sleep(1100);
        cts.Token.IsCancellationRequested.Should().BeTrue();
    }

    /// <summary>
    /// 测试磁盘空间不足的情况
    /// </summary>
    [Theory]
    [InlineData(50, 100, false)]  // 需要100MB，只有50MB
    [InlineData(200, 100, true)]  // 需要100MB，有200MB
    [InlineData(100, 100, true)]  // 刚好够用
    public void ValidateDiskSpace_ShouldReturnCorrectResult(long availableSpace, long requiredSpace, bool expectedResult)
    {
        // Act
        var hasEnoughSpace = availableSpace >= requiredSpace;

        // Assert
        hasEnoughSpace.Should().Be(expectedResult);
    }
}

/// <summary>
/// H2TestController配置测试 - 专注于配置验证
/// </summary>
public class H2TestControllerConfigurationTests
{
    /// <summary>
    /// 测试CrystalDiskMark配置创建
    /// </summary>
    [Fact]
    public void CreateCrystalDiskMarkConfig_ShouldHaveCorrectProperties()
    {
        // Arrange & Act
        var config = TestDataBuilder.CreateCrystalDiskMarkConfig();

        // Assert
        config.Name.Should().Be("CrystalDiskMark");
        config.Type.Should().Be(Core.Enums.TestToolType.GUI);
        config.DefaultParameters.Should().ContainKey("TestSize");
        config.DefaultParameters.Should().ContainKey("TestCount");
        config.UIConfig.Should().ContainKey("MainWindowClass");
        config.OutputParsing.ParsingRules.Should().ContainKey("SEQ1M_Read");
        config.MinimumDiskSpace.Should().Be(1024);
    }

    /// <summary>
    /// 测试日志事件参数创建
    /// </summary>
    [Fact]
    public void CreateLogEventArgs_ShouldHaveCorrectProperties()
    {
        // Arrange & Act
        var args = TestDataBuilder.CreateLogEventArgs(
            Microsoft.Extensions.Logging.LogLevel.Warning,
            "测试警告消息",
            "H2testw");

        // Assert
        args.Level.Should().Be(Microsoft.Extensions.Logging.LogLevel.Warning);
        args.Message.Should().Be("测试警告消息");
        args.Source.Should().Be("H2testw");
        args.Timestamp.Should().BeCloseTo(DateTime.Now, TimeSpan.FromSeconds(5));
    }

    /// <summary>
    /// 测试状态变更事件参数创建
    /// </summary>
    [Fact]
    public void CreateStatusChangedEventArgs_ShouldHaveCorrectProperties()
    {
        // Arrange & Act
        var args = TestDataBuilder.CreateStatusChangedEventArgs(
            Core.Enums.TestStatus.Completed,
            "H2testw");

        // Assert
        args.NewStatus.Should().Be(Core.Enums.TestStatus.Completed);
        args.ToolName.Should().Be("H2testw");
        args.Timestamp.Should().BeCloseTo(DateTime.Now, TimeSpan.FromSeconds(5));
    }
}
