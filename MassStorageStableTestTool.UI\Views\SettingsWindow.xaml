<Window x:Class="MassStorageStableTestTool.UI.Views.SettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="测试工具设置" 
        Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">
    
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    
    <Grid Background="{StaticResource BackgroundColor}">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 主内容区域 -->
        <Grid Grid.Row="0" Margin="16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- 左侧导航 -->
            <Border Grid.Column="0" Style="{StaticResource CardStyle}" Margin="0,0,8,0">
                <StackPanel>
                    <TextBlock Text="设置分类" Style="{StaticResource CardTitleStyle}"/>
                    
                    <TreeView Background="Transparent" BorderThickness="0">
                        <TreeViewItem Header="🔧 常规设置" IsExpanded="True"/>
                        <TreeViewItem Header="🖥️ GUI工具" IsExpanded="True">
                            <TreeViewItem Header="H2testw"/>
                            <TreeViewItem Header="CrystalDiskMark"/>
                            <TreeViewItem Header="ATTO"/>
                        </TreeViewItem>
                        <TreeViewItem Header="💻 CLI工具" IsExpanded="False">
                            <TreeViewItem Header="fio"/>
                            <TreeViewItem Header="diskspd"/>
                            <TreeViewItem Header="dd"/>
                        </TreeViewItem>
                        <TreeViewItem Header="🔄 混合工具" IsExpanded="False">
                            <TreeViewItem Header="HD Tune Pro"/>
                            <TreeViewItem Header="IOMeter"/>
                        </TreeViewItem>
                        <TreeViewItem Header="📊 报告设置"/>
                        <TreeViewItem Header="📝 日志设置"/>
                    </TreeView>
                </StackPanel>
            </Border>
            
            <!-- 右侧设置内容 -->
            <Border Grid.Column="1" Style="{StaticResource CardStyle}">
                <ScrollViewer Style="{StaticResource ModernScrollViewerStyle}">
                    <StackPanel>
                        <TextBlock Text="H2testw 设置" Style="{StaticResource CardTitleStyle}"/>
                        
                        <!-- 文件路径设置 -->
                        <TextBlock Text="📁 可执行文件路径:" Style="{StaticResource LabelStyle}"/>
                        <Grid Margin="0,0,0,12">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBox Grid.Column="0" 
                                   Text="./third_party_tools/h2testw.exe"
                                   Height="32"
                                   VerticalContentAlignment="Center"
                                   Margin="0,0,8,0"/>
                            <Button Grid.Column="1" 
                                  Content="📁"
                                  Style="{StaticResource SecondaryButtonStyle}"
                                  Width="32" Height="32"/>
                        </Grid>
                        
                        <!-- 超时设置 -->
                        <TextBlock Text="⏱️ 超时设置:" Style="{StaticResource LabelStyle}"/>
                        <Grid Margin="0,0,0,12">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                <TextBlock Text="启动超时 (秒):" FontSize="11" Margin="0,0,0,4"/>
                                <TextBox Text="30" Height="28"/>
                            </StackPanel>
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="测试超时 (秒):" FontSize="11" Margin="0,0,0,4"/>
                                <TextBox Text="7200" Height="28"/>
                            </StackPanel>
                        </Grid>
                        
                        <!-- 默认参数 -->
                        <TextBlock Text="🔧 默认参数:" Style="{StaticResource LabelStyle}"/>
                        <StackPanel Margin="0,0,0,12">
                            <CheckBox Content="测试全部可用空间" IsChecked="True" Style="{StaticResource ModernCheckBoxStyle}"/>
                            <CheckBox Content="验证写入数据" IsChecked="True" Style="{StaticResource ModernCheckBoxStyle}"/>
                            <CheckBox Content="快速测试模式" IsChecked="False" Style="{StaticResource ModernCheckBoxStyle}"/>
                        </StackPanel>
                        
                        <!-- 窗口识别 -->
                        <TextBlock Text="🪟 窗口识别:" Style="{StaticResource LabelStyle}"/>
                        <Grid Margin="0,0,0,12">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                <TextBlock Text="窗口标题:" FontSize="11" Margin="0,0,0,4"/>
                                <TextBox Text="H2testw" Height="28"/>
                            </StackPanel>
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="等待窗口 (秒):" FontSize="11" Margin="0,0,0,4"/>
                                <TextBox Text="30" Height="28"/>
                            </StackPanel>
                        </Grid>
                        
                        <!-- 操作按钮 -->
                        <StackPanel Orientation="Horizontal" Margin="0,16,0,0">
                            <Button Content="🧪 测试连接" 
                                  Style="{StaticResource SecondaryButtonStyle}"
                                  Margin="0,0,8,0"/>
                            <Button Content="🔄 重置为默认值" 
                                  Style="{StaticResource SecondaryButtonStyle}"/>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </Border>
        </Grid>
        
        <!-- 底部按钮 -->
        <Border Grid.Row="1" 
                Background="{StaticResource SurfaceColor}" 
                BorderBrush="{StaticResource BorderColor}" 
                BorderThickness="0,1,0,0"
                Padding="16,12">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="确定" 
                      Style="{StaticResource PrimaryButtonStyle}"
                      Width="80"
                      Margin="0,0,8,0"
                      Click="OkButton_Click"/>
                <Button Content="取消" 
                      Style="{StaticResource SecondaryButtonStyle}"
                      Width="80"
                      Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
