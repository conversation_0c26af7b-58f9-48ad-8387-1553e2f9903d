# CrystalDiskMark Controller Implementation

## 概述

成功为 MassStorageStableTestTool 系统添加了 CrystalDiskMark 控制器，实现了对 CrystalDiskMark 磁盘性能测试工具的自动化控制。

## 实现的功能

### 1. CrystalDiskMarkController 类

**位置**: `MassStorageStableTestTool.Automation/Controllers/CrystalDiskMarkController.cs`

**主要功能**:
- 继承自 `BaseTestToolController`
- 实现 GUI 自动化控制 CrystalDiskMark 应用程序
- 支持配置测试参数（测试大小、测试次数）
- 自动选择目标驱动器
- 监控测试进度
- 解析性能测试结果

### 2. 核心方法

#### 测试执行流程
1. **StartCrystalDiskMarkAsync()** - 启动 CrystalDiskMark 程序
2. **ConfigureTestParametersAsync()** - 配置测试参数
3. **SelectTargetDriveAsync()** - 选择目标驱动器
4. **StartTestAsync()** - 开始性能测试
5. **MonitorTestProgressAsync()** - 监控测试进度
6. **ParseTestResultsAsync()** - 解析测试结果
7. **CleanupAsync()** - 清理资源

#### 参数配置
- **SetTestSizeAsync()** - 设置测试数据大小（1GiB, 4GiB 等）
- **SetTestCountAsync()** - 设置测试次数

#### 结果解析
- **ParsePerformanceMetrics()** - 解析性能指标
  - 顺序读取速度 (SEQ1M Read)
  - 顺序写入速度 (SEQ1M Write)
  - 随机读取速度 (RND4K Read)
  - 随机写入速度 (RND4K Write)
  - IOPS 值
- **ExtractResultsFromUIAsync()** - 从 UI 元素提取结果

### 3. 控制器注册

**位置**: `MassStorageStableTestTool.Automation/Services/ControllerFactory.cs`

在 `RegisterDefaultControllers()` 方法中添加了：
```csharp
RegisterController("CrystalDiskMark", typeof(CrystalDiskMarkController));
```

### 4. 配置支持

**位置**: `MassStorageStableTestTool.Core/Services/ConfigurationService.cs`

CrystalDiskMark 已在默认配置中定义：
```csharp
["CrystalDiskMark"] = new TestToolConfig
{
    Name = "CrystalDiskMark",
    Type = TestToolType.GUI,
    ExecutablePath = "./third part tools/CrystalDiskMark8.0.1/DiskMark64.exe",
    WindowTitle = "CrystalDiskMark",
    Description = "CrystalDiskMark是一个磁盘基准测试工具，可以测试存储设备的顺序和随机读写性能",
    Version = "8.0.1",
    DefaultParameters = new Dictionary<string, object>
    {
        ["TestSize"] = "1GiB",
        ["TestCount"] = 5,
        ["TestMode"] = "All",
        ["QueueDepth"] = 32,
        ["ThreadCount"] = 1
    },
    Timeouts = new TimeoutSettings
    {
        LaunchTimeout = TimeSpan.FromSeconds(30),
        TestTimeout = TimeSpan.FromMinutes(45),
        ShutdownTimeout = TimeSpan.FromSeconds(10),
        ElementTimeout = TimeSpan.FromSeconds(10)
    }
}
```

### 5. 单元测试

**位置**: `MassStorageStableTestTool.Tests/Controllers/CrystalDiskMarkControllerTests.cs`

实现了完整的单元测试套件：
- 构造函数测试
- 参数验证测试
- 配置验证测试
- 工具可用性检查测试
- 支持参数获取测试
- 基础功能测试

## 技术特点

### 1. GUI 自动化
- 使用 FlaUI 框架进行 Windows UI 自动化
- 通过 AutomationHelper 封装常用操作
- 支持元素查找、点击、文本输入等操作

### 2. 错误处理
- 完善的异常处理机制
- 详细的日志记录
- 超时控制和取消支持

### 3. 结果解析
- 使用正则表达式解析性能数据
- 支持多种性能指标提取
- 自动截图保存测试结果

### 4. 资源管理
- 自动进程管理
- 窗口资源清理
- 内存泄漏防护

## 支持的测试参数

| 参数名 | 类型 | 描述 | 默认值 |
|--------|------|------|--------|
| TestSize | string | 测试数据大小 | "1GiB" |
| TestCount | int | 测试次数 | 5 |
| TestMode | string | 测试模式 | "All" |
| QueueDepth | int | 队列深度 | 32 |
| ThreadCount | int | 线程数 | 1 |

## 解析的性能指标

| 指标名 | 描述 | 单位 |
|--------|------|------|
| SequentialRead_MBps | 顺序读取速度 | MB/s |
| SequentialWrite_MBps | 顺序写入速度 | MB/s |
| RandomRead_MBps | 随机读取速度 | MB/s |
| RandomWrite_MBps | 随机写入速度 | MB/s |
| IOPS_1, IOPS_2, ... | IOPS 值 | IOPS |

## 使用示例

```csharp
// 创建测试配置
var testConfig = new TestConfiguration
{
    TargetDrive = "D:\\",
    Parameters = new Dictionary<string, object>
    {
        ["TestSize"] = "1GiB",
        ["TestCount"] = 3
    }
};

// 通过工厂创建控制器
var controller = await controllerFactory.CreateControllerAsync("CrystalDiskMark");

// 执行测试
var result = await controller.ExecuteTestAsync(testConfig, CancellationToken.None);

// 获取性能数据
var seqRead = result.GetPerformanceData<double>("SequentialRead_MBps");
var seqWrite = result.GetPerformanceData<double>("SequentialWrite_MBps");
```

## 编译状态

✅ **编译成功** - 所有核心项目（Core、Automation、UI、Reports）编译通过
✅ **控制器注册** - 已在 ControllerFactory 中注册
✅ **配置完整** - 默认配置已包含 CrystalDiskMark 设置
✅ **测试覆盖** - 包含完整的单元测试

## 后续改进建议

1. **UI 元素识别优化** - 根据实际 CrystalDiskMark 版本调整元素查找策略
2. **结果解析增强** - 支持更多版本的输出格式
3. **错误恢复机制** - 增加测试失败时的自动重试
4. **性能监控** - 实时监控测试进度和系统资源使用
5. **集成测试** - 添加真实环境下的集成测试

## 总结

CrystalDiskMark 控制器已成功集成到 MassStorageStableTestTool 系统中，提供了完整的磁盘性能测试自动化功能。控制器遵循系统的设计模式，具有良好的可扩展性和维护性。
