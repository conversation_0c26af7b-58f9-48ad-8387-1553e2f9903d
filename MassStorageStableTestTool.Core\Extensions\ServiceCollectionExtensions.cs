using MassStorageStableTestTool.Core.Interfaces;
using MassStorageStableTestTool.Core.Services;
using Microsoft.Extensions.DependencyInjection;

namespace MassStorageStableTestTool.Core.Extensions;

/// <summary>
/// 服务集合扩展方法
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加核心服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddCoreServices(this IServiceCollection services)
    {
        // 注册磁盘格式化服务
        services.AddScoped<IDiskFormatService, DiskFormatService>();
        
        // 注册其他核心服务
        services.AddScoped<ConfigurationService>();
        services.AddScoped<ReportService>();
        services.AddScoped<SystemInfoService>();
        
        return services;
    }

    /// <summary>
    /// 添加磁盘格式化服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddDiskFormatService(this IServiceCollection services)
    {
        services.AddScoped<IDiskFormatService, DiskFormatService>();
        return services;
    }
}
