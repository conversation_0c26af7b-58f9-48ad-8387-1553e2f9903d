using MassStorageStableTestTool.Core.Interfaces;
using MassStorageStableTestTool.Reports.Interfaces;
using MassStorageStableTestTool.Reports.Models;
using MassStorageStableTestTool.Reports.Services;
using MassStorageStableTestTool.Reports.Generators;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace MassStorageStableTestTool.Reports.Extensions;

/// <summary>
/// 服务集合扩展方法
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加报告服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">报告配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddReportServices(this IServiceCollection services, ReportGenerationConfiguration? configuration = null)
    {
        // 注册报告配置
        services.TryAddSingleton(configuration ?? new ReportGenerationConfiguration());

        // 注册报告生成器
        services.TryAddTransient<TextReportGenerator>();
        services.TryAddTransient<HtmlReportGenerator>();
        services.TryAddTransient<CsvReportGenerator>();
        services.TryAddTransient<JsonReportGenerator>();

        // 注册增强的报告服务，替换Core中的默认实现
        services.Replace(ServiceDescriptor.Singleton<IReportService, EnhancedReportService>());

        return services;
    }

    /// <summary>
    /// 添加报告服务（使用配置委托）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configureOptions">配置委托</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddReportServices(this IServiceCollection services, Action<ReportGenerationConfiguration> configureOptions)
    {
        var configuration = new ReportGenerationConfiguration();
        configureOptions(configuration);
        
        return services.AddReportServices(configuration);
    }
}
