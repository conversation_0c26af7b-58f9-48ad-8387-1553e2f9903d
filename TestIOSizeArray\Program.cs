﻿using System;
using System.Collections.Generic;
using System.Xml.Linq;

// 测试 ATTO 配置中 iosize 数组的处理
class Program
{
    static void Main()
    {
        // 模拟配置参数
        var config = new Dictionary<string, object>
        {
            ["iosize1"] = 512,
            ["iosize2"] = 65536,
            ["iosize3"] = 131072
        };

        // 模拟 ATTO 控制器中的逻辑
        int ioSize = 1;
        var ioSizeList = new List<int>();
        while (true)
        {
            var key = $"iosize{ioSize}";
            if (!config.TryGetValue(key, out var value) || value is not int ioSizeValue || ioSizeValue == 0)
            {
                break;
            }
            ioSizeList.Add(ioSizeValue);
            ioSize++;
        }

        Console.WriteLine($"收集到的 IO 大小: {string.Join(", ", ioSizeList)}");

        // 模拟 XML 生成
        var benchmarkElements = new List<XElement>
        {
            new XElement("drive", "C:"),
            new XElement("description", $"ATTO Test - {DateTime.Now:yyyy-MM-dd HH:mm:ss}"),
            new XElement("verifydata", "false"),
            new XElement("directio", "true"),
            new XElement("ioruntime", 30),
            new XElement("queuedepth", 4),
            new XElement("pattern", 0)
        };

        // 添加iosize元素
        for (int i = 0; i < ioSizeList.Count; i++)
        {
            benchmarkElements.Add(new XElement($"iosize{i + 1}", ioSizeList[i]));
        }

        benchmarkElements.Add(new XElement("filesize", 268435456));
        benchmarkElements.Add(new XElement("rates"));

        var benchmarkXml = new XDocument(
            new XDeclaration("1.0", "utf-8", null),
            new XElement("benchmark", benchmarkElements)
        );

        Console.WriteLine("生成的 XML:");
        Console.WriteLine(benchmarkXml.ToString());
    }
}
