using MassStorageStableTestTool.Core.Models;
using MassStorageStableTestTool.Core.Enums;
using MassStorageStableTestTool.Reports.Common;
using MassStorageStableTestTool.Reports.Models;
using Microsoft.Extensions.Logging;
using Scriban;
using System.Text;

namespace MassStorageStableTestTool.Reports.Generators;

/// <summary>
/// 文本格式报告生成器
/// </summary>
public class TextReportGenerator : BaseReportGenerator
{
    /// <summary>
    /// 支持的报告格式
    /// </summary>
    public override ReportFormat SupportedFormat => ReportFormat.Text;

    /// <summary>
    /// 生成器名称
    /// </summary>
    public override string GeneratorName => "Text Report Generator";

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="configuration">报告配置</param>
    public TextReportGenerator(ILogger<TextReportGenerator> logger, ReportGenerationConfiguration? configuration = null)
        : base(logger, configuration)
    {
    }

    /// <summary>
    /// 生成报告内容
    /// </summary>
    /// <param name="reportData">报告数据</param>
    /// <param name="template">模板内容</param>
    /// <returns>报告内容</returns>
    protected override async Task<string> GenerateReportContentAsync(object reportData, string template)
    {
        try
        {
            var scriptTemplate = Template.Parse(template);

            string result = await scriptTemplate.RenderAsync(reportData);
            // 检查是否有错误
            if (scriptTemplate.HasErrors)
            {
                _logger.LogWarning("模板渲染存在错误: {Errors}",
                    string.Join(", ", scriptTemplate.Messages.Select(m => m.Message)));
            }
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "使用模板生成文本报告时出错: {Message}", ex.Message);
            // 如果模板解析失败，使用简单的文本生成
            return GenerateSimpleTextReport(reportData);
        }
    }

    /// <summary>
    /// 获取默认文件扩展名
    /// </summary>
    /// <returns>文件扩展名</returns>
    public override string GetDefaultFileExtension() => ".txt";

    /// <summary>
    /// 获取后备模板
    /// </summary>
    /// <returns>后备模板内容</returns>
    protected override string GetFallbackTemplate()
    {
        return @"
================================================================================
                           {{ Configuration.Title }}
================================================================================

报告生成时间: {{ GeneratedAt | date: 'yyyy-MM-dd HH:mm:ss' }}
生成器版本: {{ Version }}
报告作者: {{ Configuration.Author }}
{{ if Configuration.Organization }}组织: {{ Configuration.Organization }}{{ end }}

================================================================================
                                测试概览
================================================================================

测试开始时间: {{ TestSuite.StartTime | date: 'yyyy-MM-dd HH:mm:ss' }}
测试结束时间: {{ TestSuite.EndTime | date: 'yyyy-MM-dd HH:mm:ss' }}
测试总耗时: {{ TestSuite.Duration.TotalMinutes | round: 2 }} 分钟

目标驱动器: {{ TestSuite.Configuration.TargetDrive }}
测试工具数量: {{ TestSuite.Configuration.SelectedTools.Count }}
选中的工具: {{ TestSuite.Configuration.SelectedTools | join: ', ' }}

================================================================================
                                测试结果摘要
================================================================================

总测试数: {{ TestSuite.TotalTestsCount }}
成功测试数: {{ TestSuite.SuccessfulTestsCount }}
失败测试数: {{ TestSuite.FailedTestsCount }}
成功率: {{ TestSuite.SuccessRate | round: 1 }}%

整体状态: {{ TestSuite.Status }}
{{ if TestSuite.AllTestsPassed }}✅ 所有测试均通过{{ else }}❌ 存在测试失败{{ end }}

================================================================================
                                详细测试结果
================================================================================

{{ for test_result in TestSuite.TestResults }}
工具名称: {{ test_result.ToolName }}
测试状态: {{ test_result.Status }}
测试结果: {{ if test_result.Success }}✅ 成功{{ else }}❌ 失败{{ end }}
开始时间: {{ test_result.StartTime | date: 'yyyy-MM-dd HH:mm:ss' }}
结束时间: {{ test_result.EndTime | date: 'yyyy-MM-dd HH:mm:ss' }}
耗时: {{ test_result.Duration.TotalMinutes | round: 2 }} 分钟

{{ if test_result.Performance }}
性能数据:
{{ if test_result.Performance.ReadSpeed }}  读取速度: {{ test_result.Performance.ReadSpeed | round: 2 }} MB/s{{ end }}
{{ if test_result.Performance.WriteSpeed }}  写入速度: {{ test_result.Performance.WriteSpeed | round: 2 }} MB/s{{ end }}
{{ if test_result.Performance.ReadIOPS }}  读取IOPS: {{ test_result.Performance.ReadIOPS | round: 0 }}{{ end }}
{{ if test_result.Performance.WriteIOPS }}  写入IOPS: {{ test_result.Performance.WriteIOPS | round: 0 }}{{ end }}
{{ if test_result.Performance.ReadLatency }}  读取延迟: {{ test_result.Performance.ReadLatency | round: 2 }} ms{{ end }}
{{ if test_result.Performance.WriteLatency }}  写入延迟: {{ test_result.Performance.WriteLatency | round: 2 }} ms{{ end }}
{{ end }}

{{ if test_result.ErrorMessage }}
错误信息: {{ test_result.ErrorMessage }}
{{ end }}

{{ if test_result.Warnings.Count > 0 }}
警告信息:
{{ for warning in test_result.Warnings }}  - {{ warning }}
{{ end }}
{{ end }}

{{ if test_result.OutputFiles.Count > 0 }}
输出文件:
{{ for file in test_result.OutputFiles }}  - {{ file }}
{{ end }}
{{ end }}

--------------------------------------------------------------------------------
{{ end }}

{{ if Configuration.IncludeSystemInfo and TestSuite.SystemInfo }}
================================================================================
                                系统信息
================================================================================

操作系统: {{ TestSuite.SystemInfo.OperatingSystem }}
处理器: {{ TestSuite.SystemInfo.Processor }}
系统架构: {{ TestSuite.SystemInfo.Architecture }}
机器名称: {{ TestSuite.SystemInfo.ComputerName }}
用户名称: {{ TestSuite.SystemInfo.UserName }}
总内存: {{ TestSuite.SystemInfo.TotalMemory | round: 2 }} GB
可用内存: {{ TestSuite.SystemInfo.AvailableMemory | round: 2 }} GB
.NET版本: {{ TestSuite.SystemInfo.DotNetVersion }}
{{ end }}

{{ if TestSuite.DriveInfo }}
================================================================================
                                驱动器信息
================================================================================

驱动器: {{ TestSuite.DriveInfo.Name }}
标签: {{ TestSuite.DriveInfo.Label }}
文件系统: {{ TestSuite.DriveInfo.FileSystem }}
驱动器类型: {{ TestSuite.DriveInfo.DriveType }}
总容量: {{ TestSuite.DriveInfo.TotalSize / (1024.0 * 1024.0 * 1024.0) | round: 2 }} GB
可用空间: {{ TestSuite.DriveInfo.AvailableFreeSpace / (1024.0 * 1024.0 * 1024.0) | round: 2 }} GB
已用空间: {{ (TestSuite.DriveInfo.TotalSize - TestSuite.DriveInfo.AvailableFreeSpace) / (1024.0 * 1024.0 * 1024.0) | round: 2 }} GB
使用率: {{ (TestSuite.DriveInfo.TotalSize - TestSuite.DriveInfo.AvailableFreeSpace) / TestSuite.DriveInfo.TotalSize * 100 | round: 1 }}%
驱动器状态: {{ if TestSuite.DriveInfo.IsReady }}就绪{{ else }}未就绪{{ end }}
{{ end }}

{{ if Configuration.IncludeDetailedLogs and TestSuite.Logs.Count > 0 }}
================================================================================
                                详细日志
================================================================================

{{ for log in TestSuite.Logs }}{{ log }}
{{ end }}
{{ end }}

{{ if TestSuite.Warnings.Count > 0 }}
================================================================================
                                套件警告
================================================================================

{{ for warning in TestSuite.Warnings }}- {{ warning }}
{{ end }}
{{ end }}

================================================================================
                                报告结束
================================================================================

报告生成完成时间: {{ GeneratedAt | date: 'yyyy-MM-dd HH:mm:ss' }}
生成器: {{ GeneratedBy }}
版本: {{ Version }}
";
    }

    /// <summary>
    /// 生成简单的文本报告（当模板解析失败时使用）
    /// </summary>
    /// <param name="reportData">报告数据</param>
    /// <returns>简单文本报告</returns>
    private string GenerateSimpleTextReport(object reportData)
    {
        var sb = new StringBuilder();

        var Configuration = reportData.GetType().GetProperty("Configuration")?.GetValue(reportData) as ReportGenerationConfiguration;
        var TestSuite = reportData.GetType().GetProperty("TestSuite")?.GetValue(reportData) as TestSuiteResult;
        var GeneratedAtObj = reportData.GetType().GetProperty("GeneratedAt")?.GetValue(reportData);
        var GeneratedAt = GeneratedAtObj is DateTime dt ? dt : DateTime.Now;
        var GeneratedBy = (reportData.GetType().GetProperty("GeneratedBy")?.GetValue(reportData) ?? "Unknown") as string;
        var Version = (reportData.GetType().GetProperty("Version")?.GetValue(reportData) ?? "1.0.0") as string;
        if (Configuration == null || TestSuite == null || GeneratedBy == null || Version == null)
        {
            return string.Empty;
        }
        var reportDataModel = new ReportDataModel(Configuration, TestSuite, GeneratedBy, Version, GeneratedAt);
        
        // 这里需要从reportData中提取TestSuiteResult
        // 由于reportData是匿名对象，我们需要使用反射或者重新设计数据结构
        sb.AppendLine("================================================================================");
        sb.AppendLine($"                           {reportDataModel.Configuration.Title}");
        sb.AppendLine("================================================================================");
        sb.AppendLine();
        sb.AppendLine($"报告生成时间: {reportDataModel.GeneratedAt:yyyy-MM-dd HH:mm:ss}");
        sb.AppendLine($"生成器版本: {reportDataModel.Version}");
        sb.AppendLine($"报告作者: {reportDataModel.Configuration.Author}");
        if (!string.IsNullOrEmpty(reportDataModel.Configuration.Organization))
        {
            sb.AppendLine($"组织: {reportDataModel.Configuration.Organization}");
        }
        sb.AppendLine("================================================================================");
        sb.AppendLine("                                测试概览");
        sb.AppendLine("================================================================================");
        sb.AppendLine($"测试开始时间: {reportDataModel.TestSuite.StartTime:yyyy-MM-dd HH:mm:ss}");
        sb.AppendLine($"测试结束时间: {reportDataModel.TestSuite.EndTime:yyyy-MM-dd HH:mm:ss}");
        sb.AppendLine($"测试总耗时: {reportDataModel.TestSuite.Duration.TotalMinutes:F2} 分钟");
        sb.AppendLine();
        sb.AppendLine($"目标驱动器: {reportDataModel.TestSuite.Configuration.TargetDrive}");
        sb.AppendLine($"测试工具数量: {reportDataModel.TestSuite.Configuration.SelectedTools.Count}");
        sb.AppendLine($"选中的工具: {string.Join(", ", reportDataModel.TestSuite.Configuration.SelectedTools)}");
        sb.AppendLine();
        sb.AppendLine("================================================================================");
        sb.AppendLine("                                测试结果摘要");
        sb.AppendLine("================================================================================");
        sb.AppendLine($"总测试数: {reportDataModel.TestSuite.TotalTestsCount}");
        sb.AppendLine($"成功测试数: {reportDataModel.TestSuite.SuccessfulTestsCount}");
        sb.AppendLine($"失败测试数: {reportDataModel.TestSuite.FailedTestsCount}");
        sb.AppendLine($"成功率: {reportDataModel.TestSuite.SuccessRate:F1}%");
        sb.AppendLine();
        sb.AppendLine($"整体状态: {reportDataModel.TestSuite.Status}");
        sb.AppendLine($"所有测试是否通过: {(reportDataModel.TestSuite.AllTestsPassed ? "✅所有测试均通过" : "❌存在测试失败")}");
        sb.AppendLine();
        sb.AppendLine("================================================================================");
        sb.AppendLine("                                详细测试结果");
        sb.AppendLine("================================================================================");
        foreach (var testResult in reportDataModel.TestSuite.TestResults)
        {
            sb.AppendLine($"工具名称: {testResult.ToolName}");
            sb.AppendLine($"测试状态: {testResult.Status}");
            sb.AppendLine($"测试结果: {(testResult.Success ? "✅成功" : "❌失败")}");
            sb.AppendLine($"开始时间: {testResult.StartTime:yyyy-MM-dd HH:mm:ss}");
            sb.AppendLine($"结束时间: {testResult.EndTime:yyyy-MM-dd HH:mm:ss}");
            sb.AppendLine($"耗时: {testResult.Duration.TotalMinutes:F2} 分钟");
            sb.AppendLine();
            if (testResult.Data != null)
            {
                sb.AppendLine("性能数据:");
                // if (testResult.ToolName == "H2testw")
                // {
                //     if (testResult.GetPerformanceData<double>("WriteSpeed") != default)
                //         sb.AppendLine($"  写入速度: {testResult.GetPerformanceData<double>("WriteSpeed"):F2} MB/s");

                //     if (testResult.GetPerformanceData<double>("ReadSpeed") != default)
                //         sb.AppendLine($"  读取速度: {testResult.GetPerformanceData<double>("ReadSpeed"):F2} MB/s");

                //     if (testResult.GetPerformanceData<int>("ErrorCount") > 0)
                //         sb.AppendLine($"  错误数量: {testResult.GetPerformanceData<int>("ErrorCount")}");
                // }
                // else if (testResult.ToolName == "CrystalDiskMark")
                // {
                    var PerformanceData = testResult.Data;
                    if (PerformanceData != null)
                    {
                        foreach (var data in PerformanceData)
                        {
                            sb.AppendLine($"  {data.Key}: {data.Value}");
                        }
                    }
                // }
            }
            sb.AppendLine();
            if (!string.IsNullOrEmpty(testResult.ErrorMessage))
            {
                sb.AppendLine($"错误信息: {testResult.ErrorMessage}");
                sb.AppendLine();
            }

            if (testResult.Warnings.Any())
            {
                sb.AppendLine("警告信息:");
                foreach (var warning in testResult.Warnings)
                {
                    sb.AppendLine($"  - {warning}");
                }
                sb.AppendLine();
            }

            if (testResult.OutputFiles.Any())
            {
                sb.AppendLine("输出文件:");
                foreach (var file in testResult.OutputFiles)
                {
                    sb.AppendLine($"  - {file}");
                }
                sb.AppendLine();
            }
            sb.AppendLine("--------------------------------------------------------------------------------");
        }

        if (reportDataModel.TestSuite.SystemInfo != null && reportDataModel.Configuration.IncludeSystemInfo)
        {
            sb.AppendLine();
            sb.AppendLine("================================================================================");
            sb.AppendLine("                                系统信息");
            sb.AppendLine("================================================================================");
            sb.AppendLine($"操作系统: {reportDataModel.TestSuite.SystemInfo.OperatingSystem}");
            sb.AppendLine($"处理器: {reportDataModel.TestSuite.SystemInfo.Processor}");
            sb.AppendLine($"系统架构: {reportDataModel.TestSuite.SystemInfo.Architecture}");
            sb.AppendLine($"机器名称: {reportDataModel.TestSuite.SystemInfo.ComputerName}");
            sb.AppendLine($"用户名称: {reportDataModel.TestSuite.SystemInfo.UserName}");
            sb.AppendLine($"总内存: {reportDataModel.TestSuite.SystemInfo.TotalMemory:F2} GB");
            sb.AppendLine($"可用内存: {reportDataModel.TestSuite.SystemInfo.AvailableMemory:F2} GB");
            sb.AppendLine($".NET版本: {reportDataModel.TestSuite.SystemInfo.DotNetVersion}");
        }

        if (reportDataModel.TestSuite.DriveInfo != null)
        {
            sb.AppendLine();
            sb.AppendLine("================================================================================");
            sb.AppendLine("                                驱动器信息");
            sb.AppendLine("================================================================================");
            sb.AppendLine($"驱动器: {reportDataModel.TestSuite.DriveInfo.Name}");
            sb.AppendLine($"标签: {reportDataModel.TestSuite.DriveInfo.Label}");
            sb.AppendLine($"文件系统: {reportDataModel.TestSuite.DriveInfo.FileSystem}");
            sb.AppendLine($"驱动器类型: {reportDataModel.TestSuite.DriveInfo.DriveType}");
            sb.AppendLine($"总容量: {reportDataModel.TestSuite.DriveInfo.TotalSize / (1024.0 * 1024.0 * 1024.0):F2} GB");
            sb.AppendLine($"可用空间: {reportDataModel.TestSuite.DriveInfo.AvailableFreeSpace / (1024.0 * 1024.0 * 1024.0):F2} GB");
            sb.AppendLine($"已用空间: {(reportDataModel.TestSuite.DriveInfo.TotalSize - reportDataModel.TestSuite.DriveInfo.AvailableFreeSpace) / (1024.0 * 1024.0 * 1024.0):F2} GB");
            sb.AppendLine($"使用率: {(reportDataModel.TestSuite.DriveInfo.TotalSize - reportDataModel.TestSuite.DriveInfo.AvailableFreeSpace) / reportDataModel.TestSuite.DriveInfo.TotalSize * 100:F1}%");
            sb.AppendLine($"驱动器状态: {(reportDataModel.TestSuite.DriveInfo.IsReady ? "就绪" : "未就绪")}");
        }

        if (reportDataModel.TestSuite.Logs.Any() && reportDataModel.Configuration.IncludeDetailedLogs)
        {
            sb.AppendLine();
            sb.AppendLine("================================================================================");
            sb.AppendLine("                                详细日志");
            sb.AppendLine("================================================================================");
            foreach (var log in reportDataModel.TestSuite.Logs)
            {
                sb.AppendLine(log);
            }
        }

        if (reportDataModel.TestSuite.Warnings.Any())
        {
            sb.AppendLine();
            sb.AppendLine("================================================================================");
            sb.AppendLine("                                套件警告");
            sb.AppendLine("================================================================================");
            foreach (var warning in reportDataModel.TestSuite.Warnings)
            {
                sb.AppendLine($"- {warning}");
            }
        }

        sb.AppendLine();
        sb.AppendLine("================================================================================");
        sb.AppendLine("                                报告结束");
        sb.AppendLine("================================================================================");
        sb.AppendLine($"报告生成完成时间: {reportDataModel.GeneratedAt:yyyy-MM-dd HH:mm:ss}");
        sb.AppendLine($"生成器: {reportDataModel.GeneratedBy}");
        sb.AppendLine($"版本: {reportDataModel.Version}");
        
        return sb.ToString();
    }
}
