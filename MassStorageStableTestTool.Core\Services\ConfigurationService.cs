using MassStorageStableTestTool.Core.Interfaces;
using MassStorageStableTestTool.Core.Models;
using MassStorageStableTestTool.Core.Enums;
using MassStorageStableTestTool.Core.Exceptions;
using System.Text.Json;

namespace MassStorageStableTestTool.Core.Services;

/// <summary>
/// 配置服务实现
/// </summary>
public class ConfigurationService : IConfigurationService
{
    private readonly string _defaultConfigPath;
    private ApplicationConfiguration? _currentConfiguration;

    /// <summary>
    /// 配置变化事件
    /// </summary>
    public event EventHandler<ConfigurationChangedEventArgs>? ConfigurationChanged;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="configPath">配置文件路径</param>
    public ConfigurationService(string? configPath = null)
    {
        _defaultConfigPath = configPath ?? Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config", "appsettings.json");
    }

    /// <summary>
    /// 加载配置
    /// </summary>
    /// <param name="configPath">配置文件路径</param>
    /// <returns>配置对象</returns>
    public async Task<ApplicationConfiguration> LoadConfigurationAsync(string? configPath = null)
    {
        var filePath = configPath ?? _defaultConfigPath;

        try
        {
            if (!File.Exists(filePath))
            {
                // 如果配置文件不存在，创建默认配置
                var defaultConfig = CreateDefaultConfiguration();
                await SaveConfigurationAsync(defaultConfig, filePath).ConfigureAwait(false);
                _currentConfiguration = defaultConfig;
                return defaultConfig;
            }

            var jsonContent = await File.ReadAllTextAsync(filePath).ConfigureAwait(false);
            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                WriteIndented = true
            };

            var configuration = JsonSerializer.Deserialize<ApplicationConfiguration>(jsonContent, options);
            if (configuration == null)
            {
                throw new ConfigurationException("配置文件格式无效");
            }

            // 验证配置
            var (isValid, errors) = ValidateConfiguration(configuration);
            if (!isValid)
            {
                throw new ConfigurationException($"配置验证失败: {string.Join(", ", errors)}");
            }

            configuration.LastModified = DateTime.Now;
            _currentConfiguration = configuration;

            return configuration;
        }
        catch (JsonException ex)
        {
            throw new ConfigurationException($"配置文件JSON格式错误: {ex.Message}", ex);
        }
        catch (Exception ex) when (!(ex is ConfigurationException))
        {
            throw new ConfigurationException($"加载配置文件失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 保存配置
    /// </summary>
    /// <param name="configuration">配置对象</param>
    /// <param name="configPath">配置文件路径</param>
    /// <returns>保存结果</returns>
    public async Task<bool> SaveConfigurationAsync(ApplicationConfiguration configuration, string? configPath = null)
    {
        if (configuration == null)
        {
            throw new ArgumentNullException(nameof(configuration));
        }

        var filePath = configPath ?? _defaultConfigPath;

        try
        {
            // 验证配置
            var (isValid, errors) = ValidateConfiguration(configuration);
            if (!isValid)
            {
                throw new ConfigurationException($"配置验证失败: {string.Join(", ", errors)}");
            }

            // 确保目录存在
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // 更新修改时间
            configuration.LastModified = DateTime.Now;

            // 序列化配置
            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };

            var jsonContent = JsonSerializer.Serialize(configuration, options);
            await File.WriteAllTextAsync(filePath, jsonContent).ConfigureAwait(false);

            _currentConfiguration = configuration;

            // 触发配置变化事件
            OnConfigurationChanged(ConfigurationChangeType.Modified, "Configuration", null, configuration);

            return true;
        }
        catch (Exception ex)
        {
            throw new ConfigurationException($"保存配置文件失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 创建默认配置
    /// </summary>
    /// <returns>默认配置</returns>
    public ApplicationConfiguration CreateDefaultConfiguration()
    {
        return new ApplicationConfiguration
        {
            Version = "1.0",
            Description = "SD卡自动化稳定性测试工具默认配置",
            TestTools = CreateDefaultTestToolConfigurations(),
            Reporting = new ReportConfiguration
            {
                OutputDirectory = "./Reports",
                FileNameTemplate = "{DriveLabel}_{DateTime:yyyy-MM-dd_HH-mm-ss}_Report",
                IncludeSystemInfo = true,
                Formats = new List<string> { "txt", "csv", "json" },
                AutoOpenReport = true
            },
            Logging = new LoggingConfiguration
            {
                LogLevel = "Information",
                LogDirectory = "./Logs",
                EnableConsoleOutput = true,
                EnableFileOutput = true,
                MaxFileSizeMB = 10,
                RetainedFileCount = 7
            },
            Performance = new PerformanceConfiguration
            {
                MaxConcurrentTests = 1,
                ProcessCleanupTimeout = 30,
                MemoryThreshold = 80,
                EnablePerformanceMonitoring = true,
                MonitoringInterval = 5
            },
            UI = new UIConfiguration
            {
                Theme = "Light",
                Language = "zh-CN",
                WindowSize = new WindowSize { Width = 1200, Height = 800 },
                AutoSaveWindowState = true,
                RefreshInterval = 1000
            },
            Security = new SecurityConfiguration
            {
                AllowedExecutableDirectories = new List<string>
                {
                    "./third_party_tools",
                    Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles),
                    Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86)
                },
                VerifyDigitalSignature = false,
                RestrictDriveAccess = true,
                AllowedDriveTypes = new List<string> { "Removable" }
            }
        };
    }

    /// <summary>
    /// 创建默认测试工具配置
    /// </summary>
    /// <returns>测试工具配置字典</returns>
    private Dictionary<string, TestToolConfig> CreateDefaultTestToolConfigurations()
    {
        return new Dictionary<string, TestToolConfig>
        {
            ["H2testw"] = new TestToolConfig
            {
                Name = "H2testw",
                Type = TestToolType.GUI,
                ExecutablePath = @".\third part tools\h2testw.exe",
                WindowTitle = "H2testw",
                Description = "H2testw测试工具",
                Version = "1.4",
                DefaultParameters = new Dictionary<string, object>
                {
                    ["language"] = "english",
                    ["TestAllSpace"] = 1000,
                    ["WriteVerify"] = true,
                    ["WriteSpeed"] = "Auto",
                    ["ReadSpeed"] = "Auto",
                    ["FormatBeforeTest"] = true,
                    ["QuickFormat"] = true
                },
                Timeouts = new TimeoutSettings
                {
                    LaunchTimeout = TimeSpan.FromSeconds(30),
                    TestTimeout = TimeSpan.FromHours(1),
                    ShutdownTimeout = TimeSpan.FromSeconds(15),
                    ElementTimeout = TimeSpan.FromSeconds(10)
                },
                UIConfig = new Dictionary<string, object>
                {
                    ["MainWindowName"] = "H2testw",
                    ["SelectTargetAutoId"] = "1002",
                    ["WriteVerifyButtonAutoId"] = "1006",
                    ["englishAutomationId"] = "1023",
                    ["resultEditorAutoId"] = "1011",
                    ["SelectFolderWindowName"] = "浏览文件夹",
                    ["FolderEditBoxAutoId"] = "14148",
                    ["ConfirmButtonAutoId"] = "1",
                    ["AllSpaceRadioButtonAutoId"] = "1000",
                    ["SpecificSizeRadioButtonAutoId"] = "1001",
                    ["SizeTextBoxAutoId"] = "1004",
                    ["ProcessBarAutoId"] = "1010",
                    ["testWindowName"] = "H2testw | Progress"
                },
                OutputParsing = new OutputParsingConfig
                {
                    Format = "text",
                    ParsingRules = new Dictionary<string, string>
                    {
                        ["WriteSpeed"] = @"Writing speed: ([\d.]+) MByte/s",
                        ["ReadSpeed"] = @"Reading speed: ([\d.]+) MByte/s",
                        ["TestResult"] = @"Test (finished|failed)",
                        ["ErrorCount"] = @"(\d+) errors"
                    }
                },
                Enabled = true,
                Priority = 1,
                SupportedFileSystems = new List<string> { "FAT32", "NTFS", "exFAT" },
                MinimumDiskSpace = 100
            },
            ["CrystalDiskMark"] = new TestToolConfig
            {
                Name = "CrystalDiskMark",
                Type = TestToolType.GUI,
                ExecutablePath = "./third part tools/CrystalDiskMark8.0.1/DiskMark64.exe",
                WindowTitle = "CrystalDiskMark",
                Description = "CrystalDiskMark是一个磁盘基准测试工具，可以测试存储设备的顺序和随机读写性能",
                Version = "8.0.1",
                DefaultParameters = new Dictionary<string, object>
                {
                    ["TestSize"] = "1GiB", //1GB
                    ["TestCount"] = "5", //5
                    ["TestMode"] = "All",
                    ["TestUnit"] = "MB/s" //MB/s
                },
                Timeouts = new TimeoutSettings
                {
                    LaunchTimeout = TimeSpan.FromSeconds(30),
                    TestTimeout = TimeSpan.FromMinutes(45),
                    ShutdownTimeout = TimeSpan.FromSeconds(10),
                    ElementTimeout = TimeSpan.FromSeconds(2)
                },
                UIConfig = new Dictionary<string, object>
                {
                    ["MainWindowName"] = "CrystalDiskMark 8.0.1", //包含这个名字，实际名称还有后缀
                    ["DriveComboBoxAutoId"] = "1027",
                    ["AllButtonAutoId"] = "1003",
                    ["TestSizeComboBoxAutoId"] = "1028",
                    ["TestCountComboBoxAutoId"] = "1026",
                    ["TestUnitComboBoxAutoId"] = "1025",
                    //result
                    ["SEQ1MQ8T1_ReadAutoId"] = "1009",
                    ["SEQ1MQ8T1_WriteAutoId"] = "1014",
                    ["SEQ1MQ1T1_ReadAutoId"] = "1010",
                    ["SEQ1MQ1T1_WriteAutoId"] = "1015",
                    ["RND4KQ32T1_ReadAutoId"] = "1011",
                    ["RND4KQ32T1_WriteAutoId"] = "1016",
                    ["RND4KQ1T1_ReadAutoId"] = "1012",
                    ["RND4KQ1T1_WriteAutoId"] = "1017"
                },
                OutputParsing = new OutputParsingConfig
                {
                    Format = "text",
                    ParsingRules = new Dictionary<string, string>
                    {
                        ["SEQ1MQ8T1_Read"] = @"SEQ1M Q8T1.*?(\d+\.\d+)",
                        ["SEQ1MQ8T1_Write"] = @"SEQ1M Q8T1.*?(\d+\.\d+).*?(\d+\.\d+)",
                        ["SEQ1MQ1T1_Read"] = @"SEQ1M Q1T1.*?(\d+\.\d+)",
                        ["SEQ1MQ1T1_Write"] = @"SEQ1M Q1T1.*?(\d+\.\d+).*?(\d+\.\d+)",
                        ["RND4KQ32T1_Read"] = @"RND4K Q32T1.*?(\d+\.\d+)",
                        ["RND4KQ32T1_Write"] = @"RND4K Q32T1.*?(\d+\.\d+).*?(\d+\.\d+)",
                        ["RND4KQ1T1_Read"] = @"RND4K Q1T1.*?(\d+\.\d+)",
                        ["RND4KQ1T1_Write"] = @"RND4K Q1T1.*?(\d+\.\d+).*?(\d+\.\d+)"
                    }
                },
                Enabled = true,
                Priority = 2,
                SupportedFileSystems = new List<string> { "NTFS", "FAT32", "exFAT" },
                MinimumDiskSpace = 1024
            },
            ["HD Bench340"] = new TestToolConfig
            {
                Name = "HD Bench340",
                Type = TestToolType.GUI,
                ExecutablePath = "./third part tools/HD Bench340/HDBENCH.EXE",
                WindowTitle = "HD Bench",
                Description = "HD Bench是一个经典的硬盘基准测试工具，可以测试存储设备的各项性能指标",
                Version = "3.40",
                DefaultParameters = new Dictionary<string, object>
                {
                    ["TestType"] = "All",
                    ["FileSize"] = "10MB",
                    ["BufferSize"] = "64KB"
                },
                Timeouts = new TimeoutSettings
                {
                    LaunchTimeout = TimeSpan.FromSeconds(30),
                    TestTimeout = TimeSpan.FromMinutes(20),
                    ShutdownTimeout = TimeSpan.FromSeconds(10),
                    ElementTimeout = TimeSpan.FromSeconds(10)
                },
                UIConfig = new Dictionary<string, object>
                {
                    ["MainWindowClass"] = "THDBenchForm",
                    ["DriveComboBoxName"] = "ComboBox1",
                    ["StartButtonName"] = "Button1",
                    ["AllTestCheckBoxName"] = "CheckBox1"
                },
                OutputParsing = new OutputParsingConfig
                {
                    Format = "text",
                    ParsingRules = new Dictionary<string, string>
                    {
                        ["ReadSpeed"] = @"Read.*?(\d+) KB/s",
                        ["WriteSpeed"] = @"Write.*?(\d+) KB/s",
                        ["RandomRead"] = @"RandomRead.*?(\d+) KB/s",
                        ["RandomWrite"] = @"RandomWrite.*?(\d+) KB/s"
                    }
                },
                Enabled = true,
                Priority = 3,
                SupportedFileSystems = new List<string> { "FAT32", "NTFS" },
                MinimumDiskSpace = 50
            },
            // ["ATTO"] = new TestToolConfig
            // {
            //     Name = "ATTO Disk Benchmark",
            //     Type = TestToolType.GUI,
            //     ExecutablePath = "./third part tools/ATTO DiskBenchmark(v3.05)/ATTODiskBenchmark.exe",
            //     WindowTitle = "ATTO Disk Benchmark",
            //     Description = "ATTO Disk Benchmark是一个专业的磁盘基准测试工具，可以测试不同块大小下的读写性能",
            //     Version = "3.05",
            //     DefaultParameters = new Dictionary<string, object>
            //     {
            //         ["FileSize"] = "256MB",
            //         ["BlockSizeMin"] = "512B",
            //         ["BlockSizeMax"] = "8MB",
            //         ["QueueDepth"] = 4,
            //         ["TestType"] = "Both"
            //     },
            //     Timeouts = new TimeoutSettings
            //     {
            //         LaunchTimeout = TimeSpan.FromSeconds(30),
            //         TestTimeout = TimeSpan.FromMinutes(25),
            //         ShutdownTimeout = TimeSpan.FromSeconds(10),
            //         ElementTimeout = TimeSpan.FromSeconds(10)
            //     },
            //     UIConfig = new Dictionary<string, object>
            //     {
            //         ["MainWindowClass"] = "TMainForm",
            //         ["DriveComboBoxName"] = "ComboBox1",
            //         ["StartButtonName"] = "Button1",
            //         ["FileSizeComboBoxName"] = "ComboBox2",
            //         ["QueueDepthComboBoxName"] = "ComboBox3"
            //     },
            //     OutputParsing = new OutputParsingConfig
            //     {
            //         Format = "text",
            //         ParsingRules = new Dictionary<string, string>
            //         {
            //             ["MaxReadSpeed"] = @"Maximum Read Speed.*?(\d+\.\d+) MB/s",
            //             ["MaxWriteSpeed"] = @"Maximum Write Speed.*?(\d+\.\d+) MB/s",
            //             ["AvgReadSpeed"] = @"Average Read Speed.*?(\d+\.\d+) MB/s",
            //             ["AvgWriteSpeed"] = @"Average Write Speed.*?(\d+\.\d+) MB/s"
            //         }
            //     },
            //     Enabled = true,
            //     Priority = 4,
            //     SupportedFileSystems = new List<string> { "NTFS", "FAT32", "exFAT" },
            //     MinimumDiskSpace = 512
            // },
            ["HDTunePro"] = new TestToolConfig
            {
                Name = "HD Tune Pro",
                Type = TestToolType.GUI,
                ExecutablePath = "./third part tools/HDtunepro5.75/硬盘检测工具(HD Tune Pro)5.75汉化专业版.exe",
                WindowTitle = "HD Tune Pro",
                Description = "HD Tune Pro是一个功能全面的硬盘检测和基准测试工具，可以进行健康检查、错误扫描和性能测试",
                Version = "5.75",
                DefaultParameters = new Dictionary<string, object>
                {
                    ["TestType"] = "Benchmark",
                    ["FileSize"] = "100MB",
                    ["AccessTime"] = true,
                    ["BurstRate"] = true,
                    ["CPUUsage"] = true
                },
                Timeouts = new TimeoutSettings
                {
                    LaunchTimeout = TimeSpan.FromSeconds(30),
                    TestTimeout = TimeSpan.FromMinutes(30),
                    ShutdownTimeout = TimeSpan.FromSeconds(10),
                    ElementTimeout = TimeSpan.FromSeconds(10)
                },
                UIConfig = new Dictionary<string, object>
                {
                    ["MainWindowClass"] = "TMainForm",
                    ["DriveComboBoxName"] = "ComboBox1",
                    ["BenchmarkTabName"] = "TabSheet1",
                    ["StartButtonName"] = "Button1",
                    ["ProgressBarName"] = "ProgressBar1"
                },
                OutputParsing = new OutputParsingConfig
                {
                    Format = "text",
                    ParsingRules = new Dictionary<string, string>
                    {
                        ["MinTransferRate"] = @"Minimum.*?(\d+\.\d+) MB/s",
                        ["MaxTransferRate"] = @"Maximum.*?(\d+\.\d+) MB/s",
                        ["AvgTransferRate"] = @"Average.*?(\d+\.\d+) MB/s",
                        ["AccessTime"] = @"Access time.*?(\d+\.\d+) ms",
                        ["BurstRate"] = @"Burst rate.*?(\d+\.\d+) MB/s",
                        ["CPUUsage"] = @"CPU usage.*?(\d+)%"
                    }
                },
                Enabled = true,
                Priority = 5,
                SupportedFileSystems = new List<string> { "NTFS", "FAT32", "exFAT" },
                MinimumDiskSpace = 200
            },
            ["ATTO"] = new TestToolConfig
            {
                Name = "ATTO",
                Type = TestToolType.CLI,
                ExecutablePath = @".\third part tools\ATTO DiskBenchmark(v3.05)\ATTODiskBenchmark.exe",
                Description = "ATTO Disk Benchmark - 磁盘性能基准测试工具",
                Version = "5.00.2",
                CommandTemplate = "-f \"{BenchmarkFile}\" -x",
                Timeouts = new TimeoutSettings
                {
                    LaunchTimeout = TimeSpan.FromSeconds(10),
                    TestTimeout = TimeSpan.FromMinutes(30),
                    CleanupTimeout = TimeSpan.FromSeconds(10)
                },
                DefaultParameters = new Dictionary<string, object>
                {
                    ["IORuntime"] = 30,
                    ["QueueDepth"] = 4,
                    ["FileSize"] = 268435456, // 256MB
                    ["VerifyData"] = false,
                    ["DirectIO"] = true,
                    ["BypassWriteCache"] = false,
                    ["ContinuousIO"] = false,
                    ["Pattern"] = 0,
                    ["iosize1"] = 512,
                    ["iosize2"] = 65536
                },
                OutputParsing = new OutputParsingConfig
                {
                    Format = "xml",
                    ParsingRules = new Dictionary<string, string>
                    {
                        ["ReadBPS"] = @"<readbps>(\d+)</readbps>",
                        ["WriteBPS"] = @"<writebps>(\d+)</writebps>",
                        ["ReadIOPS"] = @"<readiops>(\d+)</readiops>",
                        ["WriteIOPS"] = @"<writeiops>(\d+)</writeiops>",
                        ["IOSize"] = @"<iosize>(\d+)</iosize>"
                    }
                },
                Enabled = true,
                Priority = 3,
                SupportedFileSystems = new List<string> { "NTFS", "FAT32", "exFAT" },
                MinimumDiskSpace = 256
            }
        };
    }

    /// <summary>
    /// 验证配置
    /// </summary>
    /// <param name="configuration">配置对象</param>
    /// <returns>验证结果</returns>
    public (bool IsValid, List<string> Errors) ValidateConfiguration(ApplicationConfiguration configuration)
    {
        if (configuration == null)
        {
            return (false, new List<string> { "配置对象不能为空" });
        }

        return configuration.Validate();
    }

    /// <summary>
    /// 获取测试工具配置
    /// </summary>
    /// <param name="toolName">工具名称</param>
    /// <returns>工具配置</returns>
    public async Task<TestToolConfig?> GetToolConfigurationAsync(string toolName)
    {
        if (_currentConfiguration == null)
        {
            _currentConfiguration = await LoadConfigurationAsync().ConfigureAwait(false);
        }

        return _currentConfiguration.TestTools.TryGetValue(toolName, out var config) ? config : null;
    }

    /// <summary>
    /// 更新测试工具配置
    /// </summary>
    /// <param name="toolName">工具名称</param>
    /// <param name="config">工具配置</param>
    /// <returns>更新结果</returns>
    public async Task<bool> UpdateToolConfigurationAsync(string toolName, TestToolConfig config)
    {
        if (_currentConfiguration == null)
        {
            _currentConfiguration = await LoadConfigurationAsync();
        }

        var oldConfig = _currentConfiguration.TestTools.TryGetValue(toolName, out var existing) ? existing : null;
        _currentConfiguration.TestTools[toolName] = config;

        var saved = await SaveConfigurationAsync(_currentConfiguration).ConfigureAwait(false);
        if (saved)
        {
            OnConfigurationChanged(ConfigurationChangeType.Modified, $"TestTools.{toolName}", oldConfig, config);
        }

        return saved;
    }

    /// <summary>
    /// 获取所有测试工具配置
    /// </summary>
    /// <returns>工具配置字典</returns>
    public async Task<Dictionary<string, TestToolConfig>> GetAllToolConfigurationsAsync()
    {
        if (_currentConfiguration == null)
        {
            _currentConfiguration = await LoadConfigurationAsync();
        }

        return new Dictionary<string, TestToolConfig>(_currentConfiguration.TestTools);
    }

    /// <summary>
    /// 导入配置
    /// </summary>
    /// <param name="filePath">配置文件路径</param>
    /// <param name="format">配置格式</param>
    /// <returns>导入的配置</returns>
    public async Task<ApplicationConfiguration> ImportConfigurationAsync(string filePath, string format = "json")
    {
        if (!File.Exists(filePath))
        {
            throw new FileNotFoundException($"配置文件不存在: {filePath}");
        }

        if (format.ToLowerInvariant() != "json")
        {
            throw new NotSupportedException($"不支持的配置格式: {format}");
        }

        return await LoadConfigurationAsync(filePath);
    }

    /// <summary>
    /// 导出配置
    /// </summary>
    /// <param name="configuration">配置对象</param>
    /// <param name="filePath">导出文件路径</param>
    /// <param name="format">导出格式</param>
    /// <returns>导出结果</returns>
    public async Task<bool> ExportConfigurationAsync(ApplicationConfiguration configuration, string filePath, string format = "json")
    {
        if (format.ToLowerInvariant() != "json")
        {
            throw new NotSupportedException($"不支持的导出格式: {format}");
        }

        return await SaveConfigurationAsync(configuration, filePath).ConfigureAwait(false);
    }

    /// <summary>
    /// 重置配置为默认值
    /// </summary>
    /// <returns>重置结果</returns>
    public async Task<bool> ResetToDefaultAsync()
    {
        var defaultConfig = CreateDefaultConfiguration();
        var result = await SaveConfigurationAsync(defaultConfig).ConfigureAwait(false);

        if (result)
        {
            OnConfigurationChanged(ConfigurationChangeType.Reset, "Configuration", _currentConfiguration, defaultConfig);
        }

        return result;
    }

    /// <summary>
    /// 触发配置变化事件
    /// </summary>
    /// <param name="changeType">变化类型</param>
    /// <param name="configKey">配置键</param>
    /// <param name="oldValue">旧值</param>
    /// <param name="newValue">新值</param>
    protected virtual void OnConfigurationChanged(ConfigurationChangeType changeType, string configKey, object? oldValue, object? newValue)
    {
        ConfigurationChanged?.Invoke(this, new ConfigurationChangedEventArgs
        {
            ChangeType = changeType,
            ConfigKey = configKey,
            OldValue = oldValue,
            NewValue = newValue,
            Timestamp = DateTime.Now
        });
    }
}
