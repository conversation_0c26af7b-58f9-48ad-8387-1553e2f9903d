namespace MassStorageStableTestTool.Core.Exceptions;

/// <summary>
/// 配置异常
/// </summary>
public class ConfigurationException : Exception
{
    /// <summary>
    /// 配置键名
    /// </summary>
    public string? ConfigKey { get; }

    /// <summary>
    /// 配置文件路径
    /// </summary>
    public string? ConfigFilePath { get; }

    /// <summary>
    /// 使用错误消息初始化配置异常。
    /// </summary>
    /// <param name="message">错误消息</param>
    public ConfigurationException(string message) 
        : base($"配置错误: {message}")
    {
    }

    /// <summary>
    /// 使用配置项和错误消息初始化配置异常。
    /// </summary>
    /// <param name="configKey">配置项键名</param>
    /// <param name="message">错误消息</param>
    public ConfigurationException(string configKey, string message) 
        : base($"配置项 '{configKey}' 错误: {message}")
    {
        ConfigKey = configKey;
    }

    /// <summary>
    /// 使用配置项、文件路径和错误消息初始化配置异常。
    /// </summary>
    /// <param name="configKey">配置项键名</param>
    /// <param name="configFilePath">配置文件路径</param>
    /// <param name="message">错误消息</param>
    public ConfigurationException(string configKey, string configFilePath, string message) 
        : base($"配置文件 '{configFilePath}' 中的配置项 '{configKey}' 错误: {message}")
    {
        ConfigKey = configKey;
        ConfigFilePath = configFilePath;
    }

    /// <summary>
    /// 使用错误消息和内部异常初始化配置异常。
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="innerException">内部异常</param>
    public ConfigurationException(string message, Exception innerException) 
        : base($"配置错误: {message}", innerException)
    {
    }

    /// <summary>
    /// 使用配置项、错误消息和内部异常初始化配置异常。
    /// </summary>
    /// <param name="configKey">配置项键名</param>
    /// <param name="message">错误消息</param>
    /// <param name="innerException">内部异常</param>
    public ConfigurationException(string configKey, string message, Exception innerException) 
        : base($"配置项 '{configKey}' 错误: {message}", innerException)
    {
        ConfigKey = configKey;
    }
}
