﻿using System.Diagnostics;
using System.IO;
using System.Windows;
using MassStorageStableTestTool.UI.ViewModels;

namespace MassStorageStableTestTool.UI;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    public MainWindow(MainViewModel viewModel)
    {
        InitializeComponent();
        DataContext = viewModel;
    }

    public MainWindow()
    {
        InitializeComponent();
        // 设计时构造函数 - 在运行时会通过依赖注入提供正确的ViewModel
    }

    private void SwitchToChinese(object sender, RoutedEventArgs e)
    {
        App.SetLanguage("zh-CN");
        Restart();
    }

    private void SwitchToEnglish(object sender, RoutedEventArgs e)
    {
        App.SetLanguage("en-US");
        Restart();
    }

    private static void Restart()
    {
        var exePath = Process.GetCurrentProcess().MainModule?.FileName;
        if (!string.IsNullOrEmpty(exePath))
        {
            var startInfo = new ProcessStartInfo(exePath)
            {
                UseShellExecute = true,
                WorkingDirectory = Path.GetDirectoryName(exePath)
            };
            Process.Start(startInfo);
        }
        Application.Current.Shutdown();
    }
}

