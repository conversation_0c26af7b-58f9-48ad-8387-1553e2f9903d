using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using MassStorageStableTestTool.Core.Services;
using MassStorageStableTestTool.Core.Interfaces;
using MassStorageStableTestTool.Core.Models;

namespace MassStorageStableTestTool.Tests.Services;

/// <summary>
/// 磁盘格式化服务测试
/// </summary>
public class DiskFormatServiceTests
{
    private readonly Mock<ILogger<DiskFormatService>> _mockLogger;
    private readonly DiskFormatService _formatService;

    public DiskFormatServiceTests()
    {
        _mockLogger = new Mock<ILogger<DiskFormatService>>();
        _formatService = new DiskFormatService(_mockLogger.Object);
    }

    [Fact]
    public void Constructor_WithNullLogger_ThrowsArgumentNullException()
    {
        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => new DiskFormatService(null!));
    }

    [Fact]
    public void GetSupportedFileSystems_ReturnsExpectedFileSystems()
    {
        // Act
        var supportedFileSystems = _formatService.GetSupportedFileSystems();

        // Assert
        Assert.Contains("FAT32", supportedFileSystems);
        Assert.Contains("NTFS", supportedFileSystems);
        Assert.Contains("exFAT", supportedFileSystems);
        Assert.Equal(3, supportedFileSystems.Count);
    }

    [Theory]
    [InlineData("FAT32", true)]
    [InlineData("NTFS", true)]
    [InlineData("exFAT", true)]
    [InlineData("fat32", true)] // 大小写不敏感
    [InlineData("ntfs", true)]
    [InlineData("exfat", true)]
    [InlineData("EXT4", false)]
    [InlineData("HFS+", false)]
    [InlineData("", false)]
    [InlineData(null, false)]
    public void IsFileSystemSupported_WithVariousInputs_ReturnsExpectedResult(string? fileSystem, bool expected)
    {
        // Act
        var result = _formatService.IsFileSystemSupported(fileSystem!);

        // Assert
        Assert.Equal(expected, result);
    }

    [Theory]
    [InlineData("", false)]
    [InlineData(null, false)]
    [InlineData("   ", false)]
    [InlineData("InvalidDrive", false)]
    [InlineData("C", false)]
    [InlineData("C:", true)]
    [InlineData("E:", true)]
    [InlineData("Z:", true)]
    [InlineData("c:", true)] // 大小写不敏感
    public async Task CheckDriveFormatabilityAsync_WithInvalidDriveLetters_ReturnsExpectedResult(string driveLetter, bool shouldHaveValidFormat)
    {
        // Act
        var (canFormat, issues) = await _formatService.CheckDriveFormatabilityAsync(driveLetter);

        // Assert
        if (!shouldHaveValidFormat)
        {
            Assert.False(canFormat);
            Assert.NotEmpty(issues);
        }
        // 注意：对于有效格式的驱动器，我们不能确定它们是否真的存在或可格式化
        // 这需要在集成测试中验证
    }

    [Fact]
    public async Task FormatDriveAsync_WithInvalidDriveLetter_ReturnsFailureResult()
    {
        // Arrange
        var invalidDrive = "InvalidDrive";

        // Act
        var result = await _formatService.FormatDriveAsync(invalidDrive);

        // Assert
        Assert.False(result.Success);
        Assert.Contains("无效的驱动器盘符", result.ErrorMessage);
    }

    [Fact]
    public async Task FormatDriveAsync_WithUnsupportedFileSystem_ReturnsFailureResult()
    {
        // Arrange
        var validDrive = "E:";
        var unsupportedFileSystem = "EXT4";

        // Act
        var result = await _formatService.FormatDriveAsync(validDrive, unsupportedFileSystem);

        // Assert
        Assert.False(result.Success);
        Assert.Contains("不支持的文件系统", result.ErrorMessage);
    }

    [Fact]
    public async Task FormatDriveAsync_WithNullFileSystemAndVolumeLabel_ShouldUseCurrentDriveInfo()
    {
        // Arrange
        var validDrive = "E:";

        // Act
        var result = await _formatService.FormatDriveAsync(
            validDrive,
            fileSystem: null, // 应该使用当前文件系统
            volumeLabel: null); // 应该使用当前卷标

        // Assert
        // 注意：这个测试在没有真实驱动器的情况下会失败
        // 但它验证了API接受null参数的能力
        Assert.False(result.Success); // 预期失败，因为没有真实驱动器
    }

    [Fact]
    public void FormatResult_CreateSuccess_ReturnsSuccessResult()
    {
        // Arrange
        var fileSystem = "FAT32";
        var volumeLabel = "TEST_DRIVE";
        var duration = TimeSpan.FromMinutes(2);

        // Act
        var result = FormatResult.CreateSuccess(fileSystem, volumeLabel, duration);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(fileSystem, result.FileSystem);
        Assert.Equal(volumeLabel, result.VolumeLabel);
        Assert.Equal(duration, result.Duration);
        Assert.Null(result.ErrorMessage);
        Assert.Null(result.Exception);
    }

    [Fact]
    public void FormatResult_CreateFailure_ReturnsFailureResult()
    {
        // Arrange
        var errorMessage = "Test error message";
        var exception = new InvalidOperationException("Test exception");

        // Act
        var result = FormatResult.CreateFailure(errorMessage, exception);

        // Assert
        Assert.False(result.Success);
        Assert.Equal(errorMessage, result.ErrorMessage);
        Assert.Equal(exception, result.Exception);
        Assert.Null(result.FileSystem);
        Assert.Null(result.VolumeLabel);
    }

    [Fact]
    public void FormatResult_AddLog_AddsLogWithTimestamp()
    {
        // Arrange
        var result = new FormatResult();
        var message = "Test log message";

        // Act
        result.AddLog(message);

        // Assert
        Assert.Single(result.Logs);
        Assert.Contains(message, result.Logs[0]);
        Assert.Contains(DateTime.Now.ToString("yyyy-MM-dd"), result.Logs[0]);
    }
}

/// <summary>
/// 磁盘格式化服务集成测试
/// 注意：这些测试需要真实的可移动驱动器，并且会格式化驱动器
/// </summary>
public class DiskFormatServiceIntegrationTests
{
    private readonly Mock<ILogger<DiskFormatService>> _mockLogger;
    private readonly DiskFormatService _formatService;

    public DiskFormatServiceIntegrationTests()
    {
        _mockLogger = new Mock<ILogger<DiskFormatService>>();
        _formatService = new DiskFormatService(_mockLogger.Object);
    }

    [Fact(Skip = "需要真实的可移动驱动器，会格式化驱动器")]
    public async Task FormatDriveAsync_WithRealRemovableDrive_ShouldFormatSuccessfully()
    {
        // 警告：此测试会格式化真实驱动器，请谨慎使用
        // 请将 "E:" 替换为实际的测试驱动器
        var testDrive = "E:";
        
        // Arrange
        var progress = new Progress<ProgressEventArgs>(args =>
        {
            Console.WriteLine($"格式化进度: {args.Progress}% - {args.Status}");
        });

        // Act
        var result = await _formatService.FormatDriveAsync(
            testDrive,
            fileSystem: null, // 使用当前文件系统
            volumeLabel: null, // 使用当前卷标
            quickFormat: true,
            CancellationToken.None,
            progress);

        // Assert
        Assert.True(result.Success, $"格式化失败: {result.ErrorMessage}");
        Assert.NotNull(result.FileSystem);
        Assert.NotNull(result.VolumeLabel);
        Assert.True(result.Duration > TimeSpan.Zero);
    }

    [Fact(Skip = "需要真实的驱动器环境")]
    public async Task CheckDriveFormatabilityAsync_WithSystemDrive_ShouldReturnFalse()
    {
        // Arrange
        var systemDrive = "C:"; // 系统驱动器

        // Act
        var (canFormat, issues) = await _formatService.CheckDriveFormatabilityAsync(systemDrive);

        // Assert
        Assert.False(canFormat);
        Assert.Contains(issues, issue => issue.Contains("系统驱动器"));
    }

    [Fact(Skip = "需要真实的可移动驱动器环境")]
    public async Task CheckDriveFormatabilityAsync_WithRemovableDrive_ShouldReturnTrue()
    {
        // 请将 "E:" 替换为实际的可移动驱动器
        var removableDrive = "E:";

        // Act
        var (canFormat, issues) = await _formatService.CheckDriveFormatabilityAsync(removableDrive);

        // Assert
        Assert.True(canFormat, $"驱动器检查失败: {string.Join(", ", issues)}");
        Assert.Empty(issues);
    }
}
