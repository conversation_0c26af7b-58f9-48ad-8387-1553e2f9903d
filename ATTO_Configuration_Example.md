# ATTO 配置文件生成示例

## 概述

ATTO控制器现在已经更新为从 `TestToolConfig` 的 `DefaultParameters` 中读取配置设置来生成 `.bmk` 配置文件，而不是使用硬编码的值。

## 配置参数

### 支持的配置参数

| 参数名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `IORuntime` | int | 30 | IO运行时间（分钟） |
| `QueueDepth` | int | 4 | 队列深度 |
| `FileSize` | long | 268435456 | 测试文件大小（字节，256MB） |
| `VerifyData` | bool | false | 是否验证数据 |
| `DirectIO` | bool | true | 是否使用直接IO |
| `BypassWriteCache` | bool | false | 是否绕过写缓存 |
| `ContinuousIO` | bool | false | 是否连续IO |
| `Pattern` | int | 0 | 数据模式 |
| `IOSize1` | int | 512 | 第一个IO大小（字节） |
| `IOSize2` | int | 65536 | 第二个IO大小（字节） |

## 配置示例

### 在 ConfigurationService.cs 中的配置

```csharp
["ATTO"] = new TestToolConfig
{
    Name = "ATTO",
    Type = TestToolType.CLI,
    ExecutablePath = @".\third part tools\ATTO DiskBenchmark(v3.05)\ATTODiskBenchmark.exe",
    Description = "ATTO Disk Benchmark - 磁盘性能基准测试工具",
    Version = "5.00.2",
    CommandTemplate = "-f \"{BenchmarkFile}\" -x",
    Timeouts = new TimeoutSettings
    {
        LaunchTimeout = TimeSpan.FromSeconds(10),
        TestTimeout = TimeSpan.FromMinutes(30),
        CleanupTimeout = TimeSpan.FromSeconds(10)
    },
    DefaultParameters = new Dictionary<string, object>
    {
        ["IORuntime"] = 30,           // 30分钟测试时间
        ["QueueDepth"] = 4,           // 队列深度4
        ["FileSize"] = 268435456,     // 256MB文件大小
        ["VerifyData"] = false,       // 不验证数据
        ["DirectIO"] = true,          // 使用直接IO
        ["BypassWriteCache"] = false, // 不绕过写缓存
        ["ContinuousIO"] = false,     // 不使用连续IO
        ["Pattern"] = 0,              // 数据模式0
        ["IOSize1"] = 512,            // 第一个IO大小512字节
        ["IOSize2"] = 65536           // 第二个IO大小64KB
    },
    Enabled = true,
    Priority = 3,
    SupportedFileSystems = new List<string> { "NTFS", "FAT32", "exFAT" },
    MinimumDiskSpace = 256
}
```

### 生成的 .bmk 文件示例

基于上述配置，生成的XML文件将如下所示：

```xml
<?xml version="1.0" encoding="utf-8"?>
<benchmark>
  <drive>C:</drive>
  <description>ATTO Test - 2025-10-09 15:30:00</description>
  <verifydata>false</verifydata>
  <directio>true</directio>
  <bypasswritecache>false</bypasswritecache>
  <continuousio>false</continuousio>
  <ioruntime>30</ioruntime>
  <queuedepth>4</queuedepth>
  <pattern>0</pattern>
  <iosize1>512</iosize1>
  <iosize2>65536</iosize2>
  <filesize>268435456</filesize>
  <rates />
</benchmark>
```

## 配置优先级

配置参数的优先级顺序：

1. **TestConfiguration 参数** - 来自用户界面或测试配置的参数
2. **DefaultParameters 配置** - 在 TestToolConfig 中定义的默认参数
3. **硬编码默认值** - 代码中的最后备用值

### 示例：IORuntime 参数的确定

```csharp
// 优先使用测试配置中的超时时间（转换为分钟）
var ioRuntime = GetConfigValue<int>(defaultParams, "IORuntime", config.TimeoutSeconds / 60);
```

如果：
- `config.TimeoutSeconds = 600`（10分钟）
- `DefaultParameters["IORuntime"] = 30`

则最终使用 `DefaultParameters` 中的值 `30`，除非配置中没有设置该参数。

## 自定义配置

### 快速测试配置

```csharp
DefaultParameters = new Dictionary<string, object>
{
    ["IORuntime"] = 5,            // 5分钟快速测试
    ["QueueDepth"] = 2,           // 较低队列深度
    ["FileSize"] = 67108864,      // 64MB较小文件
    ["VerifyData"] = false,
    ["DirectIO"] = true,
    ["BypassWriteCache"] = false,
    ["ContinuousIO"] = false,
    ["Pattern"] = 0,
    ["IOSize1"] = 512,
    ["IOSize2"] = 32768           // 32KB
}
```

### 高性能测试配置

```csharp
DefaultParameters = new Dictionary<string, object>
{
    ["IORuntime"] = 60,           // 60分钟长时间测试
    ["QueueDepth"] = 8,           // 更高队列深度
    ["FileSize"] = 1073741824,    // 1GB大文件
    ["VerifyData"] = true,        // 验证数据完整性
    ["DirectIO"] = true,
    ["BypassWriteCache"] = true,  // 绕过写缓存
    ["ContinuousIO"] = true,      // 连续IO
    ["Pattern"] = 1,              // 不同数据模式
    ["IOSize1"] = 4096,           // 4KB
    ["IOSize2"] = 1048576         // 1MB
}
```

## 实现细节

### GetConfigValue 方法

```csharp
private static T GetConfigValue<T>(Dictionary<string, object> parameters, string key, T defaultValue)
{
    if (parameters.TryGetValue(key, out var value))
    {
        try
        {
            // 尝试直接转换
            if (value is T directValue)
            {
                return directValue;
            }

            // 尝试类型转换
            return (T)Convert.ChangeType(value, typeof(T));
        }
        catch (Exception)
        {
            // 转换失败，返回默认值
            return defaultValue;
        }
    }

    return defaultValue;
}
```

这个方法支持：
- 直接类型匹配
- 自动类型转换（如 int 到 long，string 到 bool 等）
- 转换失败时的安全回退

## 优势

1. **灵活性** - 可以通过配置文件轻松调整测试参数
2. **一致性** - 所有ATTO测试使用相同的配置源
3. **可维护性** - 配置集中管理，易于修改
4. **类型安全** - 支持强类型转换和验证
5. **向后兼容** - 如果配置缺失，使用合理的默认值

## 注意事项

- 确保 `FileSize` 参数与可用磁盘空间匹配
- `IORuntime` 应该根据测试需求合理设置
- `QueueDepth` 过高可能影响系统性能
- 某些参数组合可能不被ATTO工具支持
