namespace MassStorageStableTestTool.Core.Models;

/// <summary>
/// 驱动器信息模型
/// </summary>
public class DriveInfo
{
    /// <summary>
    /// 驱动器名称（如 "E:", "F:" 等）
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 驱动器标签
    /// </summary>
    public string Label { get; set; } = string.Empty;

    /// <summary>
    /// 驱动器类型
    /// </summary>
    public DriveType DriveType { get; set; }

    /// <summary>
    /// 文件系统类型
    /// </summary>
    public string FileSystem { get; set; } = string.Empty;

    /// <summary>
    /// 总容量（字节）
    /// </summary>
    public long TotalSize { get; set; }

    /// <summary>
    /// 可用空间（字节）
    /// </summary>
    public long AvailableFreeSpace { get; set; }

    /// <summary>
    /// 总可用空间（字节）
    /// </summary>
    public long TotalFreeSpace { get; set; }

    /// <summary>
    /// 已使用空间（字节）
    /// </summary>
    public long UsedSpace => TotalSize - TotalFreeSpace;

    /// <summary>
    /// 使用率百分比
    /// </summary>
    public double UsagePercentage => TotalSize > 0 ? (double)UsedSpace / TotalSize * 100 : 0;

    /// <summary>
    /// 是否就绪
    /// </summary>
    public bool IsReady { get; set; }

    /// <summary>
    /// 根目录
    /// </summary>
    public string RootDirectory { get; set; } = string.Empty;

    /// <summary>
    /// 驱动器格式
    /// </summary>
    public string DriveFormat { get; set; } = string.Empty;

    /// <summary>
    /// 序列号
    /// </summary>
    public string SerialNumber { get; set; } = string.Empty;

    /// <summary>
    /// 制造商
    /// </summary>
    public string Manufacturer { get; set; } = string.Empty;

    /// <summary>
    /// 型号
    /// </summary>
    public string Model { get; set; } = string.Empty;

    /// <summary>
    /// 接口类型（如 USB 3.0, SATA 等）
    /// </summary>
    public string InterfaceType { get; set; } = string.Empty;

    /// <summary>
    /// 是否可移动
    /// </summary>
    public bool IsRemovable => DriveType == System.IO.DriveType.Removable;

    /// <summary>
    /// 健康状态
    /// </summary>
    public DriveHealthStatus HealthStatus { get; set; } = DriveHealthStatus.Unknown;

    /// <summary>
    /// 温度（摄氏度）
    /// </summary>
    public double? Temperature { get; set; }

    /// <summary>
    /// 读取速度（MB/s）
    /// </summary>
    public double? ReadSpeed { get; set; }

    /// <summary>
    /// 写入速度（MB/s）
    /// </summary>
    public double? WriteSpeed { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 获取总容量（格式化字符串）
    /// </summary>
    /// <returns>格式化的容量字符串</returns>
    public string GetFormattedTotalSize()
    {
        return FormatBytes(TotalSize);
    }

    /// <summary>
    /// 获取可用空间（格式化字符串）
    /// </summary>
    /// <returns>格式化的可用空间字符串</returns>
    public string GetFormattedFreeSpace()
    {
        return FormatBytes(AvailableFreeSpace);
    }

    /// <summary>
    /// 获取已使用空间（格式化字符串）
    /// </summary>
    /// <returns>格式化的已使用空间字符串</returns>
    public string GetFormattedUsedSpace()
    {
        return FormatBytes(UsedSpace);
    }

    /// <summary>
    /// 格式化字节数
    /// </summary>
    /// <param name="bytes">字节数</param>
    /// <returns>格式化字符串</returns>
    private static string FormatBytes(long bytes)
    {
        string[] suffixes = { "B", "KB", "MB", "GB", "TB", "PB" };
        int counter = 0;
        decimal number = bytes;
        
        while (Math.Round(number / 1024) >= 1)
        {
            number /= 1024;
            counter++;
        }
        
        return $"{number:n1} {suffixes[counter]}";
    }

    /// <summary>
    /// 检查是否适合测试
    /// </summary>
    /// <param name="requiredSpace">所需空间（字节）</param>
    /// <returns>检查结果</returns>
    public (bool IsSuitable, List<string> Issues) CheckSuitabilityForTesting(long requiredSpace = 0)
    {
        var issues = new List<string>();

        if (!IsReady)
        {
            issues.Add("驱动器未就绪");
        }

        if (!IsRemovable)
        {
            issues.Add("驱动器不是可移动设备，可能不是SD卡或U盘");
        }

        if (requiredSpace > 0 && AvailableFreeSpace < requiredSpace)
        {
            issues.Add($"可用空间不足，需要 {FormatBytes(requiredSpace)}，但只有 {GetFormattedFreeSpace()}");
        }

        if (HealthStatus == DriveHealthStatus.Critical)
        {
            issues.Add("驱动器健康状态为严重，不建议进行测试");
        }

        if (Temperature.HasValue && Temperature.Value > 70)
        {
            issues.Add($"驱动器温度过高 ({Temperature.Value:F1}°C)，可能影响测试结果");
        }

        return (issues.Count == 0, issues);
    }

    /// <summary>
    /// 转换为字典格式
    /// </summary>
    /// <returns>驱动器信息字典</returns>
    public Dictionary<string, object> ToDictionary()
    {
        var dict = new Dictionary<string, object>
        {
            ["Name"] = Name,
            ["Label"] = Label,
            ["DriveType"] = DriveType.ToString(),
            ["FileSystem"] = FileSystem,
            ["TotalSize"] = TotalSize,
            ["AvailableFreeSpace"] = AvailableFreeSpace,
            ["UsedSpace"] = UsedSpace,
            ["UsagePercentage"] = UsagePercentage,
            ["IsReady"] = IsReady,
            ["IsRemovable"] = IsRemovable,
            ["HealthStatus"] = HealthStatus.ToString(),
            ["FormattedTotalSize"] = GetFormattedTotalSize(),
            ["FormattedFreeSpace"] = GetFormattedFreeSpace(),
            ["FormattedUsedSpace"] = GetFormattedUsedSpace()
        };

        if (!string.IsNullOrEmpty(SerialNumber))
            dict["SerialNumber"] = SerialNumber;
        
        if (!string.IsNullOrEmpty(Manufacturer))
            dict["Manufacturer"] = Manufacturer;
        
        if (!string.IsNullOrEmpty(Model))
            dict["Model"] = Model;
        
        if (!string.IsNullOrEmpty(InterfaceType))
            dict["InterfaceType"] = InterfaceType;
        
        if (Temperature.HasValue)
            dict["Temperature"] = Temperature.Value;
        
        if (ReadSpeed.HasValue)
            dict["ReadSpeed"] = ReadSpeed.Value;
        
        if (WriteSpeed.HasValue)
            dict["WriteSpeed"] = WriteSpeed.Value;

        return dict;
    }

    /// <summary>
    /// 获取驱动器摘要信息
    /// </summary>
    /// <returns>驱动器摘要</returns>
    public string GetSummary()
    {
        var summary = $"{Name} ({Label}) - {GetFormattedTotalSize()} {FileSystem}";
        
        if (!string.IsNullOrEmpty(Model))
        {
            summary += $" - {Model}";
        }
        
        return summary;
    }
}

/// <summary>
/// 驱动器健康状态枚举
/// </summary>
public enum DriveHealthStatus
{
    /// <summary>
    /// 未知
    /// </summary>
    Unknown,

    /// <summary>
    /// 健康
    /// </summary>
    Healthy,

    /// <summary>
    /// 警告
    /// </summary>
    Warning,

    /// <summary>
    /// 严重
    /// </summary>
    Critical
}
