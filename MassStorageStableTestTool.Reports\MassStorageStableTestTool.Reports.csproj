﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\MassStorageStableTestTool.Core\MassStorageStableTestTool.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
    <PackageReference Include="Scriban" Version="5.9.0" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Templates\**\*.liquid" />
    <EmbeddedResource Include="Templates\**\*.html" />
    <EmbeddedResource Include="Templates\**\*.txt" />
  </ItemGroup>

</Project>
