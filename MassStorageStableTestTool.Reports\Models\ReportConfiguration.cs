using MassStorageStableTestTool.Core.Enums;

namespace MassStorageStableTestTool.Reports.Models;

/// <summary>
/// 报告生成配置
/// </summary>
public class ReportGenerationConfiguration
{
    /// <summary>
    /// 报告标题
    /// </summary>
    public string Title { get; set; } = "磁盘稳定性测试报告";

    /// <summary>
    /// 报告描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 报告作者
    /// </summary>
    public string Author { get; set; } = Environment.UserName;

    /// <summary>
    /// 公司/组织名称
    /// </summary>
    public string Organization { get; set; } = string.Empty;

    /// <summary>
    /// 报告版本
    /// </summary>
    public string Version { get; set; } = "1.0";

    /// <summary>
    /// 是否包含详细日志
    /// </summary>
    public bool IncludeDetailedLogs { get; set; } = true;

    /// <summary>
    /// 是否包含性能图表
    /// </summary>
    public bool IncludePerformanceCharts { get; set; } = true;

    /// <summary>
    /// 是否包含系统信息
    /// </summary>
    public bool IncludeSystemInfo { get; set; } = true;

    /// <summary>
    /// 是否包含错误详情
    /// </summary>
    public bool IncludeErrorDetails { get; set; } = true;

    /// <summary>
    /// 是否包含警告信息
    /// </summary>
    public bool IncludeWarnings { get; set; } = true;

    /// <summary>
    /// 默认报告格式
    /// </summary>
    public ReportFormat DefaultFormat { get; set; } = ReportFormat.HTML;

    /// <summary>
    /// 输出目录
    /// </summary>
    public string OutputDirectory { get; set; } = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Reports");

    /// <summary>
    /// 文件名模板
    /// </summary>
    public string FileNameTemplate { get; set; } = "TestReport_{drive}_{timestamp}";

    /// <summary>
    /// 时间戳格式
    /// </summary>
    public string TimestampFormat { get; set; } = "yyyyMMdd_HHmmss";

    /// <summary>
    /// 自定义CSS样式（用于HTML报告）
    /// </summary>
    public string? CustomCss { get; set; }

    /// <summary>
    /// 自定义JavaScript（用于HTML报告）
    /// </summary>
    public string? CustomJavaScript { get; set; }

    /// <summary>
    /// 语言设置
    /// </summary>
    public string Language { get; set; } = "zh-CN";

    /// <summary>
    /// 时区设置
    /// </summary>
    public string TimeZone { get; set; } = TimeZoneInfo.Local.Id;

    /// <summary>
    /// 自定义字段
    /// </summary>
    public Dictionary<string, object> CustomFields { get; set; } = new();
}
