using MassStorageStableTestTool.Core.Enums;

namespace MassStorageStableTestTool.Core.Exceptions;

/// <summary>
/// 测试执行异常
/// </summary>
public class TestExecutionException : Exception
{
    /// <summary>
    /// 工具名称
    /// </summary>
    public string ToolName { get; }

    /// <summary>
    /// 测试状态
    /// </summary>
    public TestStatus Status { get; }

    /// <summary>
    /// 错误代码
    /// </summary>
    public int? ErrorCode { get; }

    /// <summary>
    /// 使用工具名称和错误消息初始化异常。
    /// </summary>
    /// <param name="toolName">工具名称</param>
    /// <param name="message">错误消息</param>
    public TestExecutionException(string toolName, string message) 
        : base($"测试工具 '{toolName}' 执行失败: {message}")
    {
        ToolName = toolName;
        Status = TestStatus.Failed;
    }

    /// <summary>
    /// 使用工具名称、错误消息和测试状态初始化异常。
    /// </summary>
    /// <param name="toolName">工具名称</param>
    /// <param name="message">错误消息</param>
    /// <param name="status">测试状态</param>
    public TestExecutionException(string toolName, string message, TestStatus status) 
        : base($"测试工具 '{toolName}' 执行失败: {message}")
    {
        ToolName = toolName;
        Status = status;
    }

    /// <summary>
    /// 使用工具名称、错误消息和错误代码初始化异常。
    /// </summary>
    /// <param name="toolName">工具名称</param>
    /// <param name="message">错误消息</param>
    /// <param name="errorCode">错误代码</param>
    public TestExecutionException(string toolName, string message, int errorCode) 
        : base($"测试工具 '{toolName}' 执行失败: {message} (错误代码: {errorCode})")
    {
        ToolName = toolName;
        Status = TestStatus.Failed;
        ErrorCode = errorCode;
    }

    /// <summary>
    /// 使用工具名称、错误消息和内部异常初始化异常。
    /// </summary>
    /// <param name="toolName">工具名称</param>
    /// <param name="message">错误消息</param>
    /// <param name="innerException">内部异常</param>
    public TestExecutionException(string toolName, string message, Exception innerException) 
        : base($"测试工具 '{toolName}' 执行失败: {message}", innerException)
    {
        ToolName = toolName;
        Status = TestStatus.Failed;
    }

    /// <summary>
    /// 使用工具名称、错误消息、测试状态和内部异常初始化异常。
    /// </summary>
    /// <param name="toolName">工具名称</param>
    /// <param name="message">错误消息</param>
    /// <param name="status">测试状态</param>
    /// <param name="innerException">内部异常</param>
    public TestExecutionException(string toolName, string message, TestStatus status, Exception innerException) 
        : base($"测试工具 '{toolName}' 执行失败: {message}", innerException)
    {
        ToolName = toolName;
        Status = status;
    }
}
