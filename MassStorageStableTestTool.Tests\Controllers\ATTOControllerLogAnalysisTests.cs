using Microsoft.Extensions.Logging;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using MassStorageStableTestTool.Automation.Controllers;
using MassStorageStableTestTool.Automation.GUI;
using MassStorageStableTestTool.Core.Models;
using System.Reflection;

namespace MassStorageStableTestTool.Tests.Controllers
{
    [TestClass]
    public class ATTOControllerLogAnalysisTests
    {
        private ATTOController _controller;
        private Mock<ILogger<ATTOController>> _mockLogger;
        private Mock<AutomationHelper> _mockAutomationHelper;
        private TestToolConfig _testConfig;

        [TestInitialize]
        public void Setup()
        {
            _mockLogger = new Mock<ILogger<ATTOController>>();
            _mockAutomationHelper = new Mock<AutomationHelper>();
            _testConfig = new TestToolConfig
            {
                ExecutablePath = @"C:\Program Files\ATTO\Disk Benchmark\Bench32.exe",
                Name = "ATTO"
            };

            _controller = new ATTOController(_testConfig, _mockAutomationHelper.Object, _mockLogger.Object);
        }

        [TestMethod]
        public void AnalyzeLogForSuccess_SuccessfulTest_ReturnsSuccess()
        {
            // Arrange
            var successLogContent = @"Friday, October 10, 2025   9:57 AM
Running ATTO_Test_20251010_095530_J.bmk....
Testing completed sucessfully.";

            // Act
            var result = InvokeAnalyzeLogForSuccess(successLogContent);

            // Assert
            Assert.IsTrue(result.IsSuccess);
            Assert.IsTrue(result.SuccessMessage.Contains("ATTO测试成功完成"));
            Assert.IsTrue(result.SuccessMessage.Contains("ATTO_Test_20251010_095530_J.bmk"));
        }

        [TestMethod]
        public void AnalyzeLogForSuccess_FailedTest_ReturnsFailure()
        {
            // Arrange
            var failureLogContent = @"Friday, October 10, 2025   9:57 AM
Running ATTO_Test_20251010_095530_J.bmk....
Error: Test failed due to disk access error.";

            // Act
            var result = InvokeAnalyzeLogForSuccess(failureLogContent);

            // Assert
            Assert.IsFalse(result.IsSuccess);
            Assert.IsTrue(result.ErrorMessage.Contains("日志中发现错误信息"));
            Assert.IsTrue(result.ErrorMessage.Contains("Error: Test failed"));
        }

        [TestMethod]
        public void AnalyzeLogForSuccess_IncompleteTest_ReturnsFailure()
        {
            // Arrange
            var incompleteLogContent = @"Friday, October 10, 2025   9:57 AM
Running ATTO_Test_20251010_095530_J.bmk....";

            // Act
            var result = InvokeAnalyzeLogForSuccess(incompleteLogContent);

            // Assert
            Assert.IsFalse(result.IsSuccess);
            Assert.AreEqual("测试已开始但未找到成功完成的消息", result.ErrorMessage);
        }

        [TestMethod]
        public void AnalyzeLogForSuccess_EmptyLog_ReturnsFailure()
        {
            // Arrange
            var emptyLogContent = "";

            // Act
            var result = InvokeAnalyzeLogForSuccess(emptyLogContent);

            // Assert
            Assert.IsFalse(result.IsSuccess);
            Assert.AreEqual("日志内容为空", result.ErrorMessage);
        }

        [TestMethod]
        public void AnalyzeLogForSuccess_NoRunningMessage_ReturnsFailure()
        {
            // Arrange
            var noRunningLogContent = @"Friday, October 10, 2025   9:57 AM
Testing completed sucessfully.";

            // Act
            var result = InvokeAnalyzeLogForSuccess(noRunningLogContent);

            // Assert
            Assert.IsFalse(result.IsSuccess);
            Assert.AreEqual("未找到测试开始的消息", result.ErrorMessage);
        }

        /// <summary>
        /// 使用反射调用私有方法AnalyzeLogForSuccess
        /// </summary>
        private ATTOLogAnalysisResult InvokeAnalyzeLogForSuccess(string logContent)
        {
            var method = typeof(ATTOController).GetMethod("AnalyzeLogForSuccess", 
                BindingFlags.NonPublic | BindingFlags.Instance);
            
            if (method == null)
            {
                throw new InvalidOperationException("未找到AnalyzeLogForSuccess方法");
            }

            var result = method.Invoke(_controller, new object[] { logContent });
            return (ATTOLogAnalysisResult)result;
        }
    }
}
