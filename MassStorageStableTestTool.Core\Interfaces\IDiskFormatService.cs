using MassStorageStableTestTool.Core.Models;

namespace MassStorageStableTestTool.Core.Interfaces;

/// <summary>
/// 磁盘格式化服务接口
/// </summary>
public interface IDiskFormatService
{
    /// <summary>
    /// 格式化磁盘
    /// </summary>
    /// <param name="driveLetter">驱动器盘符（如 "E:"）</param>
    /// <param name="fileSystem">文件系统类型（FAT32, NTFS, exFAT）</param>
    /// <param name="volumeLabel">卷标名称</param>
    /// <param name="quickFormat">是否快速格式化</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <param name="progress">进度报告器</param>
    /// <returns>格式化结果</returns>
    Task<FormatResult> FormatDriveAsync(
        string driveLetter,
        string fileSystem = "FAT32",
        string volumeLabel = "TEST_DRIVE",
        bool quickFormat = true,
        CancellationToken cancellationToken = default,
        IProgress<ProgressEventArgs>? progress = null);

    /// <summary>
    /// 检查驱动器是否可以安全格式化
    /// </summary>
    /// <param name="driveLetter">驱动器盘符</param>
    /// <returns>检查结果</returns>
    Task<(bool CanFormat, List<string> Issues)> CheckDriveFormatabilityAsync(string driveLetter);

    /// <summary>
    /// 获取支持的文件系统列表
    /// </summary>
    /// <returns>支持的文件系统列表</returns>
    List<string> GetSupportedFileSystems();

    /// <summary>
    /// 验证文件系统类型是否受支持
    /// </summary>
    /// <param name="fileSystem">文件系统类型</param>
    /// <returns>是否支持</returns>
    bool IsFileSystemSupported(string fileSystem);
}

/// <summary>
/// 格式化结果
/// </summary>
public class FormatResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 格式化耗时
    /// </summary>
    public TimeSpan Duration { get; set; }

    /// <summary>
    /// 格式化后的文件系统
    /// </summary>
    public string? FileSystem { get; set; }

    /// <summary>
    /// 格式化后的卷标
    /// </summary>
    public string? VolumeLabel { get; set; }

    /// <summary>
    /// 详细日志
    /// </summary>
    public List<string> Logs { get; set; } = new();

    /// <summary>
    /// 异常信息
    /// </summary>
    public Exception? Exception { get; set; }

    /// <summary>
    /// 添加日志
    /// </summary>
    /// <param name="message">日志消息</param>
    public void AddLog(string message)
    {
        Logs.Add($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {message}");
    }

    /// <summary>
    /// 创建成功结果
    /// </summary>
    /// <param name="fileSystem">文件系统</param>
    /// <param name="volumeLabel">卷标</param>
    /// <param name="duration">耗时</param>
    /// <returns>成功结果</returns>
    public static FormatResult CreateSuccess(string fileSystem, string volumeLabel, TimeSpan duration)
    {
        return new FormatResult
        {
            Success = true,
            FileSystem = fileSystem,
            VolumeLabel = volumeLabel,
            Duration = duration
        };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    /// <param name="errorMessage">错误消息</param>
    /// <param name="exception">异常</param>
    /// <returns>失败结果</returns>
    public static FormatResult CreateFailure(string errorMessage, Exception? exception = null)
    {
        return new FormatResult
        {
            Success = false,
            ErrorMessage = errorMessage,
            Exception = exception
        };
    }
}
