using MassStorageStableTestTool.Core.Enums;
using MassStorageStableTestTool.Core.Models;

namespace MassStorageStableTestTool.Core.Interfaces;

/// <summary>
/// 报告服务接口
/// </summary>
public interface IReportService
{
    /// <summary>
    /// 生成测试报告
    /// </summary>
    /// <param name="testSuiteResult">测试套件结果</param>
    /// <param name="format">报告格式</param>
    /// <returns>报告内容</returns>
    Task<string> GenerateReportAsync(TestSuiteResult testSuiteResult, ReportFormat format = ReportFormat.Text);

    /// <summary>
    /// 生成并保存报告
    /// </summary>
    /// <param name="testSuiteResult">测试套件结果</param>
    /// <param name="outputPath">输出路径</param>
    /// <param name="format">报告格式</param>
    /// <returns>保存的文件路径</returns>
    Task<string> GenerateAndSaveReportAsync(TestSuiteResult testSuiteResult, string? outputPath = null, ReportFormat format = ReportFormat.Text);

    /// <summary>
    /// 导出报告到文件
    /// </summary>
    /// <param name="reportContent">报告内容</param>
    /// <param name="filePath">文件路径</param>
    /// <param name="format">报告格式</param>
    /// <returns>导出结果</returns>
    Task<bool> ExportReportAsync(string reportContent, string filePath, ReportFormat format);

    /// <summary>
    /// 获取报告模板
    /// </summary>
    /// <param name="templateName">模板名称</param>
    /// <returns>模板内容</returns>
    Task<string> GetReportTemplateAsync(string templateName);

    /// <summary>
    /// 设置报告模板
    /// </summary>
    /// <param name="templateName">模板名称</param>
    /// <param name="templateContent">模板内容</param>
    /// <returns>设置结果</returns>
    Task<bool> SetReportTemplateAsync(string templateName, string templateContent);

    /// <summary>
    /// 获取可用的报告模板列表
    /// </summary>
    /// <returns>模板名称列表</returns>
    Task<List<string>> GetAvailableTemplatesAsync();

    /// <summary>
    /// 生成摘要报告
    /// </summary>
    /// <param name="testSuiteResult">测试套件结果</param>
    /// <returns>摘要报告内容</returns>
    Task<string> GenerateSummaryReportAsync(TestSuiteResult testSuiteResult);

    /// <summary>
    /// 生成详细报告
    /// </summary>
    /// <param name="testSuiteResult">测试套件结果</param>
    /// <returns>详细报告内容</returns>
    Task<string> GenerateDetailedReportAsync(TestSuiteResult testSuiteResult);

    /// <summary>
    /// 生成性能报告
    /// </summary>
    /// <param name="testSuiteResult">测试套件结果</param>
    /// <returns>性能报告内容</returns>
    Task<string> GeneratePerformanceReportAsync(TestSuiteResult testSuiteResult);

    /// <summary>
    /// 生成比较报告
    /// </summary>
    /// <param name="testResults">测试结果列表</param>
    /// <returns>比较报告内容</returns>
    Task<string> GenerateComparisonReportAsync(List<TestSuiteResult> testResults);

    /// <summary>
    /// 发送报告邮件
    /// </summary>
    /// <param name="reportContent">报告内容</param>
    /// <param name="recipients">收件人列表</param>
    /// <param name="subject">邮件主题</param>
    /// <returns>发送结果</returns>
    Task<bool> SendReportEmailAsync(string reportContent, List<string> recipients, string subject);

    /// <summary>
    /// 获取报告历史
    /// </summary>
    /// <param name="count">历史记录数量</param>
    /// <returns>报告历史列表</returns>
    Task<List<ReportHistory>> GetReportHistoryAsync(int count = 10);

    /// <summary>
    /// 删除报告文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>删除结果</returns>
    Task<bool> DeleteReportAsync(string filePath);

    /// <summary>
    /// 清理过期报告
    /// </summary>
    /// <param name="retentionDays">保留天数</param>
    /// <returns>清理的文件数量</returns>
    Task<int> CleanupExpiredReportsAsync(int retentionDays = 30);

    /// <summary>
    /// 验证报告内容
    /// </summary>
    /// <param name="reportContent">报告内容</param>
    /// <param name="format">报告格式</param>
    /// <returns>验证结果</returns>
    (bool IsValid, List<string> Errors) ValidateReport(string reportContent, ReportFormat format);

    /// <summary>
    /// 报告生成完成事件
    /// </summary>
    event EventHandler<ReportGeneratedEventArgs>? ReportGenerated;
}

/// <summary>
/// 报告历史记录
/// </summary>
public class ReportHistory
{
    /// <summary>
    /// 报告ID
    /// </summary>
    public string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 报告文件路径
    /// </summary>
    public string FilePath { get; set; } = string.Empty;

    /// <summary>
    /// 报告格式
    /// </summary>
    public ReportFormat Format { get; set; }

    /// <summary>
    /// 生成时间
    /// </summary>
    public DateTime GeneratedAt { get; set; }

    /// <summary>
    /// 测试配置名称
    /// </summary>
    public string ConfigurationName { get; set; } = string.Empty;

    /// <summary>
    /// 目标驱动器
    /// </summary>
    public string TargetDrive { get; set; } = string.Empty;

    /// <summary>
    /// 测试工具列表
    /// </summary>
    public List<string> TestTools { get; set; } = new();

    /// <summary>
    /// 测试结果摘要
    /// </summary>
    public string Summary { get; set; } = string.Empty;

    /// <summary>
    /// 文件大小（字节）
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 标签
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// 备注
    /// </summary>
    public string? Notes { get; set; }
}

/// <summary>
/// 报告生成事件参数
/// </summary>
public class ReportGeneratedEventArgs : EventArgs
{
    /// <summary>
    /// 报告文件路径
    /// </summary>
    public string FilePath { get; set; } = string.Empty;

    /// <summary>
    /// 报告格式
    /// </summary>
    public ReportFormat Format { get; set; }

    /// <summary>
    /// 报告大小（字节）
    /// </summary>
    public long Size { get; set; }

    /// <summary>
    /// 生成耗时
    /// </summary>
    public TimeSpan Duration { get; set; }

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;
}


