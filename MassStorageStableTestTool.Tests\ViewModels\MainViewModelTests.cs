using Xunit;
using Moq;
using Microsoft.Extensions.Logging;
using MassStorageStableTestTool.UI.ViewModels;
using MassStorageStableTestTool.Core.Interfaces;
using MassStorageStableTestTool.Automation.Services;
using MassStorageStableTestTool.UI.Models;
using System.Collections.ObjectModel;
using UIDriveInfo = MassStorageStableTestTool.UI.Models.DriveInfo;

namespace MassStorageStableTestTool.Tests.ViewModels
{
    public class MainViewModelTests
    {
        private readonly Mock<ILogger<MainViewModel>> _mockLogger;
        private readonly Mock<ITestOrchestrator> _mockTestOrchestrator;
        private readonly Mock<ISystemInfoService> _mockSystemInfoService;
        private readonly Mock<IReportService> _mockReportService;
        private readonly MainViewModel _viewModel;

        public MainViewModelTests()
        {
            _mockLogger = new Mock<ILogger<MainViewModel>>();
            _mockTestOrchestrator = new Mock<ITestOrchestrator>();
            _mockSystemInfoService = new Mock<ISystemInfoService>();
            _mockReportService = new Mock<IReportService>();

            _viewModel = new MainViewModel(
                _mockLogger.Object,
                _mockTestOrchestrator.Object,
                _mockSystemInfoService.Object,
                _mockReportService.Object);
        }

        [Fact]
        public void DriveStatusCounts_InitiallyZero()
        {
            // Assert
            Assert.Equal(0, _viewModel.TestingDriveCount);
            Assert.Equal(0, _viewModel.CompletedDriveCount);
            Assert.Equal(0, _viewModel.WaitingDriveCount);
            Assert.Equal(0, _viewModel.FailedDriveCount);
        }

        [Fact]
        public void UpdateDriveStatusCounts_WithDifferentStatuses_UpdatesCorrectly()
        {
            // Arrange
            var drive1 = new UIDriveInfo { Name = "D:", TestStatus = DriveTestStatus.Testing };
            var drive2 = new UIDriveInfo { Name = "E:", TestStatus = DriveTestStatus.Completed };
            var drive3 = new UIDriveInfo { Name = "F:", TestStatus = DriveTestStatus.Ready };
            var drive4 = new UIDriveInfo { Name = "G:", TestStatus = DriveTestStatus.Failed };

            _viewModel.AvailableDrives.Add(drive1);
            _viewModel.AvailableDrives.Add(drive2);
            _viewModel.AvailableDrives.Add(drive3);
            _viewModel.AvailableDrives.Add(drive4);

            // Act - 通过反射调用私有方法
            var method = typeof(MainViewModel).GetMethod("UpdateDriveStatusCounts", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            method?.Invoke(_viewModel, null);

            // Assert
            Assert.Equal(1, _viewModel.TestingDriveCount);
            Assert.Equal(1, _viewModel.CompletedDriveCount);
            Assert.Equal(1, _viewModel.WaitingDriveCount);
            Assert.Equal(1, _viewModel.FailedDriveCount);
        }

        [Fact]
        public void DrivePropertyChanged_TestStatusChanged_UpdatesCounts()
        {
            // Arrange
            var drive = new UIDriveInfo { Name = "D:", TestStatus = DriveTestStatus.Ready };
            _viewModel.AvailableDrives.Add(drive);

            // Act
            drive.TestStatus = DriveTestStatus.Testing;

            // Assert - 由于PropertyChanged事件会自动触发UpdateDriveStatusCounts
            Assert.Equal(1, _viewModel.TestingDriveCount);
            Assert.Equal(0, _viewModel.CompletedDriveCount);
            Assert.Equal(0, _viewModel.WaitingDriveCount);
            Assert.Equal(0, _viewModel.FailedDriveCount);
        }
    }
}
