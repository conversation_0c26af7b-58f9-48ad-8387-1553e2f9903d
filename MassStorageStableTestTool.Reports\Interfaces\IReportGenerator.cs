using MassStorageStableTestTool.Core.Models;
using MassStorageStableTestTool.Core.Enums;

namespace MassStorageStableTestTool.Reports.Interfaces;

/// <summary>
/// 报告生成器接口
/// </summary>
public interface IReportGenerator
{
    /// <summary>
    /// 支持的报告格式
    /// </summary>
    ReportFormat SupportedFormat { get; }

    /// <summary>
    /// 生成器名称
    /// </summary>
    string GeneratorName { get; }

    /// <summary>
    /// 生成报告
    /// </summary>
    /// <param name="testSuiteResult">测试套件结果</param>
    /// <param name="templateName">模板名称（可选）</param>
    /// <returns>报告内容</returns>
    Task<string> GenerateReportAsync(TestSuiteResult testSuiteResult, string? templateName = null);

    /// <summary>
    /// 验证报告内容
    /// </summary>
    /// <param name="reportContent">报告内容</param>
    /// <returns>验证结果</returns>
    (bool IsValid, List<string> Errors) ValidateReport(string reportContent);

    /// <summary>
    /// 获取可用的模板列表
    /// </summary>
    /// <returns>模板名称列表</returns>
    Task<List<string>> GetAvailableTemplatesAsync();

    /// <summary>
    /// 获取默认文件扩展名
    /// </summary>
    /// <returns>文件扩展名</returns>
    string GetDefaultFileExtension();
}
