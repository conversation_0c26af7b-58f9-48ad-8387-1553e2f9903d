namespace MassStorageStableTestTool.Core.Models;

/// <summary>
/// 性能指标模型
/// </summary>
public class PerformanceMetrics
{
    /// <summary>
    /// 读取速度 (MB/s)
    /// </summary>
    public double? ReadSpeed { get; set; }

    /// <summary>
    /// 写入速度 (MB/s)
    /// </summary>
    public double? WriteSpeed { get; set; }

    /// <summary>
    /// 读取IOPS
    /// </summary>
    public double? ReadIOPS { get; set; }

    /// <summary>
    /// 写入IOPS
    /// </summary>
    public double? WriteIOPS { get; set; }

    /// <summary>
    /// 读取延迟 (ms)
    /// </summary>
    public double? ReadLatency { get; set; }

    /// <summary>
    /// 写入延迟 (ms)
    /// </summary>
    public double? WriteLatency { get; set; }

    /// <summary>
    /// 平均延迟 (ms)
    /// </summary>
    public double? AverageLatency { get; set; }

    /// <summary>
    /// 最小延迟 (ms)
    /// </summary>
    public double? MinLatency { get; set; }

    /// <summary>
    /// 最大延迟 (ms)
    /// </summary>
    public double? MaxLatency { get; set; }

    /// <summary>
    /// 总带宽 (MB/s)
    /// </summary>
    public double? TotalBandwidth { get; set; }

    /// <summary>
    /// 总IOPS
    /// </summary>
    public double? TotalIOPS { get; set; }

    /// <summary>
    /// 测试的数据量 (MB)
    /// </summary>
    public double? DataVolume { get; set; }

    /// <summary>
    /// 错误数量
    /// </summary>
    public int ErrorCount { get; set; }

    /// <summary>
    /// CPU使用率 (%)
    /// </summary>
    public double? CpuUsage { get; set; }

    /// <summary>
    /// 内存使用率 (%)
    /// </summary>
    public double? MemoryUsage { get; set; }

    /// <summary>
    /// 队列深度
    /// </summary>
    public int? QueueDepth { get; set; }

    /// <summary>
    /// 块大小 (KB)
    /// </summary>
    public int? BlockSize { get; set; }

    /// <summary>
    /// 线程数
    /// </summary>
    public int? ThreadCount { get; set; }

    /// <summary>
    /// 随机访问百分比
    /// </summary>
    public double? RandomAccessPercentage { get; set; }

    /// <summary>
    /// 读取百分比
    /// </summary>
    public double? ReadPercentage { get; set; }

    /// <summary>
    /// 写入百分比
    /// </summary>
    public double? WritePercentage { get; set; }

    /// <summary>
    /// 获取综合性能评分
    /// </summary>
    /// <returns>性能评分 (0-100)</returns>
    public double GetOverallScore()
    {
        var scores = new List<double>();

        // 读取性能评分 (基于速度，假设100MB/s为满分)
        if (ReadSpeed.HasValue)
        {
            scores.Add(Math.Min(ReadSpeed.Value / 100.0 * 100, 100));
        }

        // 写入性能评分 (基于速度，假设50MB/s为满分)
        if (WriteSpeed.HasValue)
        {
            scores.Add(Math.Min(WriteSpeed.Value / 50.0 * 100, 100));
        }

        // IOPS评分 (基于总IOPS，假设10000为满分)
        if (TotalIOPS.HasValue)
        {
            scores.Add(Math.Min(TotalIOPS.Value / 10000.0 * 100, 100));
        }

        // 延迟评分 (延迟越低分数越高，假设1ms为满分)
        if (AverageLatency.HasValue && AverageLatency.Value > 0)
        {
            scores.Add(Math.Max(0, 100 - AverageLatency.Value));
        }

        // 错误率评分 (无错误为满分)
        if (ErrorCount == 0)
        {
            scores.Add(100);
        }
        else
        {
            scores.Add(Math.Max(0, 100 - ErrorCount * 10));
        }

        return scores.Any() ? scores.Average() : 0;
    }

    /// <summary>
    /// 获取性能等级
    /// </summary>
    /// <returns>性能等级</returns>
    public string GetPerformanceGrade()
    {
        var score = GetOverallScore();
        return score switch
        {
            >= 90 => "优秀",
            >= 80 => "良好",
            >= 70 => "中等",
            >= 60 => "及格",
            _ => "较差"
        };
    }

    /// <summary>
    /// 转换为字典格式
    /// </summary>
    /// <returns>性能指标字典</returns>
    public Dictionary<string, object> ToDictionary()
    {
        var dict = new Dictionary<string, object>();

        if (ReadSpeed.HasValue) dict["ReadSpeed"] = ReadSpeed.Value;
        if (WriteSpeed.HasValue) dict["WriteSpeed"] = WriteSpeed.Value;
        if (ReadIOPS.HasValue) dict["ReadIOPS"] = ReadIOPS.Value;
        if (WriteIOPS.HasValue) dict["WriteIOPS"] = WriteIOPS.Value;
        if (ReadLatency.HasValue) dict["ReadLatency"] = ReadLatency.Value;
        if (WriteLatency.HasValue) dict["WriteLatency"] = WriteLatency.Value;
        if (AverageLatency.HasValue) dict["AverageLatency"] = AverageLatency.Value;
        if (TotalBandwidth.HasValue) dict["TotalBandwidth"] = TotalBandwidth.Value;
        if (TotalIOPS.HasValue) dict["TotalIOPS"] = TotalIOPS.Value;
        
        dict["ErrorCount"] = ErrorCount;
        dict["OverallScore"] = GetOverallScore();
        dict["PerformanceGrade"] = GetPerformanceGrade();

        return dict;
    }
}
