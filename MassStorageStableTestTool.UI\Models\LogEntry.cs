using System.ComponentModel;
using System.Windows.Media;

namespace MassStorageStableTestTool.UI.Models
{
    public enum LogLevel
    {
        Debug,
        Info,
        Warning,
        Error
    }

    public class LogEntry : INotifyPropertyChanged
    {
        public DateTime Timestamp { get; set; }
        public LogLevel Level { get; set; }
        public string Message { get; set; } = string.Empty;
        public string Source { get; set; } = string.Empty;

        public string FormattedMessage => $"[{Timestamp:HH:mm:ss}] {Message}";

        public Brush LevelColor
        {
            get
            {
                return Level switch
                {
                    LogLevel.Debug => new SolidColorBrush(Colors.Gray),
                    LogLevel.Info => new SolidColorBrush(Colors.Black),
                    LogLevel.Warning => new SolidColorBrush(Colors.Orange),
                    LogLevel.Error => new SolidColorBrush(Colors.Red),
                    _ => new SolidColorBrush(Colors.Black)
                };
            }
        }

        public string LevelIcon
        {
            get
            {
                return Level switch
                {
                    LogLevel.Debug => "🔍",
                    LogLevel.Info => "ℹ️",
                    LogLevel.Warning => "⚠️",
                    LogLevel.Error => "❌",
                    _ => "📝"
                };
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
