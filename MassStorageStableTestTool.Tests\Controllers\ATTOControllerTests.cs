using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using MassStorageStableTestTool.Core.Models;
using MassStorageStableTestTool.Core.Enums;
using MassStorageStableTestTool.Core.Services;
using MassStorageStableTestTool.Core.Interfaces;
using MassStorageStableTestTool.Automation.Controllers;
using MassStorageStableTestTool.Automation.GUI;
using MassStorageStableTestTool.Automation.Services;
using System.IO;
using Xunit;
using Xunit.Abstractions;

namespace MassStorageStableTestTool.Tests.Controllers;

/// <summary>
/// ATTO控制器测试类
/// </summary>
public class ATTOControllerTests : IDisposable
{
    private readonly ITestOutputHelper _output;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<ATTOController> _logger;
    private readonly AutomationHelper _automationHelper;
    private readonly TestToolConfig _attoConfig;
    private readonly string _testDirectory;

    public ATTOControllerTests(ITestOutputHelper output)
    {
        _output = output;
        _testDirectory = Path.Combine(Path.GetTempPath(), "ATTOTests", Guid.NewGuid().ToString());
        Directory.CreateDirectory(_testDirectory);

        // 设置服务容器
        var services = new ServiceCollection();
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
        services.AddSingleton<AutomationHelper>();
        services.AddSingleton<ConfigurationService>();
        services.AddSingleton<IControllerFactory, ControllerFactory>();

        _serviceProvider = services.BuildServiceProvider();
        _logger = _serviceProvider.GetRequiredService<ILogger<ATTOController>>();
        _automationHelper = _serviceProvider.GetRequiredService<AutomationHelper>();

        // 创建ATTO配置
        _attoConfig = CreateTestATTOConfig();
    }

    /// <summary>
    /// 创建测试用的ATTO配置
    /// </summary>
    /// <returns>ATTO配置</returns>
    private TestToolConfig CreateTestATTOConfig()
    {
        // 使用实际的ATTO路径
        var attoPath = @".\third part tools\ATTO DiskBenchmark(v3.05)\ATTODiskBenchmark.exe";
        var fullPath = Path.GetFullPath(attoPath);

        return new TestToolConfig
        {
            Name = "ATTO",
            Type = TestToolType.CLI,
            ExecutablePath = fullPath,
            Description = "ATTO Disk Benchmark - 测试版本",
            Version = "3.05",
            CommandTemplate = "-f \"{BenchmarkFile}\" -x",
            Timeouts = new TimeoutSettings
            {
                LaunchTimeout = TimeSpan.FromSeconds(10),
                TestTimeout = TimeSpan.FromMinutes(5), // 测试时缩短超时时间
                CleanupTimeout = TimeSpan.FromSeconds(10)
            },
            DefaultParameters = new Dictionary<string, object>
            {
                ["IORuntime"] = 10, // 测试时缩短运行时间
                ["QueueDepth"] = 4,
                ["FileSize"] = 67108864, // 64MB，测试时使用较小文件
                ["VerifyData"] = false,
                ["DirectIO"] = true,
                ["BypassWriteCache"] = false,
                ["ContinuousIO"] = false,
                ["Pattern"] = 0,
                ["IOSize1"] = 512,
                ["IOSize2"] = 65536
            },
            Enabled = true,
            Priority = 3,
            SupportedFileSystems = new List<string> { "NTFS", "FAT32", "exFAT" },
            MinimumDiskSpace = 64
        };
    }

    /// <summary>
    /// 测试ATTO控制器创建
    /// </summary>
    [Fact]
    public void Constructor_ShouldCreateController_WhenValidParametersProvided()
    {
        // Arrange & Act
        var controller = new ATTOController(_attoConfig, _automationHelper, _logger);

        // Assert
        Assert.NotNull(controller);
        Assert.Equal("ATTO", controller.ToolName);
        Assert.Equal(TestToolType.CLI, controller.ToolType);
        Assert.Equal(_attoConfig, controller.Configuration);
    }

    /// <summary>
    /// 测试工具可用性检查
    /// </summary>
    [Fact]
    public void IsToolAvailable_ShouldReturnTrue_WhenExecutableExists()
    {
        // Arrange
        var controller = new ATTOController(_attoConfig, _automationHelper, _logger);

        // Act
        var isAvailable = controller.IsToolAvailable();

        // Assert
        if (File.Exists(_attoConfig.ExecutablePath))
        {
            Assert.True(isAvailable);
            _output.WriteLine($"ATTO工具可用: {_attoConfig.ExecutablePath}");
        }
        else
        {
            Assert.False(isAvailable);
            _output.WriteLine($"ATTO工具不可用: {_attoConfig.ExecutablePath}");
        }
    }

    /// <summary>
    /// 测试配置验证
    /// </summary>
    [Fact]
    public void ValidateConfiguration_ShouldReturnValid_WhenConfigurationIsCorrect()
    {
        // Arrange
        var controller = new ATTOController(_attoConfig, _automationHelper, _logger);
        var testConfig = new TestConfiguration
        {
            TargetDrive = "C:",
            TimeoutSeconds = 10,
            ThreadCount = 4,
            TestSize = "64MB",
            OutputDirectory = _testDirectory
        };

        // Act
        var (isValid, errors) = controller.ValidateConfiguration(testConfig);

        // Assert
        Assert.True(isValid);
        Assert.Empty(errors);
    }

    /// <summary>
    /// 测试配置验证失败情况
    /// </summary>
    [Fact]
    public void ValidateConfiguration_ShouldReturnInvalid_WhenTargetDriveIsEmpty()
    {
        // Arrange
        var controller = new ATTOController(_attoConfig, _automationHelper, _logger);
        var testConfig = new TestConfiguration
        {
            TargetDrive = "", // 空的目标驱动器
            TimeoutSeconds = 10,
            ThreadCount = 4,
            TestSize = "64MB"
        };

        // Act
        var (isValid, errors) = controller.ValidateConfiguration(testConfig);

        // Assert
        Assert.False(isValid);
        Assert.NotEmpty(errors);
        Assert.Contains(errors, e => e.Contains("目标驱动器"));
    }

    /// <summary>
    /// 测试支持的参数获取
    /// </summary>
    [Fact]
    public void GetSupportedParameters_ShouldReturnParameters()
    {
        // Arrange
        var controller = new ATTOController(_attoConfig, _automationHelper, _logger);

        // Act
        var parameters = controller.GetSupportedParameters();

        // Assert
        Assert.NotNull(parameters);
        // ATTO控制器应该返回支持的参数
        _output.WriteLine($"支持的参数数量: {parameters.Count}");
        foreach (var param in parameters)
        {
            _output.WriteLine($"参数: {param.Key} - {param.Value.Description}");
        }
    }

    /// <summary>
    /// 测试状态变化事件
    /// </summary>
    [Fact]
    public void StatusChanged_ShouldFireEvent_WhenStatusChanges()
    {
        // Arrange
        var controller = new ATTOController(_attoConfig, _automationHelper, _logger);
        var eventFired = false;
        TestStatus? newStatus = null;

        controller.StatusChanged += (sender, args) =>
        {
            eventFired = true;
            newStatus = args.NewStatus;
        };

        // Act
        var currentStatus = controller.GetCurrentStatus();

        // Assert
        Assert.Equal(TestStatus.NotStarted, currentStatus);
        // 注意：状态变化事件只有在实际状态改变时才会触发
    }

    /// <summary>
    /// 模拟测试执行（仅在ATTO工具可用时运行）
    /// </summary>
    [Fact(Skip = "需要实际的ATTO工具和测试驱动器")]
    public async Task ExecuteTestAsync_ShouldCompleteSuccessfully_WhenValidConfiguration()
    {
        // 这个测试需要实际的ATTO工具和可用的测试驱动器
        // 在CI/CD环境中应该跳过此测试

        // Arrange
        if (!File.Exists(_attoConfig.ExecutablePath))
        {
            _output.WriteLine("跳过测试：ATTO工具不可用");
            return;
        }

        var controller = new ATTOController(_attoConfig, _automationHelper, _logger);
        var testConfig = new TestConfiguration
        {
            TargetDrive = "C:", // 注意：这里应该使用测试专用的驱动器
            TimeoutSeconds = 5,   // 短时间测试
            ThreadCount = 2,
            TestSize = "32MB",
            OutputDirectory = _testDirectory
        };

        var progress = new Progress<MassStorageStableTestTool.Core.Interfaces.ProgressEventArgs>(args =>
        {
            _output.WriteLine($"进度: {args.Progress}% - {args.Status}");
        });

        // Act
        var result = await controller.ExecuteTestAsync(testConfig, CancellationToken.None, progress);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("ATTO", result.ToolName);
        _output.WriteLine($"测试结果: {(result.Success ? "成功" : "失败")}");
        if (!result.Success)
        {
            _output.WriteLine($"错误信息: {result.ErrorMessage}");
        }
    }

    /// <summary>
    /// 清理测试资源
    /// </summary>
    public void Dispose()
    {
        try
        {
            if (Directory.Exists(_testDirectory))
            {
                Directory.Delete(_testDirectory, true);
            }
        }
        catch (Exception ex)
        {
            _output.WriteLine($"清理测试目录失败: {ex.Message}");
        }

        (_serviceProvider as IDisposable)?.Dispose();
    }
}
