using MassStorageStableTestTool.Core.Enums;

namespace MassStorageStableTestTool.Core.Models;

/// <summary>
/// 测试结果模型
/// </summary>
public class TestResult
{
    /// <summary>
    /// 工具名称
    /// </summary>
    public string ToolName { get; set; } = string.Empty;

    /// <summary>
    /// 测试是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 测试状态
    /// </summary>
    public TestStatus Status { get; set; } = TestStatus.NotStarted;

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 测试持续时间
    /// </summary>
    public TimeSpan Duration => EndTime - StartTime;

    /// <summary>
    /// 测试数据字典
    /// </summary>
    public Dictionary<string, object> Data { get; set; } = new();

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 异常详情
    /// </summary>
    public Exception? Exception { get; set; }

    /// <summary>
    /// 警告信息列表
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// 详细日志
    /// </summary>
    public List<string> Logs { get; set; } = new();

    /// <summary>
    /// 输出文件路径列表
    /// </summary>
    public List<string> OutputFiles { get; set; } = new();

    /// <summary>
    /// 性能指标
    /// </summary>
    public PerformanceMetrics? Performance { get; set; }

    /// <summary>
    /// 添加日志条目
    /// </summary>
    /// <param name="message">日志消息</param>
    public void AddLog(string message)
    {
        Logs.Add($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {message}");
    }

    /// <summary>
    /// 添加警告
    /// </summary>
    /// <param name="warning">警告信息</param>
    public void AddWarning(string warning)
    {
        Warnings.Add(warning);
        AddLog($"WARNING: {warning}");
    }

    /// <summary>
    /// 设置性能数据
    /// </summary>
    /// <param name="key">数据键</param>
    /// <param name="value">数据值</param>
    public void SetPerformanceData(string key, object value)
    {
        Data[key] = value;
    }

    /// <summary>
    /// 获取性能数据
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="key">数据键</param>
    /// <returns>数据值</returns>
    public T? GetPerformanceData<T>(string key)
    {
        if (Data.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return default;
    }

    /// <summary>
    /// 标记测试完成
    /// </summary>
    /// <param name="success">是否成功</param>
    /// <param name="errorMessage">错误信息（如果失败）</param>
    public void Complete(bool success, string? errorMessage = null)
    {
        Success = success;
        Status = success ? TestStatus.Completed : TestStatus.Failed;
        EndTime = DateTime.Now;
        ErrorMessage = errorMessage;
        
        AddLog($"测试完成: {(success ? "成功" : "失败")}");
        if (!success && !string.IsNullOrEmpty(errorMessage))
        {
            AddLog($"错误: {errorMessage}");
        }
    }

    /// <summary>
    /// 标记测试取消
    /// </summary>
    public void Cancel()
    {
        Success = false;
        Status = TestStatus.Cancelled;
        EndTime = DateTime.Now;
        AddLog("测试已取消");
    }

    /// <summary>
    /// 标记测试超时
    /// </summary>
    public void Timeout()
    {
        Success = false;
        Status = TestStatus.Timeout;
        EndTime = DateTime.Now;
        ErrorMessage = "测试超时";
        AddLog("测试超时");
    }
}
