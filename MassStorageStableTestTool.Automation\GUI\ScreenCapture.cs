using FlaUI.Core.AutomationElements;
using FlaUI.Core.Capturing;
using Microsoft.Extensions.Logging;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;

namespace MassStorageStableTestTool.Automation.GUI;

/// <summary>
/// 屏幕截图功能，用于调试和记录测试过程
/// </summary>
public class ScreenCapture
{
    private readonly ILogger<ScreenCapture> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public ScreenCapture(ILogger<ScreenCapture> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 截取整个屏幕
    /// </summary>
    /// <param name="filePath">保存文件路径</param>
    /// <param name="format">图片格式</param>
    /// <returns>是否成功截图</returns>
    public bool CaptureScreen(string filePath, ImageFormat? format = null)
    {
        if (string.IsNullOrWhiteSpace(filePath))
            throw new ArgumentException("文件路径不能为空", nameof(filePath));

        format ??= ImageFormat.Png;

        try
        {
            // 确保目录存在
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            using var bitmap = Capture.Screen();
            bitmap.Bitmap.Save(filePath, format);

            _logger.LogDebug($"成功截取屏幕并保存到: {filePath}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError($"截取屏幕失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 截取指定窗口
    /// </summary>
    /// <param name="window">要截图的窗口</param>
    /// <param name="filePath">保存文件路径</param>
    /// <param name="format">图片格式</param>
    /// <returns>是否成功截图</returns>
    public bool CaptureWindow(Window window, string filePath, ImageFormat? format = null)
    {
        if (window == null)
            throw new ArgumentNullException(nameof(window));
        if (string.IsNullOrWhiteSpace(filePath))
            throw new ArgumentException("文件路径不能为空", nameof(filePath));

        format ??= ImageFormat.Png;

        try
        {
            // 确保目录存在
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            using var bitmap = Capture.Element(window);
            bitmap.Bitmap.Save(filePath, format);

            _logger.LogDebug($"成功截取窗口 '{window.Title}' 并保存到: {filePath}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError($"截取窗口 '{window.Title}' 失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 截取指定元素
    /// </summary>
    /// <param name="element">要截图的元素</param>
    /// <param name="filePath">保存文件路径</param>
    /// <param name="format">图片格式</param>
    /// <returns>是否成功截图</returns>
    public bool CaptureElement(AutomationElement element, string filePath, ImageFormat? format = null)
    {
        if (element == null)
            throw new ArgumentNullException(nameof(element));
        if (string.IsNullOrWhiteSpace(filePath))
            throw new ArgumentException("文件路径不能为空", nameof(filePath));

        format ??= ImageFormat.Png;

        try
        {
            // 确保目录存在
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            using var bitmap = Capture.Element(element);
            bitmap.Bitmap.Save(filePath, format);

            _logger.LogDebug($"成功截取元素 '{element.Name}' 并保存到: {filePath}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError($"截取元素 '{element.Name}' 失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 截取指定区域
    /// </summary>
    /// <param name="rectangle">要截图的区域</param>
    /// <param name="filePath">保存文件路径</param>
    /// <param name="format">图片格式</param>
    /// <returns>是否成功截图</returns>
    public bool CaptureRectangle(Rectangle rectangle, string filePath, ImageFormat? format = null)
    {
        if (string.IsNullOrWhiteSpace(filePath))
            throw new ArgumentException("文件路径不能为空", nameof(filePath));

        format ??= ImageFormat.Png;

        try
        {
            // 确保目录存在
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            using var bitmap = Capture.Rectangle(rectangle);
            bitmap.Bitmap.Save(filePath, format);

            _logger.LogDebug($"成功截取区域 ({rectangle.X}, {rectangle.Y}, {rectangle.Width}, {rectangle.Height}) 并保存到: {filePath}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError($"截取指定区域失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 生成带时间戳的文件名
    /// </summary>
    /// <param name="baseName">基础文件名</param>
    /// <param name="extension">文件扩展名（不包含点）</param>
    /// <param name="directory">保存目录</param>
    /// <returns>完整的文件路径</returns>
    public string GenerateTimestampedFileName(string baseName, string extension = "png", string? directory = null)
    {
        if (string.IsNullOrWhiteSpace(baseName))
            throw new ArgumentException("基础文件名不能为空", nameof(baseName));

        directory ??= Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Screenshots");
        
        var timestamp = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss-fff");
        var fileName = $"{baseName}_{timestamp}.{extension}";
        
        return Path.Combine(directory, fileName);
    }

    /// <summary>
    /// 截图并返回字节数组
    /// </summary>
    /// <param name="window">要截图的窗口</param>
    /// <param name="format">图片格式</param>
    /// <returns>图片字节数组</returns>
    public byte[]? CaptureWindowToBytes(Window window, ImageFormat? format = null)
    {
        if (window == null)
            throw new ArgumentNullException(nameof(window));

        format ??= ImageFormat.Png;

        try
        {
            using var bitmap = Capture.Element(window);
            using var stream = new MemoryStream();
            bitmap.Bitmap.Save(stream, format);
            
            _logger.LogDebug($"成功截取窗口 '{window.Title}' 到字节数组");
            return stream.ToArray();
        }
        catch (Exception ex)
        {
            _logger.LogError($"截取窗口 '{window.Title}' 到字节数组失败: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 截图并返回Base64字符串
    /// </summary>
    /// <param name="element">要截图的元素</param>
    /// <param name="format">图片格式</param>
    /// <returns>Base64编码的图片字符串</returns>
    public string? CaptureElementToBase64(AutomationElement element, ImageFormat? format = null)
    {
        if (element == null)
            throw new ArgumentNullException(nameof(element));

        format ??= ImageFormat.Png;

        try
        {
            using var bitmap = Capture.Element(element);
            using var stream = new MemoryStream();
            bitmap.Bitmap.Save(stream, format);
            
            var base64String = Convert.ToBase64String(stream.ToArray());
            _logger.LogDebug($"成功截取元素 '{element.Name}' 到Base64字符串");
            return base64String;
        }
        catch (Exception ex)
        {
            _logger.LogError($"截取元素 '{element.Name}' 到Base64字符串失败: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 批量截图（用于记录测试过程）
    /// </summary>
    /// <param name="window">要截图的窗口</param>
    /// <param name="baseName">基础文件名</param>
    /// <param name="directory">保存目录</param>
    /// <param name="maxCount">最大截图数量</param>
    /// <returns>保存的文件路径列表</returns>
    public List<string> CaptureBatch(Window window, string baseName, string? directory = null, int maxCount = 10)
    {
        if (window == null)
            throw new ArgumentNullException(nameof(window));
        if (string.IsNullOrWhiteSpace(baseName))
            throw new ArgumentException("基础文件名不能为空", nameof(baseName));

        directory ??= Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Screenshots", "Batch");
        
        var savedFiles = new List<string>();

        try
        {
            // 确保目录存在
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            for (int i = 1; i <= maxCount; i++)
            {
                var fileName = GenerateTimestampedFileName($"{baseName}_{i:D2}", "png", directory);
                
                if (CaptureWindow(window, fileName))
                {
                    savedFiles.Add(fileName);
                    _logger.LogDebug($"批量截图 {i}/{maxCount} 完成");
                }
                else
                {
                    _logger.LogWarning($"批量截图 {i}/{maxCount} 失败");
                }

                // 间隔一段时间
                Thread.Sleep(1000);
            }

            _logger.LogInformation($"批量截图完成，共保存 {savedFiles.Count} 张图片");
        }
        catch (Exception ex)
        {
            _logger.LogError($"批量截图失败: {ex.Message}");
        }

        return savedFiles;
    }

    /// <summary>
    /// 清理旧的截图文件
    /// </summary>
    /// <param name="directory">截图目录</param>
    /// <param name="olderThanDays">删除多少天前的文件</param>
    /// <returns>删除的文件数量</returns>
    public int CleanupOldScreenshots(string directory, int olderThanDays = 7)
    {
        if (string.IsNullOrWhiteSpace(directory) || !Directory.Exists(directory))
        {
            _logger.LogWarning($"截图目录不存在: {directory}");
            return 0;
        }

        var deletedCount = 0;
        var cutoffDate = DateTime.Now.AddDays(-olderThanDays);

        try
        {
            var files = Directory.GetFiles(directory, "*.png", SearchOption.AllDirectories)
                .Concat(Directory.GetFiles(directory, "*.jpg", SearchOption.AllDirectories))
                .Concat(Directory.GetFiles(directory, "*.jpeg", SearchOption.AllDirectories));

            foreach (var file in files)
            {
                var fileInfo = new FileInfo(file);
                if (fileInfo.CreationTime < cutoffDate)
                {
                    File.Delete(file);
                    deletedCount++;
                    _logger.LogDebug($"删除旧截图文件: {file}");
                }
            }

            _logger.LogInformation($"清理完成，删除了 {deletedCount} 个旧截图文件");
        }
        catch (Exception ex)
        {
            _logger.LogError($"清理旧截图文件失败: {ex.Message}");
        }

        return deletedCount;
    }
}
