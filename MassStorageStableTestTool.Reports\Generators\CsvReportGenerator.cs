using MassStorageStableTestTool.Core.Models;
using MassStorageStableTestTool.Core.Enums;
using MassStorageStableTestTool.Reports.Common;
using MassStorageStableTestTool.Reports.Models;
using Microsoft.Extensions.Logging;
using System.Text;
using System.Globalization;

namespace MassStorageStableTestTool.Reports.Generators;

/// <summary>
/// CSV格式报告生成器
/// </summary>
public class CsvReportGenerator : BaseReportGenerator
{
    /// <summary>
    /// 支持的报告格式
    /// </summary>
    public override ReportFormat SupportedFormat => ReportFormat.CSV;

    /// <summary>
    /// 生成器名称
    /// </summary>
    public override string GeneratorName => "CSV Report Generator";

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="configuration">报告配置</param>
    public CsvReportGenerator(ILogger<CsvReportGenerator> logger, ReportGenerationConfiguration? configuration = null)
        : base(logger, configuration)
    {
    }

    /// <summary>
    /// 生成报告内容
    /// </summary>
    /// <param name="reportData">报告数据</param>
    /// <param name="template">模板内容</param>
    /// <returns>报告内容</returns>
    protected override async Task<string> GenerateReportContentAsync(object reportData, string template)
    {
        try
        {
            // 对于CSV格式，我们不使用模板，而是直接生成结构化数据
            return await GenerateCsvContentAsync(reportData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成CSV报告时出错");
            return GenerateSimpleCsvReport();
        }
    }

    /// <summary>
    /// 获取默认文件扩展名
    /// </summary>
    /// <returns>文件扩展名</returns>
    public override string GetDefaultFileExtension() => ".csv";

    /// <summary>
    /// 验证报告内容
    /// </summary>
    /// <param name="reportContent">报告内容</param>
    /// <returns>错误列表</returns>
    protected override List<string> ValidateReportContent(string reportContent)
    {
        var errors = new List<string>();
        var lines = reportContent.Split('\n', StringSplitOptions.RemoveEmptyEntries);

        if (lines.Length < 2)
        {
            errors.Add("CSV报告至少应包含标题行和一行数据");
        }

        // 检查是否有逗号分隔符
        if (lines.Length > 0 && !lines[0].Contains(','))
        {
            errors.Add("CSV报告缺少逗号分隔符");
        }

        return errors;
    }

    /// <summary>
    /// 获取后备模板
    /// </summary>
    /// <returns>后备模板内容</returns>
    protected override string GetFallbackTemplate()
    {
        // CSV格式不使用模板
        return string.Empty;
    }

    /// <summary>
    /// 生成CSV内容
    /// </summary>
    /// <param name="reportData">报告数据</param>
    /// <returns>CSV内容</returns>
    private async Task<string> GenerateCsvContentAsync(object reportData)
    {
        await Task.CompletedTask;
        
        var sb = new StringBuilder();
        
        // 从reportData中提取TestSuiteResult
        var testSuiteResult = ExtractTestSuiteResult(reportData);
        if (testSuiteResult == null)
        {
            return GenerateSimpleCsvReport();
        }

        // 生成概览信息CSV
        sb.AppendLine("# 测试概览");
        sb.AppendLine("属性,值");
        sb.AppendLine($"报告生成时间,{DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        sb.AppendLine($"目标驱动器,{EscapeCsvValue(testSuiteResult.Configuration.TargetDrive)}");
        sb.AppendLine($"测试开始时间,{testSuiteResult.StartTime:yyyy-MM-dd HH:mm:ss}");
        sb.AppendLine($"测试结束时间,{testSuiteResult.EndTime:yyyy-MM-dd HH:mm:ss}");
        sb.AppendLine($"总耗时(分钟),{testSuiteResult.Duration.TotalMinutes:F2}");
        sb.AppendLine($"总测试数,{testSuiteResult.TotalTestsCount}");
        sb.AppendLine($"成功测试数,{testSuiteResult.SuccessfulTestsCount}");
        sb.AppendLine($"失败测试数,{testSuiteResult.FailedTestsCount}");
        sb.AppendLine($"成功率(%),{testSuiteResult.SuccessRate:F1}");
        sb.AppendLine($"整体状态,{testSuiteResult.Status}");
        sb.AppendLine();

        // 生成详细测试结果CSV
        sb.AppendLine("# 详细测试结果");
        sb.AppendLine("工具名称,状态,结果,开始时间,结束时间,耗时(分钟),读取速度(MB/s),写入速度(MB/s),读取IOPS,写入IOPS,读取延迟(ms),写入延迟(ms),错误信息,警告数量");

        foreach (var testResult in testSuiteResult.TestResults)
        {
            var line = new StringBuilder();
            line.Append(EscapeCsvValue(testResult.ToolName));
            line.Append(',').Append(EscapeCsvValue(testResult.Status.ToString()));
            line.Append(',').Append(testResult.Success ? "成功" : "失败");
            line.Append(',').Append(testResult.StartTime.ToString("yyyy-MM-dd HH:mm:ss"));
            line.Append(',').Append(testResult.EndTime.ToString("yyyy-MM-dd HH:mm:ss"));
            line.Append(',').Append(testResult.Duration.TotalMinutes.ToString("F2", CultureInfo.InvariantCulture));
            
            // 性能数据
            if (testResult.Performance != null)
            {
                line.Append(',').Append(testResult.Performance.ReadSpeed?.ToString("F2", CultureInfo.InvariantCulture) ?? "");
                line.Append(',').Append(testResult.Performance.WriteSpeed?.ToString("F2", CultureInfo.InvariantCulture) ?? "");
                line.Append(',').Append(testResult.Performance.ReadIOPS?.ToString("F0", CultureInfo.InvariantCulture) ?? "");
                line.Append(',').Append(testResult.Performance.WriteIOPS?.ToString("F0", CultureInfo.InvariantCulture) ?? "");
                line.Append(',').Append(testResult.Performance.ReadLatency?.ToString("F2", CultureInfo.InvariantCulture) ?? "");
                line.Append(',').Append(testResult.Performance.WriteLatency?.ToString("F2", CultureInfo.InvariantCulture) ?? "");
            }
            else
            {
                line.Append(",,,,,,"); // 空的性能数据列
            }
            
            line.Append(',').Append(EscapeCsvValue(testResult.ErrorMessage ?? ""));
            line.Append(',').Append(testResult.Warnings.Count);
            
            sb.AppendLine(line.ToString());
        }

        // 如果有系统信息，添加系统信息部分
        if (testSuiteResult.SystemInfo != null)
        {
            sb.AppendLine();
            sb.AppendLine("# 系统信息");
            sb.AppendLine("属性,值");
            sb.AppendLine($"操作系统,{EscapeCsvValue(testSuiteResult.SystemInfo.OperatingSystem)}");
            sb.AppendLine($"处理器,{EscapeCsvValue(testSuiteResult.SystemInfo.Processor)}");
            sb.AppendLine($"内存(GB),{testSuiteResult.SystemInfo.TotalMemory:F2}");
        }

        // 如果有驱动器信息，添加驱动器信息部分
        if (testSuiteResult.DriveInfo != null)
        {
            sb.AppendLine();
            sb.AppendLine("# 驱动器信息");
            sb.AppendLine("属性,值");
            sb.AppendLine($"驱动器,{EscapeCsvValue(testSuiteResult.DriveInfo.Name)}");
            sb.AppendLine($"标签,{EscapeCsvValue(testSuiteResult.DriveInfo.Label)}");
            sb.AppendLine($"文件系统,{EscapeCsvValue(testSuiteResult.DriveInfo.FileSystem)}");
            sb.AppendLine($"总容量(GB),{testSuiteResult.DriveInfo.TotalSize / (1024.0 * 1024.0 * 1024.0):F2}");
            sb.AppendLine($"可用空间(GB),{testSuiteResult.DriveInfo.AvailableFreeSpace / (1024.0 * 1024.0 * 1024.0):F2}");
        }

        return sb.ToString();
    }

    /// <summary>
    /// 从报告数据中提取TestSuiteResult
    /// </summary>
    /// <param name="reportData">报告数据</param>
    /// <returns>TestSuiteResult或null</returns>
    private TestSuiteResult? ExtractTestSuiteResult(object reportData)
    {
        try
        {
            // 使用反射从匿名对象中提取TestSuiteResult
            var type = reportData.GetType();
            var testSuiteProperty = type.GetProperty("TestSuite");
            return testSuiteProperty?.GetValue(reportData) as TestSuiteResult;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 转义CSV值
    /// </summary>
    /// <param name="value">原始值</param>
    /// <returns>转义后的值</returns>
    private string EscapeCsvValue(string value)
    {
        if (string.IsNullOrEmpty(value))
            return "";

        // 如果包含逗号、引号或换行符，需要用引号包围并转义内部引号
        if (value.Contains(',') || value.Contains('"') || value.Contains('\n') || value.Contains('\r'))
        {
            return $"\"{value.Replace("\"", "\"\"")}\"";
        }

        return value;
    }

    /// <summary>
    /// 生成简单的CSV报告（当数据提取失败时使用）
    /// </summary>
    /// <returns>简单CSV报告</returns>
    private string GenerateSimpleCsvReport()
    {
        var sb = new StringBuilder();
        sb.AppendLine("# 磁盘稳定性测试报告");
        sb.AppendLine("属性,值");
        sb.AppendLine($"报告生成时间,{DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        sb.AppendLine($"生成器,{GeneratorName}");
        sb.AppendLine("状态,数据提取失败 - 使用简化格式");
        return sb.ToString();
    }
}
