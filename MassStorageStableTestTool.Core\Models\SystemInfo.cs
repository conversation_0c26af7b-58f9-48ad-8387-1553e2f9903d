namespace MassStorageStableTestTool.Core.Models;

/// <summary>
/// 系统信息模型
/// </summary>
public class SystemInfo
{
    /// <summary>
    /// 操作系统名称
    /// </summary>
    public string OperatingSystem { get; set; } = string.Empty;

    /// <summary>
    /// 操作系统版本
    /// </summary>
    public string OSVersion { get; set; } = string.Empty;

    /// <summary>
    /// 操作系统架构
    /// </summary>
    public string Architecture { get; set; } = string.Empty;

    /// <summary>
    /// 处理器名称
    /// </summary>
    public string Processor { get; set; } = string.Empty;

    /// <summary>
    /// 处理器核心数
    /// </summary>
    public int ProcessorCores { get; set; }

    /// <summary>
    /// 处理器逻辑核心数
    /// </summary>
    public int LogicalProcessors { get; set; }

    /// <summary>
    /// 总内存大小（GB）
    /// </summary>
    public double TotalMemory { get; set; }

    /// <summary>
    /// 可用内存大小（GB）
    /// </summary>
    public double AvailableMemory { get; set; }

    /// <summary>
    /// 计算机名称
    /// </summary>
    public string ComputerName { get; set; } = string.Empty;

    /// <summary>
    /// 用户名
    /// </summary>
    public string UserName { get; set; } = string.Empty;

    /// <summary>
    /// 系统启动时间
    /// </summary>
    public DateTime SystemStartTime { get; set; }

    /// <summary>
    /// .NET 运行时版本
    /// </summary>
    public string DotNetVersion { get; set; } = string.Empty;

    /// <summary>
    /// 系统语言
    /// </summary>
    public string SystemLanguage { get; set; } = string.Empty;

    /// <summary>
    /// 时区
    /// </summary>
    public string TimeZone { get; set; } = string.Empty;

    /// <summary>
    /// 屏幕分辨率
    /// </summary>
    public string ScreenResolution { get; set; } = string.Empty;

    /// <summary>
    /// 系统运行时间
    /// </summary>
    public TimeSpan SystemUptime => DateTime.Now - SystemStartTime;

    /// <summary>
    /// 内存使用率
    /// </summary>
    public double MemoryUsagePercentage => TotalMemory > 0 ? (TotalMemory - AvailableMemory) / TotalMemory * 100 : 0;

    /// <summary>
    /// 环境变量
    /// </summary>
    public Dictionary<string, string> EnvironmentVariables { get; set; } = new();

    /// <summary>
    /// 已安装的软件列表
    /// </summary>
    public List<InstalledSoftware> InstalledSoftware { get; set; } = new();

    /// <summary>
    /// 网络适配器信息
    /// </summary>
    public List<NetworkAdapter> NetworkAdapters { get; set; } = new();

    /// <summary>
    /// 转换为字典格式
    /// </summary>
    /// <returns>系统信息字典</returns>
    public Dictionary<string, object> ToDictionary()
    {
        return new Dictionary<string, object>
        {
            ["OperatingSystem"] = OperatingSystem,
            ["OSVersion"] = OSVersion,
            ["Architecture"] = Architecture,
            ["Processor"] = Processor,
            ["ProcessorCores"] = ProcessorCores,
            ["LogicalProcessors"] = LogicalProcessors,
            ["TotalMemory"] = TotalMemory,
            ["AvailableMemory"] = AvailableMemory,
            ["MemoryUsagePercentage"] = MemoryUsagePercentage,
            ["ComputerName"] = ComputerName,
            ["UserName"] = UserName,
            ["SystemStartTime"] = SystemStartTime,
            ["SystemUptime"] = SystemUptime.ToString(),
            ["DotNetVersion"] = DotNetVersion,
            ["SystemLanguage"] = SystemLanguage,
            ["TimeZone"] = TimeZone,
            ["ScreenResolution"] = ScreenResolution
        };
    }

    /// <summary>
    /// 获取系统摘要信息
    /// </summary>
    /// <returns>系统摘要</returns>
    public string GetSummary()
    {
        return $"{OperatingSystem} {OSVersion} ({Architecture}), {Processor}, {TotalMemory:F1}GB RAM, {ProcessorCores} cores";
    }
}

/// <summary>
/// 已安装软件信息
/// </summary>
public class InstalledSoftware
{
    /// <summary>
    /// 软件名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 软件版本
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 发布商
    /// </summary>
    public string Publisher { get; set; } = string.Empty;

    /// <summary>
    /// 安装日期
    /// </summary>
    public DateTime? InstallDate { get; set; }

    /// <summary>
    /// 安装路径
    /// </summary>
    public string InstallPath { get; set; } = string.Empty;
}

/// <summary>
/// 网络适配器信息
/// </summary>
public class NetworkAdapter
{
    /// <summary>
    /// 适配器名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 适配器描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// MAC地址
    /// </summary>
    public string MacAddress { get; set; } = string.Empty;

    /// <summary>
    /// IP地址列表
    /// </summary>
    public List<string> IPAddresses { get; set; } = new();

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }

    /// <summary>
    /// 连接速度（Mbps）
    /// </summary>
    public long Speed { get; set; }

    /// <summary>
    /// 适配器类型
    /// </summary>
    public string AdapterType { get; set; } = string.Empty;
}
