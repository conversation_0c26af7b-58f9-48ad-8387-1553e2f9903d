using FlaUI.Core;
using FlaUI.Core.AutomationElements;
using FlaUI.Core.Definitions;
using FlaUI.Core.Input;
using FlaUI.Core.WindowsAPI;
using FlaUI.UIA3;
using Microsoft.Extensions.Logging;
using System;
using System.Diagnostics;
using System.Threading.Tasks;

namespace MassStorageStableTestTool.Automation.GUI;

/// <summary>
/// 窗口管理器，负责查找、激活和控制应用程序窗口
/// </summary>
public class WindowManager : IDisposable
{
    private readonly ILogger<WindowManager> _logger;
    private readonly UIA3Automation _automation;
    private readonly Dictionary<string, Window> _cachedWindows;
    private bool _disposed = false;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public WindowManager(ILogger<WindowManager> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _automation = new UIA3Automation();
        _cachedWindows = new Dictionary<string, Window>();
    }

    /// <summary>
    /// 根据进程ID查找窗口
    /// </summary>
    /// <param name="processId">进程ID</param>
    /// <param name="timeout">超时时间</param>
    /// <returns>窗口对象</returns>
    public Window? FindWindowByProcessId(int processId, TimeSpan timeout = default)
    {
        if (timeout == default)
            timeout = TimeSpan.FromSeconds(10);

        var endTime = DateTime.Now.Add(timeout);
        
        while (DateTime.Now < endTime)
        {
            try
            {
                var process = Process.GetProcessById(processId);
                if (process.MainWindowHandle != IntPtr.Zero)
                {
                    var window = _automation.FromHandle(process.MainWindowHandle).AsWindow();
                    if (window != null)
                    {
                        _logger.LogDebug($"找到进程 {processId} 的窗口: {window.Title}");
                        return window;
                    }
                }
            }
            catch (ArgumentException)
            {
                // 进程不存在
                _logger.LogWarning($"进程 {processId} 不存在");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"查找进程 {processId} 的窗口时出错: {ex.Message}");
            }

            Thread.Sleep(500);
        }

        _logger.LogError($"在 {timeout.TotalSeconds} 秒内未找到进程 {processId} 的窗口");
        return null;
    }

    /// <summary>
    /// 根据窗口标题查找窗口
    /// </summary>
    /// <param name="title">窗口标题</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="exactMatch">是否精确匹配</param>
    /// <returns>窗口对象</returns>
    public Window? FindWindowByTitle(string title, TimeSpan timeout = default, bool exactMatch = false)
    {
        if (string.IsNullOrWhiteSpace(title))
            throw new ArgumentException("窗口标题不能为空", nameof(title));

        if (timeout == default)
            timeout = TimeSpan.FromSeconds(10);

        var cacheKey = $"title_{title}_{exactMatch}";
        if (_cachedWindows.TryGetValue(cacheKey, out var cachedWindow) && IsWindowValid(cachedWindow))
        {
            return cachedWindow;
        }

        var endTime = DateTime.Now.Add(timeout);
        
        while (DateTime.Now < endTime)
        {
            try
            {
                var desktop = _automation.GetDesktop();
                var windows = desktop.FindAllChildren(cf => cf.ByControlType(ControlType.Window));

                foreach (var window in windows)
                {
                    var windowTitle = window.AsWindow().Title;
                    if (string.IsNullOrEmpty(windowTitle))
                        continue;

                    bool matches = exactMatch 
                        ? windowTitle.Equals(title, StringComparison.OrdinalIgnoreCase)
                        : windowTitle.Contains(title, StringComparison.OrdinalIgnoreCase);

                    if (matches)
                    {
                        var windowElement = window.AsWindow();
                        _cachedWindows[cacheKey] = windowElement;
                        _logger.LogDebug($"找到窗口: {windowTitle}");
                        return windowElement;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"查找窗口 '{title}' 时出错: {ex.Message}");
            }

            Thread.Sleep(500);
        }

        _logger.LogError($"在 {timeout.TotalSeconds} 秒内未找到标题包含 '{title}' 的窗口");
        return null;
    }

    /// <summary>
    /// 根据类名查找窗口
    /// </summary>
    /// <param name="className">窗口类名</param>
    /// <param name="timeout">超时时间</param>
    /// <returns>窗口对象</returns>
    public Window? FindWindowByClassName(string className, TimeSpan timeout = default)
    {
        if (string.IsNullOrWhiteSpace(className))
            throw new ArgumentException("窗口类名不能为空", nameof(className));

        if (timeout == default)
            timeout = TimeSpan.FromSeconds(10);

        var cacheKey = $"class_{className}";
        if (_cachedWindows.TryGetValue(cacheKey, out var cachedWindow) && IsWindowValid(cachedWindow))
        {
            return cachedWindow;
        }

        var endTime = DateTime.Now.Add(timeout);
        
        while (DateTime.Now < endTime)
        {
            try
            {
                var desktop = _automation.GetDesktop();
                var window = desktop.FindFirstChild(cf => cf.ByClassName(className))?.AsWindow();
                
                if (window != null)
                {
                    _cachedWindows[cacheKey] = window;
                    _logger.LogDebug($"找到类名为 '{className}' 的窗口: {window.Title}");
                    return window;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"查找类名为 '{className}' 的窗口时出错: {ex.Message}");
            }

            Thread.Sleep(500);
        }

        _logger.LogError($"在 {timeout.TotalSeconds} 秒内未找到类名为 '{className}' 的窗口");
        return null;
    }

    /// <summary>
    /// 激活窗口
    /// </summary>
    /// <param name="window">窗口对象</param>
    /// <returns>是否成功激活</returns>
    public bool ActivateWindow(Window window)
    {
        if (window == null)
            throw new ArgumentNullException(nameof(window));

        try
        {
            // 检查窗口是否最小化，如果是则恢复
            var windowPattern = window.Patterns.Window.PatternOrDefault;
            if (windowPattern != null && windowPattern.WindowVisualState.Value == WindowVisualState.Minimized)
            {
                windowPattern.SetWindowVisualState(WindowVisualState.Normal);
                Thread.Sleep(500);
            }

            // 激活窗口
            window.Focus();
            window.SetForeground();

            _logger.LogDebug($"成功激活窗口: {window.Title}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError($"激活窗口 '{window.Title}' 失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 关闭窗口 - 使用多种策略确保成功关闭
    /// </summary>
    /// <param name="window">窗口对象</param>
    /// <param name="force">是否强制关闭</param>
    /// <param name="smart">是否使用智能关闭策略（适用于确认对话框等）</param>
    /// <returns>是否成功关闭</returns>
    public async Task<bool> CloseWindow(Window window, bool force = false, bool smart = false)
    {
        if (window == null)
            throw new ArgumentNullException(nameof(window));

        try
        {
            _logger.LogInformation("尝试关闭窗口: {Title} (force: {Force}, smart: {Smart})", window.Title, force, smart);

            // 如果使用智能关闭策略
            if (smart)
            {
                if (!await CloseWindowWithSmartStrategy(window) && !force)
                {
                    return false;
                }

                // 检查窗口是否已关闭
                if (IsWindowClosed(window))
                {
                    return true;
                }
            }

            // 强制关闭
            if (force)
            {
                return await CloseWindowForcefully(window);
            }

            // 标准关闭
            return await CloseWindowStandard(window);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "关闭窗口 '{Title}' 时发生异常", window.Title);
            return false;
        }
    }

    /// <summary>
    /// 等待窗口关闭
    /// </summary>
    /// <param name="window">窗口对象</param>
    /// <param name="timeout">超时时间</param>
    /// <returns>是否在超时时间内关闭</returns>
    public bool WaitForWindowClosed(Window window, TimeSpan timeout)
    {
        if (window == null)
            throw new ArgumentNullException(nameof(window));

        var endTime = DateTime.Now.Add(timeout);
        
        while (DateTime.Now < endTime)
        {
            try
            {
                if (!IsWindowValid(window))
                {
                    _logger.LogDebug($"窗口已关闭: {window.Title}");
                    return true;
                }
            }
            catch
            {
                // 窗口已关闭，访问属性会抛出异常
                return true;
            }

            Thread.Sleep(500);
        }

        _logger.LogWarning($"窗口 '{window.Title}' 在 {timeout.TotalSeconds} 秒内未关闭");
        return false;
    }

    /// <summary>
    /// 检查窗口是否有效
    /// </summary>
    /// <param name="window">窗口对象</param>
    /// <returns>是否有效</returns>
    private bool IsWindowValid(Window window)
    {
        try
        {
            return window != null && window.IsAvailable && !string.IsNullOrEmpty(window.Title);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 清理缓存的窗口
    /// </summary>
    public void ClearCache()
    {
        _cachedWindows.Clear();
        _logger.LogDebug("已清理窗口缓存");
    }

    /// <summary>
    /// 检查窗口是否已关闭
    /// </summary>
    /// <param name="window">要检查的窗口</param>
    /// <returns>窗口是否已关闭</returns>
    public bool IsWindowClosed(Window window)
    {
        if (window == null)
            return true;

        try
        {
            // 方法1：检查窗口句柄是否有效
            if (window.Properties.NativeWindowHandle.IsSupported)
            {
                var handle = window.Properties.NativeWindowHandle.Value;
                if (handle == IntPtr.Zero)
                {
                    return true; // 句柄无效，窗口已关闭
                }
            }

            // 方法2：尝试访问窗口属性
            var _ = window.Title; // 如果窗口已关闭，这里会抛出异常
            var isVisible = window.IsOffscreen == false;
            var isAvailable = window.IsAvailable;

            // 窗口不可见或不可用，认为已关闭
            return !isVisible || !isAvailable;
        }
        catch (Exception)
        {
            // 访问窗口属性时抛出异常，说明窗口已关闭
            return true;
        }
    }

    /// <summary>
    /// 使用智能策略关闭窗口 - 适用于确认对话框等特殊窗口
    /// </summary>
    /// <param name="window">要关闭的窗口</param>
    /// <returns>是否成功关闭</returns>
    private async Task<bool> CloseWindowWithSmartStrategy(Window window)
    {
        if (window == null)
            return true;

        try
        {
            _logger.LogInformation("尝试智能关闭窗口: {Title}", window.Title);

            // 方法1: 尝试点击常见的确认按钮
            var buttonTexts = new[]
            {
                "确定", "OK", "Yes", "是", "Continue", "继续",
                "取消", "Cancel", "No", "否", "Close", "关闭"
            };

            foreach (var buttonText in buttonTexts)
            {
                try
                {
                    var button = window.FindFirstDescendant(cf =>
                        cf.ByControlType(ControlType.Button)
                        .And(cf.ByName(buttonText)));

                    if (button != null)
                    {
                        _logger.LogInformation("找到按钮: {ButtonText}，点击它", buttonText);
                        button.Click();
                        await Task.Delay(500); // 等待窗口关闭

                        // 检查窗口是否已关闭
                        if (IsWindowClosed(window))
                        {
                            _logger.LogInformation("窗口已通过点击按钮关闭");
                            return true;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, "尝试点击按钮 {ButtonText} 失败", buttonText);
                }
            }

            // 方法2: 尝试按 Enter 键
            try
            {
                _logger.LogInformation("尝试按 Enter 键关闭窗口");
                window.Focus();
                await Task.Delay(100);
                Keyboard.Type(VirtualKeyShort.RETURN);
                await Task.Delay(500);

                if (IsWindowClosed(window))
                {
                    _logger.LogInformation("窗口已通过 Enter 键关闭");
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "尝试按 Enter 键失败");
            }

            // 方法3: 尝试按 Escape 键
            try
            {
                _logger.LogInformation("尝试按 Escape 键关闭窗口");
                window.Focus();
                await Task.Delay(100);
                Keyboard.Type(VirtualKeyShort.ESCAPE);
                await Task.Delay(500);

                if (IsWindowClosed(window))
                {
                    _logger.LogInformation("窗口已通过 Escape 键关闭");
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "尝试按 Escape 键失败");
            }

            // 方法4: 使用标准关闭方法
            try
            {
                _logger.LogInformation("尝试使用标准方法关闭窗口");
                var closed = await CloseWindowStandard(window);
                if (closed)
                {
                    await Task.Delay(500);
                    if (IsWindowClosed(window))
                    {
                        _logger.LogInformation("窗口已通过标准方法关闭");
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "使用标准方法关闭窗口失败");
            }

            // 方法5: 强制关闭
            try
            {
                _logger.LogWarning("尝试强制关闭窗口");
                var closed = await CloseWindowForcefully(window);
                if (closed)
                {
                    _logger.LogInformation("窗口已强制关闭");
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "强制关闭窗口失败");
            }

            _logger.LogWarning("所有关闭窗口的方法都失败了");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "智能关闭窗口时发生异常");
            return false;
        }
    }

    /// <summary>
    /// 标准方式关闭窗口
    /// </summary>
    /// <param name="window">要关闭的窗口</param>
    /// <returns>是否成功关闭</returns>
    private async Task<bool> CloseWindowStandard(Window window)
    {
        try
        {
            _logger.LogDebug("使用标准方式关闭窗口: {Title}", window.Title);
            window.Close();
            await Task.Delay(500); // 等待窗口关闭

            if (IsWindowClosed(window))
            {
                _logger.LogDebug("窗口已通过标准方式关闭");
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "标准方式关闭窗口失败");
            return false;
        }
    }

    /// <summary>
    /// 强制关闭窗口
    /// </summary>
    /// <param name="window">要关闭的窗口</param>
    /// <returns>是否成功关闭</returns>
    private async Task<bool> CloseWindowForcefully(Window window)
    {
        try
        {
            _logger.LogWarning("强制关闭窗口: {Title}", window.Title);

            var processId = window.Properties.ProcessId.Value;
            var process = Process.GetProcessById(processId);
            process.Kill();
            process.WaitForExit(5000);

            await Task.Delay(500);
            _logger.LogInformation("窗口已强制关闭");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "强制关闭窗口失败");
            return false;
        }
    }

    /// <summary>
    /// 查找子窗口
    /// </summary>
    /// <param name="parentWindow"></param>
    /// <param name="title"></param>
    /// <param name="timeout"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentNullException"></exception>
    /// <exception cref="ArgumentException"></exception>
    public Window? FindChildWindowByTitleAsync(Window parentWindow, string title, TimeSpan timeout)
    {
        if (parentWindow == null)
            throw new ArgumentNullException(nameof(parentWindow));

        if (string.IsNullOrWhiteSpace(title))
            throw new ArgumentException("窗口标题不能为空", nameof(title));

        var endTime = DateTime.Now.Add(timeout);

        while (DateTime.Now < endTime)
        {
            try
            {
                var childWindow = parentWindow.FindFirstChild(cf => cf.ByControlType(ControlType.Window)
                    .And(cf.ByName(title)))?.AsWindow();

                if (childWindow != null)
                {
                    _logger.LogDebug("找到子窗口: {Title}", childWindow.Title);
                    return childWindow;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"查找子窗口时出错: {ex.Message}");
            }

            Thread.Sleep(500);
        }

        _logger.LogError($"在 {timeout.TotalSeconds} 秒内未找到子窗口 '{title}'");
        return null;
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _cachedWindows.Clear();
            _automation?.Dispose();
            _disposed = true;
        }
    }
}
